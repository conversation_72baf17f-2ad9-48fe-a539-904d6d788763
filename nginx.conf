user www-data;
worker_processes auto;
pid /run/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	keepalive_timeout 65;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;

	server {
        listen 80;
        server_name langnuoibienvandon.com;
        listen 443 ssl;
        ssl_certificate /root/source/carcity/ssl2/cer.crt;
        ssl_certificate_key /root/source/carcity/ssl2/ssl.key;

        client_max_body_size 2500M;

        if ($scheme = http) {
            return 301 https://langnuoibienvandon.com$request_uri;
        }

        # global non-WWW HTTPS handler
        if ($http_host = www.langnuoibienvandon.com){
            return 303 https://langnuoibienvandon.com$request_uri;
        }


        location /template/ {
                alias /root/source/carcity/template/;
                autoindex off;
                access_log off;
                expires max;
                proxy_pass         "http://***********:4000";

        }

        location /files/ {
                alias /root/source/carcity/files/;
                autoindex off;
                access_log off;
                expires max;
                proxy_pass         "http://***********:4000";

        }
                
        # location /v2/ {
        #         alias /var/lib/jenkins/workspace/mypet-api-v2/test_nestjs/;
        #         autoindex off;
        #         access_log off;
        #         expires max;
        #         proxy_pass         "http://***********:3000";

        # }

        #location = /dang-nhap.html {
          #return 301 http://quantri.mypet.vn/dang-nhap.html;
        #}

        location / {
            proxy_pass         "http://***********:4000";
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
	    proxy_set_header X-Real-IP $remote_addr;
        }
    }
}

