# 1. <PERSON><PERSON><PERSON> hình ip, port và database

    ├── www/config/CfApp.js

        protocol: 'https',
        domainRelease: 'langnuoibienvandon.com', // tên miền
        webName: 'MyPet', // tên website
        hostProduct: '**************', // ip product
        postProduct: '4000', // port product
    
        hostDev: 'localhost', // ip môi trường dev
        postDev: '5000', // port môi trường dev
    
        
        dbProduct: { // thông số kết nối db product
          ................................................................
        },
    
        dbDev: { // thông số kết nối db dev
          ................................................................
        },

# 2. <PERSON><PERSON><PERSON> thử ở client với lệnh `nodemon` cần cài đặt package nodemon nếu chưa có

# 3. Ch<PERSON>y thử kế nối db thành công sẽ truy cập với địa địa localhost:5000 (Cổng đã config)

# 4. Cài đặt redis server trên ubuntu server

# 5. Yêu cầu cài pm2

# 6. chạy file ./run.sh trường hợp có lỗi cần kiểm tra phân quyền file sh <https://askubuntu.com/questions/38661/how-do-i-run-sh-scripts>

# 7. Muốn chạy được dạng <https://langnuoibienvandon.com> thì cần config ssl cer, tham khảo server đang chạy hiện tại để copy cấu hình qua, mở file /etc/nginx/nginx.conf để xem
