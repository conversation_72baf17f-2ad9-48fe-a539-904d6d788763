"use strict";
const cron = require("node-cron");
const NotificationModel = require("../../models/NotificationModel");
const BookRoomModel = require("../../models/BookRoomModel");
const SettingAdminModel = require("../../models/SettingAdminModel");

module.exports = function (io) {
    cron.schedule('0 * * * * *', async () => {
        let time = new Date().getTime();
        let settingAD = await SettingAdminModel.MODEL.getSettingAdmin()
        let bookRooms = await BookRoomModel.MODEL.getBookRoomByCondition({
            timeCheckOut:{$gte:time+settingAD.timeMinute*60*1000, $lt:time+(settingAD.timeMinute+1)*60*1000},
            status:1
        });
        bookRooms.forEach(item=>{
            NotificationModel.MODEL.addNewNotification(item.storeManagerId, null,'Thông báo sắp đến giờ trả phòng',10,io,{typeService:2,bookId:item._id})
            NotificationModel.MODEL.addNewNotification(item.userId, null,'Thông báo sắp đến giờ trả phòng',11,io,{typeService:2,bookId:item._id})
        })
    }).start();
};