"use strict";
const cron = require("node-cron");
const NotificationModel = require("../../models/NotificationModel");
const BookSpaModel = require("../../models/BookSpaModel");

module.exports = function (io) {
    cron.schedule('0 * * * * *',async () => {
        let time = new Date().getTime(); // Time hien tai
        let bookSpas = await BookSpaModel.MODEL.getBookSpaByCondition({
            createAt: {
                $lt: time + 60 * 60 * 1000
            },
            status: 0
        }); // Lấy lịch hẹn có trạng thái chờ xác nhận và thời gian tạo nhỏ hơn thời gian hiện tại 1 giờ.

        bookSpas.forEach(item => {
            NotificationModel.MODEL.addNewNotification(item.storeManagerId,null,'Lịch hẹn của bạn đã bị hủy',10,io,{
                typeService: 3,
                bookId: item._id
            })
            NotificationModel.MODEL.addNewNotification(item.userId,null,'Lịch hẹn của bạn không được chấp nhận.',11,io,{
                typeService: 3,
                bookId: item._id
            })
        })

    }).start();
};
