"use strict";
const UserModel = require("../models/UserModel");
const kiemTraTraPhong = require("./module/kiemTraTraPhong");
const kiemTraCoupon = require("./module/kiemTraCoupon");
const cronLichHen = require("./module/cronLichHen");
const cronReport = require("./module/cronReport");
const cronUpdateData = require("./module/cronUpdateData");
const cronResetPointEveryYear = require("./module/cronResetPointEveryYear");

module.exports = async function (io) {
    UserModel.MODEL.chekAccountSystem();
    kiemTraTraPhong(io);
    kiemTraCoupon();
    cronReport();
    cronUpdateData();
    cronResetPointEveryYear();
    // cronLichHen(io); // Tu dong huy don
};
