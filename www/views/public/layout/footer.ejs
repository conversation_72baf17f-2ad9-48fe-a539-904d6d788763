<div class="popup">
    <div class="popup_container">
        <div class="title_popup">
            <PERSON><PERSON><PERSON> danh mục
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup text-center">
            <h6>Bạn muốn xoá danh mục này</h6>
        </div>
        <div class="submit_popup">
            <a href="#" class="back">Quay lại</a>
            <a href="#" class="delete">Xóa</a>
        </div>
    </div>
</div>


<div class="popup">
    <div class="popup_container">
        <div class="title_popup">
            Tính phí vận chuyển
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup text-center">
            <p><PERSON><PERSON> vận chuyển cho đơn hàng này là 120.000 vnd. Bạn muốn tính phí vận chuyển hay miên phí cho đơn hàng này?</p>
        </div>
        <div class="submit_popup">
            <a href="#" class="back">Miễn phí</a>
            <a href="#" class="delete">Tính phí</a>
        </div>
    </div>
</div>

</div>
<audio id="newMessage" controls loop style="display: none;">
    <source src="/template/newmessage.mp3" type="audio/mpeg">
</audio>

<!-- Javascript -->
<script type="text/javascript" src="/template/ui/js/main.js?v=<%- cacheVersion %>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
<script src="/template/tagsinput/jquery.tagsinput.min.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/jquery.datetimepicker.full.min.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/image_dropbox.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/jquery.blockUI.js?v=<%- cacheVersion %>"></script>
<script>
    $(() => {
        if (userLogin) {
            socket.on(userLogin._id, (data) => {
                if (data.type == 'change-count-message') {
                    $('#number-message').text(data.countMessage);
                    $('#number-message').show();
                } else if (data.type == 'notify-change-count') {
                    newMessageMp3()
                    $('#number-notification').text(data.notifications);
                    $('#number-notification').show();
                } else if (data.type == 'notify-bill-return-change-count') {
                    newMessageMp3()
                    $('#number-notification-bill-return').text(data.notificationsBillReturn);
                    $('#number-notification-bill-return').show();
                }
            })
        }
    })
</script>
<script src="/template/emoji-picker/lib/js/config.js?v=<%- cacheVersion %>"></script>
<script src="/template/emoji-picker/lib/js/util.js?v=<%- cacheVersion %>"></script>
<script src="/template/emoji-picker/lib/js/jquery.emojiarea.js?v=<%- cacheVersion %>"></script>
<script src="/template/emoji-picker/lib/js/emoji-picker.js?v=<%- cacheVersion %>"></script>
<script>
    $(function () {
        EmojiPicker.prototype.discover = function() {
            return $(this.options.emojiable_selector).emojiarea($.extend({
                emojiPopup: this,
                norealTime: true
            }, this.options));
        };
        window.emojiPicker = new EmojiPicker({
            emojiable_selector: '[data-emojiable=true]',
            assetsPath: '/template/emoji-picker/lib/img/',
            popupButtonClasses: 'fa fa-smile-o'
        });
        window.emojiPicker.discover();

    })
</script>
</body>

</html>
