<!DOCTYPE html>
<!--[if IE 8]>
<html class="ie" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US"> <![endif]-->
<!--[if (gte IE 9)|!(IE)]><!-->
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-US" lang="en-US">
  <!--<![endif]-->

  <head>
    <title><%- render.title %></title>
    <link rel="icon" type="image/png" href="/template/ui/img/logo.png" />

    <link
      rel="stylesheet"
      type="text/css"
      href="/template/ui/css/bootstrap.css?v=<%- cacheVersion %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/template/ui/css/style.css?v=<%- cacheVersion %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/template/ui/css/responsive.css?v=<%- cacheVersion %>"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/template/custom.css?v=<%- cacheVersion %>"
    />
    <link
      rel="stylesheet"
      href="/template/sweetalert2.css?v=<%- cacheVersion %>"
    />
    <link
      rel="stylesheet"
      href="//stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/7.33.1/sweetalert2.min.js"></script>
    <link
      href="/template/select2.min.css?v=<%- cacheVersion %>"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/template/tagsinput/jquery.tagsinput.min.css?v=<%- cacheVersion %>"
    />
    <script src="/template/ckeditor/ckeditor.js?v=<%- cacheVersion %>"></script>
    <script
      type="text/javascript"
      src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.1.1/socket.io.js?v=<%- cacheVersion %>"
    ></script>
    <link
      rel="stylesheet"
      href="https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css?v=<%- cacheVersion %>"
    />
    <link
      href="/template/emoji-picker/lib/css/emoji.css?v=<%- cacheVersion %>"
      rel="stylesheet"
    />
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCeYUnODV7lrPa7bTAmyGjzebneFCQsFXQ&libraries=places"></script>
    <script
      type="text/javascript"
      src="/template/ui/js/jquery.min.js?v=<%- cacheVersion %>"
    ></script>
    <script
      type="text/javascript"
      src="/template/app.js?v=<%- cacheVersion %>"
    ></script>
    <script
      type="text/javascript"
      src="/template/base.form.submit.js?v=<%- cacheVersion %>"
    ></script>
    <script src="/template/moment.js?v=<%- cacheVersion %>"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="/template/datetimepicker/jquery.datetimepicker.css"
    />
    <link
      rel="stylesheet"
      href="/template/select2-themes/select2-bootstrap4.css"
    />
    <link
      rel="stylesheet"
      href="/template/fontawesome/css/font-awesome.min.css"
    />
    <!--    <link rel="stylesheet" href="/template/dropzone/dist/dropzone.css">-->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/4.3.0/dropzone.css"
    />
    <script
      type="text/javascript"
      src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/4.3.0/dropzone.js"
    ></script>
    <script src="/template/jquery.geocomplete.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fetch/2.0.3/fetch.js"></script>
    <script src="https://unpkg.com/dropbox@6.0.2/dist/Dropbox-sdk.min.js"></script>
    <script src="https://www.promisejs.org/polyfills/promise-7.0.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui/1.11.2/jquery-ui.js"></script>
  </head>

  <body>
    <script>
      var userLogin = <%- JSON.stringify(userLogin) %>;
      var baseUrl = window.location.protocol + "//" + window.location.host;
      var socket = io.connect(baseUrl, {secure: true});
    </script>
    <div>
      <div id="preloader">
        <div class="row loader">
          <div class="loader-icon"></div>
        </div>
      </div>

      <header class="header">
        <div class="container">
          <div class="row">
            <div class="col col-md-12">
              <div class="header_wrap">
                <div class="logo">
                  <a href="/">
                    <img src="/template/ui/img/logo.png" alt="Logo" />
                  </a>
                </div>
                <div class="nav-wrap">
                  <nav id="mainnav" class="mainnav">
                    <ul class="menu">
                      <% if (userLogin.type == 0){ %> <% include
                      ./admin-menu.ejs %> <% } else if (userLogin.type == 1) {
                      %> <% include ./store-menu.ejs %> <% } %>
                    </ul>
                  </nav>
                </div>
                <div class="box_account_info">
                  <div class="name_account">
                    <% let image = userLogin.picture; const arr_str =
                    image.split("files/"); image = 'files/' + arr_str[1]; %>
                    <a href="#"
                      ><img
                        src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(image) %>"
                        style="border-radius: 50%; height: 40px"
                      />
                      <%- userLogin.fullName %></a
                    >
                    <ul class="menu_account_info">
                      <% if(userLogin.type == 1){ %>
                      <li>
                        <a href="javascript:;"
                          >Mã tài khoản: MPU<%- userLogin.code || 'OLD DB' %></a
                        >
                      </li>
                      <% } %> <% if(userLogin.type == 1){ %>
                      <li>
                        <a href="/store/quan-ly-doanh-thu.html"
                          >> Quản lý doanh thu</a
                        >
                      </li>
                      <li>
                        <a href="/store/quan-ly-coupon.html"
                          >> Quản lý coupon</a
                        >
                      </li>

                      <% } %> <% if(userLogin.type == 0){ %>
                      <li
                        class="<%- url.includes('/admin/quan-ly-danh-muc.html') ? 'active' : '' %>"
                      >
                        <a href="/admin/quan-ly-danh-muc.html">> Danh mục</a>
                      </li>
                      <li
                        class="<%- url.includes('/admin/quan-ly-tin-tuc.html') ? 'active' : '' %>"
                      >
                        <a href="/admin/quan-ly-tin-tuc.html">> Tin tức</a>
                      </li>
                      <li>
                        <a href="/admin/quan-ly-banner-app.html"
                          >> Quản lý Banner App</a
                        >
                      </li>
                      <li>
                        <a href="/admin/quan-ly-banner-store.html"
                          >> Quản lý Banner Store</a
                        >
                      </li>
                      <li>
                        <a href="/admin/promotions.html"
                          >> Quản lý CT Khuyến mãi</a
                        >
                      </li>
                      <li>
                        <a href="/admin/thong-ke-du-lieu.html"
                          >> Thống kê hệ thống</a
                        >
                      </li>
                      <li>
                        <a href="/admin/quan-ly-don-hang-doi-qua.html"
                          >> Quản lý đơn hàng đổi quà</a
                        >
                      </li>
                      <li>
                        <a href="/admin/quan-ly-doanh-thu.html"
                          >> Quản lý doanh thu</a
                        >
                      </li>
                      <li>
                        <a href="/admin/thanh-toan-online.html"
                          >> Quản lý thanh toán online</a
                        >
                      </li>
                      <li>
                        <a href="/admin/quan-ly-coupon.html"
                          >> Quản lý coupon</a
                        >
                      </li>
                      <li>
                        <a href="/admin/quan-ly-thong-bao-da-gui.html"
                          >> Gửi thông báo</a
                        >
                      </li>
                      <li>
                        <a href="/admin/setting-admin.html"
                          >> Cài đặt hệ thống</a
                        >
                      </li>
                      <li>
                        <a href="/admin/setting-ads.html"
                          >> Cài đặt Quảng cáo</a
                        >
                      </li>
                      <li>
                        <a href="/admin/ho-tro.html">> Hỗ trợ</a>
                      </li>

                      <% } %>
                      <li>
                        <a href="/thong-tin-tai-khoan.html"
                          >> Thông tin tài khoản</a
                        >
                      </li>
                      <li>
                        <a href="/logout.html"
                          ><img src="/template/ui/img/quit.svg" alt="" /> Đăng
                          xuất</a
                        >
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
    </div>
  </body>
</html>
