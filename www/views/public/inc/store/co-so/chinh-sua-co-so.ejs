<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1>
                            <a onclick="goBack()">
                                <img src="/template/ui/img/arrow-left.png" alt="" />
                            </a>
                            Chỉnh sửa cơ sở
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form id="form-add-service" onsubmit="return false">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Tên cơ sở</label>
                                        <input type="text"
                                                name="name"
                                                value="<%- branch.name %>"
                                                placeholder="Nhập tên c<PERSON> sở"
                                        />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Đ<PERSON>a chỉ</label>
                                        <input
                                                type="text"
                                                name="address"
                                                id="address"
                                                value="<%- branch.address %>"
                                                placeholder="Nhập địa chỉ"
                                        />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Điện thoại</label>
                                        <input
                                                type="number"
                                                name="phone"
                                                id="phone"
                                                value="<%- branch.phone %>"
                                                placeholder="Nhập số điện thoại"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Tỉnh/Thành phố</label>
                                        <div class="select_form">
                                            <select id="city-state" class="" name="province">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Quận/Huyện</label>
                                        <div class="select_form">
                                            <select id="district-state" class="" name="district">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Phường/Xã</label>
                                        <div class="select_form">
                                            <select id="ward-state" class="" name="ward">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Thương hiệu</label>
                                        <div class="select_form">
                                            <select class="" name="storeId">
                                                <option value="null">[[---------Chọn thương hiệu --------]]</option>
                                                <% brands.forEach(item=>{ %>
                                                    <option value="<%- item._id %>" <%- branch.storeId == item._id ? 'selected' : '' %>><%- item.name %> </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Giờ mở cửa</label>
                                        <select class="" name="timeOpen">
                                            <option value="null">[[---------Chọn giờ mở cửa --------]]</option>
                                            <% dateTimes.forEach(item=>{ %>
                                                <option value="<%- item %>" <%- branch.timeOpen == item ? 'selected' : '' %>><%- item %></option>
                                            <% }) %>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Giờ đóng cửa</label>
                                        <select class="" name="timeClose">
                                            <option value="null">[[---------Chọn giờ đóng cửa --------]]</option>
                                            <% dateTimes.forEach(item=>{ %>
                                                <option value="<%- item %>" <%- branch.timeClose == item ? 'selected' : '' %>><%- item %></option>
                                            <% }) %>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Kinh độ (Tự động lấy khi gõ địa chỉ hoặc chủ động nhập)</label>
                                        <input name="lat" type="text" value="<%- branch.lat %>" placeholder="21.028511">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Vĩ độ (Tự động lấy khi gõ địa chỉ hoặc chủ động nhập)</label>
                                        <input name="lng" type="text" value="<%- branch.lng %>" placeholder="105.804817">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="input_field schedule_field">
                                        <label for="">Hotline (Số SOS cho cơ sở có dịch vụ cứu hộ)</label>
                                        <input name="hotline" type="text" value="<%- branch.hotline %>" placeholder="">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Giới thiệu</label>
                                        <textarea id="content" placeholder="Nhập ghi chú" style="height: 200px;"><%- branch.content %></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="button_submit">
                                <a onclick="goBack()">Quay lại</a>
                                <button type="submit" name="save">Lưu</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    let branchId = '<%- branch._id %>';
    let branch = '<%- JSON.stringify(branch) %>';
    let province = '<%- branch.province %>';
    let district = '<%- branch.district %>';
    let ward = '<%- branch.ward %>';

    console.log(branch)

    function goBack() {
        window.history.back();
    }
</script>
<script type="text/javascript" src="/template/countries.js"></script>
<script type="text/javascript" src="/template/store/co-so/chinh-sua-co-so.js?v=<%- cacheVersion %>"></script>
