<style>
    .upfile label::before {
        content: unset !important;
    }
</style>
<script>
    const PET_TYPES = {
        DOG: 'dog',
        CAT: 'cat',
        OTHER: 'other',
    }
</script>
<% const type = [
    {id: 0, name: '<PERSON><PERSON><PERSON> cho Ô tô'},
    {id: 1, name: '<PERSON><PERSON><PERSON> cho M<PERSON>'},
    {id: 2, name: '<PERSON>à<PERSON> cho Khác'},
];
let image = dataService.thumbnail;
image = image.indexOf('/') == 0 ? image.substring(1) : image;

%>
<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1 style="flex: 1;">
                            <% if(userLogin.type == 1){ %>
                                <a href="/store/thuong-hieu.html">
                                    <img src="/template/ui/img/arrow-left.png" alt="">
                                </a>
                            <% }else{ %>
                                <a href="/admin/shop-trung-tam.html">
                                    <img src="/template/ui/img/arrow-left.png" alt="">
                                </a>
                            <% } %>
                            Chỉnh sửa th<PERSON>ơng hiệu
                        </h1>
                        <% if(userLogin.type == 0){ %>
                            <a href="/admin/chinh-sua-yeu-cau/<%- dataService.userId %>.html">
                                Tài khoản bán hàng
                            </a>
                        <% } %>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form id="form-add-service" onsubmit="return false">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Tên thương hiệu</label>
                                        <label for="" class="is_featured">Nổi bật <input type="checkbox"
                                                                                         name="isFeatured" <%- dataService.isFeatured ? 'checked' : '' %> <%- userLogin.type != 0 ? 'disabled' : '' %>></label>
                                        <input type="text" name="name" value="<%- dataService.name %>"
                                               placeholder="Nhập tên thương hiệu">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tỉnh/Thành phố</label>
                                        <div class="select_form">
                                            <select id="city-state" class=""
                                                    name="province" <%- (typeof dataService != 'undefined') ? 'value="' + dataService.province + '"' : '' %>>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Quận/Huyện</label>
                                        <div class="select_form">
                                            <select id="district-state" class=""
                                                    name="district" <%- (typeof dataService != 'undefined') ? 'value="' + dataService.district + '"' : '' %>>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Phường/Xã</label>
                                        <div class="select_form">
                                            <select id="ward-state" class=""
                                                    name="ward" <%- (typeof dataService != 'undefined') ? 'value="' + dataService.ward + '"' : '' %>>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Đường/Phố</label>
                                        <input type="text" name="street" placeholder="Nhập đường / phố"
                                               value="<%- dataService.street %>">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Địa chỉ chi tiết</label>
                                        <input type="text" name="location" value="<%- dataService.location %>"
                                               placeholder="Nhập địa chỉ">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giờ mở cửa</label>
                                        <select class="" name="timeOpen">
                                            <option value="null">[[---------Chọn giờ mở cửa --------]]</option>
                                            <% dateTimes.forEach(item=>{ %>
                                                <option value="<%- item %>" <%- dataService.timeOpen == item ? 'selected' : '' %>><%- item %></option>
                                            <% }) %>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giờ đóng cửa</label>
                                        <select class="" name="timeClose">
                                            <option value="null">[[---------Chọn giờ đóng cửa --------]]</option>
                                            <% dateTimes.forEach(item=>{ %>
                                                <option value="<%- item %>" <%- dataService.timeClose == item ? 'selected' : '' %>><%- item %></option>
                                            <% }) %>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Thời gian phân biệt nửa ngày - Để tính ngày cho điểm gửi xe.</label>
                                        <select class="" name="betweenTime">
                                            <option value="null">[[---------Thời gian phân biệt nửa ngày--------]]
                                            </option>
                                            <% dateTimes.forEach(item=>{ %>
                                                <option value="<%- item %>" <%- dataService.betweenTime == item ? 'selected' : '' %>><%- item %></option>
                                            <% }) %>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <% if(userLogin.type == 0){ %>
                                <!-- Admin-->
                                <div class="row">
                                    <br>
                                    <div class="col-md-12"><h4>Cấu hình giảm giá</h4></div>
                                    <div class="col-md-12">
                                        <div class="input_field schedule_field">
                                            <label for="">Loại hình giảm giá</label>
                                            <div class="checkbox-group">
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" name="saleServices" type="checkbox"
                                                           id="cbSpa2"
                                                           value="3" <%- dataService.saleServices && dataService.saleServices.includes('3') ? 'checked="checked"' : '' %> />
                                                    <label class="form-check-label" for="cbSpa2">Spa</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" name="saleServices" type="checkbox"
                                                           id="cbHotel2"
                                                           value="2" <%- dataService.saleServices && dataService.saleServices.includes('2') ? 'checked="checked"' : '' %> />
                                                    <label class="form-check-label" for="cbHotel2">Khách sạn</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" name="saleServices" type="checkbox"
                                                           id="cbClinic2"
                                                           value="1" <%- dataService.saleServices && dataService.saleServices.includes('1') ? 'checked="checked"' : '' %> />
                                                    <label class="form-check-label" for="cbClinic2">Khám bệnh</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" name="saleServices" type="checkbox"
                                                           id="cbShop2"
                                                           value="0" <%- dataService.saleServices && dataService.saleServices.includes('0') ? 'checked="checked"' : '' %> />
                                                    <label class="form-check-label" for="cbShop2">Sản phẩm</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input_field schedule_field">
                                            <label for="">Tên chương trình</label>
                                            <input type="text" name="saleName" value="<%- dataService.saleName %>"
                                                   placeholder="Nhập tên chương trình">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input_field schedule_field">
                                            <label for="">Giá trị giảm giá</label>
                                            <input type="text" name="saleValue" value="<%- dataService.saleValue %>"
                                                   placeholder="Nhập số tiền hoặc %">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input_field schedule_field">
                                            <label for="">Thời gian kết thúc</label>
                                            <input type="text" name="saleDate" class="dateTimePicker" data-date="<%- dataService.saleDate %>" value="<%- dataService.saleDate ? convertDateShow1(dataService.saleDate) : '' %>"
                                                   placeholder="Nhập thời gian kết thúc">
                                        </div>
                                    </div>
                                </div>
                            <% } %>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Giới thiệu</label>
                                        <textarea id="content"
                                                  placeholder="Nhập ghi chú"><%- dataService.content %></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input_field schedule_field upload_img_product">
                                        <label for="">Ảnh đại diện</label>
                                        <div class="upload_container img_cover">
                                            <div class="upfile">
                                                <label for="">
                                                    <button class="delete-image"
                                                            style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">
                                                        Xóa
                                                    </button>
                                                    <img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(image) %>" alt="">
                                                </label>
                                                <input type="file" name="myfile_1" id="upload-thumbnail"
                                                       accept="image/x-png,image/gif,image/jpeg"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-9">

                                </div>
                            </div>
                        </form>

                        <section>
                            <div id="dropzone">
                                <form class="dropzone needsclick" id="upload-images" max-files="4" action="/">
                                    <div class="dz-message needsclick">
                                        Kéo ảnh thả ảnh vào hoặc<span
                                                class="btn btn-link">ấn vào đây để tải ảnh</span></span>.
                                        <span class="note needsclick">( Kích thước tối đa <strong>3MB</strong>)</span>
                                    </div>
                                </form>
                            </div>
                        </section>
                        <div class="button_submit button_submit_form">
                            <% if(userLogin.type == 1){ %>
                                <a href="/store/thuong-hieu.html">Quay lại</a>
                            <% }else{ %>
                                <a href="/admin/shop-trung-tam.html">Quay lại</a>
                            <% } %>
                            <button onclick="submitForm()" name="save">Lưu</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    let pictureOlds = <%- JSON.stringify(dataService.pictures) %>;
    let dataService = <%- JSON.stringify(dataService) %>;
    let dataServiceId = '<%- dataService._id %>';

</script>
<script src="/template/datetimepicker/jquery.datetimepicker.full.min.js"></script>
<script type="text/javascript" src="/template/countries.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/handle-price.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/store/brand-edit.js?v=<%- cacheVersion %>"></script>
