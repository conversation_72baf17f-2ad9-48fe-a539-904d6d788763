<style>
    button.swal2-styled {
        line-height: unset;
        width: unset;
    }

    .swal2-modal {
        width: auto
    }

    /* Styles cho hiển thị món ăn */
    .meal-item-container {
        display: flex;
        gap: 15px;
        align-items: flex-start;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .meal-image img {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #ddd;
    }

    .meal-info {
        flex: 1;
    }

    .meal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .meal-name {
        font-weight: bold;
        color: #333;
        margin: 0;
    }

    .meal-price {
        color: #e74c3c;
        font-weight: bold;
    }

    .meal-description {
        margin-bottom: 10px;
        color: #666;
        font-style: italic;
    }

    .meal-details {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .meal-details li {
        display: flex;
        justify-content: space-between;
        padding: 4px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .meal-details li:last-child {
        border-bottom: none;
    }

    .meal-details label {
        color: #666;
        margin: 0;
    }
</style>
<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a href="/store/quan-ly-lich-hen.html"><img src="/template/ui/img/arrow-left.png"
                                                                        alt=""></a> Chi tiết lịch hẹn
                        </h1>
                        <div class="status">
                            <span class="<%- dataBook.status == 0 ? 'wait' : dataBook.status == 1 ? 'delivery_product' : dataBook.status == 2 ? 'cancle' : 'deliveried_product' %>"><%- dataBook.status == 0 ? 'Chờ duyệt' : dataBook.status == 1 ? 'Chờ làm dịch vụ' : dataBook.status == 2 ? 'Thất bại' : "Hoàn thành" %></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="booking-detail">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <% if(dataBook.status == 2){ %>
                        <div class="status_order_product rounded box-shadow" style="background: #AAAAAA">
                        <span class="status_order">
                            <strong><%- 'Lịch đặt không thành công với nội dung: ' + dataBook.messageReject %></strong>
                        </span>
                        </div>
                    <% } %>
                </div>
            </div>
            <div class="row row-eq-height">
                <div class="col-md-4">
                    <div class="my-3 p-3 bg-white rounded box-shadow">
                        <div class="col-inner">
                            <div class="box-info">
                                <h5>Thông tin khách hàng</h5>
                            </div>
                            <div class="box-content">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <label>Tên KH</label>
                                        <div class="pull-right list-value"><%- dataBook.fullName || dataBook.userName || 'N/A' %></div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Email</label>
                                        <div class="pull-right list-value"><%- dataBook.email || 'N/A' %></div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Số điện thoại</label>
                                        <div class="pull-right list-value"><%- dataBook.phone %></div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Email</label>
                                        <div class="pull-right list-value"><%- dataBook.email %></div>
                                    </li>

                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="my-3 p-3 bg-white rounded box-shadow">
                        <div class="col-inner">
                            <div class="box-info">
                                <h5>Thông tin dịch vụ</h5>
                            </div>
                            <div class="box-content">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <label>Mã đơn</label>
                                        <div class="pull-right list-value">#<%- dataBook.orderId %></div>
                                    </li>
                                    <% if (dataBook.service){ %>
                                        <li class="list-group-item">
                                            <label>Dịch vụ</label>
                                            <div class="pull-right list-value"><%- dataBook.service %></div>
                                        </li>
                                        <li class="list-group-item">
                                            <label>Cân nặng</label>
                                            <div class="pull-right list-value"><%- dataBook.weight %></div>
                                        </li>
                                        <li class="list-group-item">
                                            <label>Thời gian đến</label>
                                            <div class="pull-right list-value"><%- convertDateShow1(dataBook.timeCheckIn) %></div>
                                        </li>
                                    <% }else if(dataBook.items){ %>
                                        <li class="list-group-item">
                                            <label>Món ăn đã đặt:</label>
                                        </li>
                                        <% dataBook.items.forEach(item=>{ %>
                                            <li class="list-group-item sv">
                                                <div class="meal-item-container">
                                                    <% if (item.image) { %>
                                                        <div class="meal-image">
                                                            <img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- item.image %>" alt="<%- item.serviceName %>">
                                                        </div>
                                                    <% } %>
                                                    <div class="meal-info">
                                                        <div class="meal-header">
                                                            <label class="meal-name"><%- item.serviceName %></label>
                                                            <div class="meal-price"><%- priceFormat(item.price) %>VNĐ</div>
                                                        </div>
                                                        <% if (item.description || item.shortDes) { %>
                                                            <div class="meal-description">
                                                                <small><%- item.description || item.shortDes %></small>
                                                            </div>
                                                        <% } %>
                                                    </div>
                                                </div>
                                                <ul class="meal-details">
                                                    <li>
                                                        <label>Số lượng</label>
                                                        <div><%- item.quantity || 1 %></div>
                                                    </li>
                                                    <li>
                                                        <label>Số khách</label>
                                                        <div><%- item.numberOfGuests %></div>
                                                    </li>
                                                    <li>
                                                        <label>Thời gian đến</label>
                                                        <div><%- item.reservationDate %> <%- item.reservationTime %></div>
                                                    </li>
                                                    <% if (item.timeCheckOut) { %>
                                                        <li>
                                                            <label>Thời gian đi</label>
                                                            <div class="pull-right list-value"><%- convertDateShow1(item.timeCheckOut) %></div>
                                                        </li>
                                                   <% }%>
                                                    <li>
                                                        <label>Chi tiết</label>
                                                        <div class="pull-right list-value"><%- item.weight %></div>
                                                    </li>
                                                </ul>
                                            </li>
                                        <% }) %>
                                    <% } %>

                                    <li class="list-group-item">
                                        <label>Mã giảm giá</label>
                                        <% if (dataBook.coupon){ %>
                                            <div class="pull-right list-value"><span
                                                        class="badge badge-primary"><%- dataBook.coupon %></span></div>
                                        <% } else { %>
                                            <div class="pull-right list-value"><span class="badge badge-secondary">Không sử dụng</span>
                                            </div>
                                        <% } %>

                                    </li>
                                    <li class="list-group-item">
                                        <label>Tổng thanh toán</label>
                                        <div class="pull-right list-value"><%- priceFormat(dataBook.price) %> VNĐ</div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Phụ thu</label>
                                        <div class="pull-right list-value"><%- dataBook.priceAddition ? priceFormat(dataBook.priceAddition) : '0' %>
                                            VNĐ
                                        </div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Ghi chú</label>
                                        <div class="pull-right list-value"><%- dataBook.note %></div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="col-md-4">
                    <div class="my-3 p-3 bg-white rounded box-shadow">
                        <div class="col-inner">
                            <div class="box-info">
                                <h5>Thông tin thanh toán</h5>
                            </div>
                            <div class="box-content">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <label>Hình thức thanh toán</label>
                                        <div class="pull-right list-value"><span
                                                    class="badge badge-info"><%- dataBook.paymentMethod == 0 ? 'Tiền mặt' : 'ATM Online' %></span>
                                        </div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Trạng thái thanh toán</label>
                                        <div class="pull-right list-value"><%- dataBook.isPayOnline == 1 ? '<span class="badge badge-success">Đã thanh toán</span>' : '<span class="badge badge-danger">Chưa thanh toán</span>' %></div>
                                    </li>
                                </ul>
                                <% if (dataBook.paymentMethod == 1 && dataBook.isPayOnline == 0){ %>
                                    <div class="payment-note">
                                        Đơn hàng này có hình thức thanh toán là ATM Online nhưng thanh toán không thành
                                        công.
                                        Bạn có thể từ chối hoặc tiếp nhận dịch vụ và yêu cầu khách hàng thanh toán tiền
                                        mặt.
                                    </div>
                                <% } %>
                            </div>
                            <div class="box-info">
                                <h5>Cơ sở làm dịch vụ</h5>
                            </div>
                            <div class="box-content">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <label>Thương hiệu</label>
                                        <div class="pull-right list-value"><%- dataBook.storeName %></div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Tên cơ sở</label>
                                        <div class="pull-right list-value"><%- dataBook.branchName %></div>
                                    </li>
                                    <li class="list-group-item">
                                        <label>Địa chỉ cơ sở</label>
                                        <div class="pull-right list-value"><%- dataBook.branchAddress %></div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">

                    <div class="button_submit text-center">
                        <% if ([0, 1].includes(dataBook.status)) { %>
                            <a onclick="cancelScheduleViewBox(this)"
                               href="javascript:;"
                               class="confirmed_schedule_form">
                                Từ chối lịch hẹn
                            </a>

                        <% } %>
                        <% if (dataBook.status == 0){ %>
                            <a onclick="successSchedule(this)"
                               url="/store/change-status-schedule/<%- dataBook._id %>.html?status=1&index=<%- dataBook.type %>"
                               href="javascript:;"
                               class=<%- dataBook.status == 1 ? 'confirmed_schedule_form' : 'confirm_schedule_form' %>>
                                Đồng ý lịch hẹn
                            </a>
                        <% } %>

                        <% if (dataBook.status == 1){ %>
                            <a onclick="successDoneScheduleViewBox(this)" href="javascript:;"
                               class="confirm_schedule_form">Hoàn thành dịch vụ</a>
                        <% } %>
                        <% if ([1, 3].includes(dataBook.status)) { %>
                            <% if (dataBook.isPayOnline == 0){ %>
                                <a href="javascript:;" onclick="xacNhanThanhToan()" class="payment">
                                    Xác nhận đã thanh toán
                                </a>
                            <% } %>
                        <% } %>

                    </div>
                </div>
            </div>
        </div>
    </section>
</div>


<div class="popup" id="cancelScheduleViewBox">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Lý do từ chối lịch hẹn
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup">
            <div class="info_confirm">
                <div class="text_info_confirm">
                    <p>Để giúp nguời dùng cải thiện thao tác đặt hàng hoặc thay đổi đơn hàng phù hợp với tiêu chí của
                        của hàng</p>
                </div>
                <div class="icon"><img src="/template/ui/img/check.svg" alt=""></div>
            </div>

            <div class="info_confirm">
                <textarea id="lyDoTuChoi" style="width: 100%; height: 100px;"></textarea>

            </div>
        </div>
        <div class="submit_popup text-right">
            <a href="javascript:;" onclick="guiLenhTuChoi()" class="confirm">Từ chối lịch hẹn</a>
        </div>
    </div>
</div>


<div class="popup" id="successDoneScheduleViewBox">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Chi phí dịch vụ lịch hẹn
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup">
            <div class="info_confirm">
                <div class="text_info_confirm">
                    <p>- Bạn vui lòng khai báo phần dịch vụ</p>
                    <p>- Hệ thống lưu giữ chi phí để giúp bạn quản lý doanh thu hệ thống, và cải tiến hơn giúp doanh thu
                        của bạn được giữ ở mức tốt</p>
                </div>
                <div class="icon"><img src="/template/ui/img/check.svg" alt=""></div>
            </div>

            <div class="info_confirm">
                <label for="">Giá dịch vụ:</label>
                <input id="gia" style="width: 100%;" type="text" name="currency-field" value="<%- dataBook.price %>"
                       disabled data-type="currency">
            </div>
            <div class="info_confirm">
                <label for="">Phụ thu:</label>
                <input id="priceAddition" style="width: 100%;" type="text" name="currency-field" value="0"
                       data-type="currency">
            </div>
        </div>
        <div class="submit_popup text-right">
            <a href="javascript:;" onclick="xacNhanHoanThanh()" class="confirm">Hoàn thành lịch hẹn</a>
        </div>
    </div>
</div>
<script>
    var dataBook = <%- JSON.stringify(dataBook) %>
</script>
<script type="text/javascript" src="/template/store/chi-tiet-lich-hen.js?v=<%- cacheVersion %>"></script>
