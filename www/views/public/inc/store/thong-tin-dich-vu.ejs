<div class="content_boxed">
	<% if(userLogin.servicesParking == 0){ %>
		<section class="page_title">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="page_title_container">
							<h1>Thông tin điểm gửi xe</h1>
							<div class="permalink_page_title">
								<a href="/store/edit-spa-wash/<%- dataServices[2]._id %>.html" class="add_new"> Chỉnh sửa</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<section class="sec_portfolio">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="search_portfolio form_schedule edit">
							<form id="form-add-room" onsubmit="return false">
								<div class="row">
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Tên đi<PERSON>m gửi xe</label>
											<input type="text" disabled value="<%- dataServices[2].name %>" placeholder="Nhập tên phòng">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Địa chỉ</label>
											<input type="text" disabled value="<%- dataServices[2].address %>" placeholder="Nhập địa chỉ">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Loại</label>
											<div class="select_form">
												<select class="" name="typePet" disabled>
													<option value="null">[[---------Chọn Loại --------]]</option>
													<option value="0" <%- dataServices[2].typePet==0? 'selected':'' %>>Ô tô</option>
													<option value="1" <%- dataServices[2].typePet==1? 'selected':'' %>>Mèo</option>
													<option value="2" <%- dataServices[2].typePet==2? 'selected':'' %>>Phương tiện khác</option>
													<option value="3" <%- dataServices[2].typePet==3? 'selected':'' %>>Tất cả</option>
												</select>
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field schedule_field">
											<label for="">Giới thiệu</label>
											<textarea placeholder="Nhập ghi chú"><%- dataServices[2].content %></textarea>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field upload_img_product schedule_field">
											<label for="">Ảnh con</label>
											<div class="upload_container img_child">
												<div class="upfile">
													<label for="upload-product-1"><img src="<%- dataServices[2].pictures? dataServices[2].pictures[0] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-2"><img src="<%- dataServices[2].pictures? dataServices[2].pictures[1] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[2].pictures? dataServices[2].pictures[2] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[2].pictures? dataServices[2].pictures[3] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
	<% } %>

	<% if(userLogin.servicesStore == 0){ %>
		<section class="page_title">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="page_title_container">
							<h1>Thông tin cửa hàng</h1>
							<div class="permalink_page_title">
								<a href="/store/edit-spa-wash/<%- dataServices[0]._id %>.html" class="add_new"> Chỉnh sửa</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<section class="sec_portfolio">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="search_portfolio form_schedule edit">
							<form id="form-add-room" onsubmit="return false">
								<div class="row">
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Tên cửa hàng</label>
											<input type="text" disabled value="<%- dataServices[0].name %>" placeholder="Nhập tên phòng">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Địa chỉ</label>
											<input type="text" disabled value="<%- dataServices[0].address %>" placeholder="Nhập địa chỉ">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Loại</label>
											<div class="select_form">
												<select class="" name="typePet" disabled>
													<option value="null">[[---------Chọn Loại --------]]</option>
													<option value="0" <%- dataServices[0].typePet==0? 'selected':'' %>>Ô tô</option>
													<option value="1" <%- dataServices[0].typePet==1? 'selected':'' %>>Mèo</option>
													<option value="2" <%- dataServices[0].typePet==2? 'selected':'' %>>Phương tiện khác</option>
													<option value="3" <%- dataServices[0].typePet==3? 'selected':'' %>>Tất cả</option>
												</select>
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field schedule_field">
											<label for="">Giới thiệu</label>
											<textarea placeholder="Nhập ghi chú"><%- dataServices[0].content %></textarea>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field upload_img_product schedule_field">
											<label for="">Ảnh con</label>
											<div class="upload_container img_child">
												<div class="upfile">
													<label for="upload-product-1"><img src="<%- dataServices[0].pictures? dataServices[0].pictures[0] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-2"><img src="<%- dataServices[0].pictures? dataServices[0].pictures[1] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[0].pictures? dataServices[0].pictures[2] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[0].pictures? dataServices[0].pictures[3] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
	<% } %>

	<% if(userLogin.servicesExamination == 0){ %>
		<section class="page_title">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="page_title_container">
							<h1>Thông tin</h1>
							<div class="permalink_page_title">
								<a href="/store/edit-spa-wash/<%- dataServices[1]._id %>.html" class="add_new"> Chỉnh sửa</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<section class="sec_portfolio">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="search_portfolio form_schedule edit">
							<form id="form-add-room" onsubmit="return false">
								<div class="row">
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Tên</label>
											<input type="text" disabled value="<%- dataServices[1].name %>" placeholder="Nhập tên">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Địa chỉ</label>
											<input type="text" disabled value="<%- dataServices[1].address %>" placeholder="Nhập địa chỉ">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Loại</label>
											<div class="select_form">
												<select class="" name="typePet" disabled>
													<option value="null">[[---------Chọn Loại --------]]</option>
													<option value="0" <%- dataServices[2].typePet==0? 'selected':'' %>>Ô tô</option>
													<option value="1" <%- dataServices[2].typePet==1? 'selected':'' %>>Mèo</option>
													<option value="2" <%- dataServices[2].typePet==2? 'selected':'' %>>Phương tiện khác</option>
													<option value="3" <%- dataServices[2].typePet==3? 'selected':'' %>>Tất cả</option>
												</select>
											</div>
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Giờ mở cửa</label>
											<input type="text" disabled value="<%- dataServices[1].timeOpen %>">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Giờ đóng cửa</label>
											<input type="text" disabled value="<%- dataServices[1].timeClose %>">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field schedule_field">
											<label for="">Giới thiệu</label>
											<textarea placeholder="Nhập ghi chú"><%- dataServices[1].content %></textarea>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field upload_img_product schedule_field">
											<label for="">Ảnh con</label>
											<div class="upload_container img_child">
												<div class="upfile">
													<label for="upload-product-1"><img src="<%- dataServices[1].pictures? dataServices[1].pictures[0] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-2"><img src="<%- dataServices[1].pictures? dataServices[1].pictures[1] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[1].pictures? dataServices[1].pictures[2] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[1].pictures? dataServices[1].pictures[3] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
	<% } %>

	<% if(userLogin.servicesSpa == 0){ %>
		<section class="page_title">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="page_title_container">
							<h1>Thông tin spa</h1>
							<div class="permalink_page_title">
								<a href="/store/edit-spa-wash/<%- dataServices[3]._id %>.html" class="add_new"> Chỉnh sửa</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<section class="sec_portfolio">
			<div class="container">
				<div class="row">
					<div class="col-md-12">
						<div class="search_portfolio form_schedule edit">
							<form id="form-add-room" onsubmit="return false">
								<div class="row">
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Tên Spa</label>
											<input type="text" disabled value="<%- dataServices[3].name %>" placeholder="Nhập tên spa">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Địa chỉ</label>
											<input type="text" disabled value="<%- dataServices[3].address %>" placeholder="Nhập địa chỉ">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Loại</label>
											<div class="select_form">
												<select class="" name="typePet" disabled>
													<option value="null">[[---------Chọn Loại --------]]</option>
													<option value="0" <%- dataServices[3].typePet==0? 'selected':'' %>>Ô tô</option>
													<option value="1" <%- dataServices[3].typePet==1? 'selected':'' %>>Mèo</option>
													<option value="2" <%- dataServices[3].typePet==2? 'selected':'' %>>Phương tiện khác</option>
													<option value="3" <%- dataServices[3].typePet==3? 'selected':'' %>>Tất cả</option>
												</select>
											</div>
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Giờ mở cửa</label>
											<input type="text" disabled value="<%- dataServices[3].timeOpen %>">
										</div>
									</div>
									<div class="col-md-3">
										<div class="input_field schedule_field">
											<label for="">Giờ đóng cửa</label>
											<input type="text" disabled value="<%- dataServices[3].timeClose %>">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field schedule_field">
											<label for="">Giới thiệu</label>
											<textarea placeholder="Nhập ghi chú"><%- dataServices[3].content %></textarea>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="input_field upload_img_product schedule_field">
											<label for="">Ảnh con</label>
											<div class="upload_container img_child">
												<div class="upfile">
													<label for="upload-product-1"><img src="<%- dataServices[3].pictures? dataServices[3].pictures[0] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-2"><img src="<%- dataServices[3].pictures? dataServices[3].pictures[1] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[3].pictures? dataServices[3].pictures[2] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
												<div class="upfile">
													<label for="upload-product-3"><img src="<%- dataServices[3].pictures? dataServices[3].pictures[3] : '' %>" onerror="this.src='/template/ui/img/upload.png'" alt=""></label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</section>
	<% } %>
</div>
