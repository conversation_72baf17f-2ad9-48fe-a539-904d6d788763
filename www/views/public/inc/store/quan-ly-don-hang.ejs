<style>
    button.swal2-styled {
        line-height: unset;
        width: unset;
    }

    .swal2-modal {
        width: auto
    }
</style>
<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1>Quản lý đơn hàng</h1>
                        <div class="permalink_page_title">
							<a href="javascript:;" class="export_excel"><img src="/template/ui/img/print.svg" alt=""> Xuất Excel</a>
						</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio">
                        <form onsubmit="return false">
                            <div class="input_container">
                                <div class="input_field search" style="width: 50% !important">
                                    <label for="">T<PERSON><PERSON> kiếm</label>
                                    <input type="text" name="search" id="search-goods"
                                           placeholder="">
                                </div>
                                <div class="input_field portfolio_parent">
                                    <label for="">Tình trạng</label>
                                    <div class="select_form">
                                        <select class="portfolio_select" id="status-table" name="portfolio_parent">
                                            <option value="all" selected>Tất cả</option>
                                            <option value="0">Chờ xác nhận</option>
                                            <option value="1">Chờ giao hàng</option>
                                            <option value="2">Đang giao</option>
                                            <option value="3">Hoàn thành</option>
                                            <option value="4">Trả hàng</option>
                                            <option value="5">Đã huỷ</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="input_field portfolio_parent">
                                    <label for="">Hình thức thanh toán</label>
                                    <div class="select_form">
                                        <select class="portfolio_select" id="status-payments" name="portfolio_parent">
                                            <option value="all" selected>Tất cả</option>
                                            <option value="0">COD</option>
                                            <option value="1">Thanh toán online</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="input_field date_time ico_time" style="border-right: unset">
                                    <label for="">Thời gian bắt đầu</label>
                                    <input type="text" class="dateTimeHandle" value='<%- moment(new Date().getTime()-30*24*60*60*1000, "HH:mm, DD/MM/YYYY") %>' id="time-start-1">
                                </div>
                                <div class="input_field date_time ico_time">
                                    <label for="">Thời gian kết thúc</label>
                                    <input type="text" class="dateTimeHandle" value='<%- moment(new Date().getTime(), "HH:mm, DD/MM/YYYY") %>' id="time-end-1">
                                </div>
                                <div class="search_submit_portfolio">
                                    <button name="search_portfolio" onclick="filterList()" style="width: 150px;;margin: 0">
                                        <img src="/template/ui/img/search.svg" alt=""> Tìm kiếm
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <table class="table_portfolio">
                        <thead>
                        <tr>
                            <th class="text-center">STT</th>
                            <th>Mã đơn hàng</th>
                            <th style="width: 400px !important;">Hàng mua</th>
                            <th>Người nhận</th>
                            <th>Ngày</th>
                            <th>Tổng tiền</th>
                            <th>Hình thức thanh toán</th>
                            <th>Tình trạng</th>
                            <th>Tác vụ</th>
                        </tr>
                        </thead>
                        <tbody class="tbody-table">
                        </tbody>
                    </table>
                    <div class="pagination_container">
                        <p>Hiển thị từ 1-10 trên 30 đơn hàng</p>
                        <ul class="pagination-list">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>

<script type="text/javascript" src="/template/store/quan-ly-don-hang.js?v=<%- cacheVersion %>"></script>
