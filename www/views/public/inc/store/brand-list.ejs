<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><PERSON><PERSON> sách thương hiệu</h1>
                        <div class="permalink_page_title">
                            <a href="/store/mng-products.html" class="">Quản lý món</a>
                            <!-- <a href="/store/mng-spa-wash.html" class="">Quản lý dịch vụ Spa</a>
                            <a href="/store/mng-parking.html" class="">Quản lý Điểm gửi xe</a>
                            <a href="/store/mng-garage.html" class="">Quản lý Gara</a> -->
                            <a href="/store/classify/table" class="">Quản lý đặt bàn</a>
                            <!-- <a href="/store/classify/gas" class="">Quản lý Gas / Trạm xăng</a> -->
                            <a href="/store/them-thuong-hieu.html" class="add_new">
                                <i class="fa fa-plus-circle" aria-hidden="true"></i> Thêm thương hiệu
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio">
                        <form onsubmit="return false">
                            <div class="input_container">
                                <div class="input_field search">
                                    <label for="">Tìm kiếm</label>
                                    <input type="text" name="search" id="search-goods" placeholder="Nhập tên thương hiệu...">
                                </div>
                                <div class="search_submit_portfolio">
                                    <button name="search_portfolio" onclick="filterList()">
                                        <img src="/template/ui/img/search.svg" alt=""> Tìm kiếm
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <table class="table_portfolio table_product">
                        <thead>
                        <tr>
                            <th class="text-center">STT</th>
                            <th>Mã</th>
                            <th>Thương Hiệu</th>
                            <th>Địa chỉ</th>
                            <th>Trạng thái</th>
                            <th>Tác vụ</th>
                        </tr>
                        </thead>
                        <tbody class="tbody-table"></tbody>
                    </table>
                    <div class="pagination_container">
                        <p>Hiển thị từ 1-10 trên 30 đơn hàng</p>
                        <ul class="pagination-list">
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<div class="popup" id="successDoneScheduleViewBox">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Bạn chưa có tài khoản ngân hàng
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup">
            <div class="info_confirm">
                <div class="text_info_confirm">
                    <p style="text-align: center; font-size: 20px;">Bạn vui lòng thêm ngân hàng để người dùng có thể
                        chuyển khoản cho bạn khi muốn thanh toán qua chuyển khoản</p>
                </div>
            </div>

        </div>
        <div class="submit_popup text-right">
            <a href="/thong-tin-tai-khoan.html" class="">Thêm ngân hàng</a>
        </div>
    </div>
</div>
<script>
    var services = <%- JSON.stringify(services) %>;
    var checkBank = <%- JSON.stringify(checkBank) %>;
</script>
<script type="text/javascript" src="/template/store/dich-vu.js?v=<%- cacheVersion %>"></script>
