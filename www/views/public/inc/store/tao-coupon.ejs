<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><%- render.title %></h1>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form onsubmit="return false" class="container createCoupon"
                              action="<%- typeof coupon != 'undefined' ? '/store/sua-coupon.html/' + coupon._id : '/store/tao-coupon.html' %>"
                              sRedirect='/store/quan-ly-coupon.html'
                              method="POST" accept-charset="utf-8">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">H<PERSON><PERSON> thức coupon</label>
                                        <select name="type">
                                            <option value="1" <%- (typeof coupon != 'undefined' && coupon.type == 1) ? 'selected' : '' %>>
                                                % Giá trị đơn hàng
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giá trị</label>
                                        <input type="text" data-type="number" name="value"
                                               value="<%- typeof coupon != 'undefined' ? coupon.value : '' %>"
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Thời gian bắt đầu</label>
                                        <input type="text" name="startTime" class="dateTimePicker"
                                               value="<%- typeof coupon != 'undefined' ? convertDateShow1(coupon.startTime) : '' %>"
                                               required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Thời gian kết thúc</label>
                                        <input type="text" name="endTime" class="dateTimePicker"
                                               value="<%- typeof coupon != 'undefined' ? convertDateShow1(coupon.endTime) : '' %>"
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">

                                <div class="col-md-<%- typeof coupon != 'undefined' ? '12' : '6' %>">
                                    <div class="input_field schedule_field">
                                        <label for="">Số lần sử dụng tối đa</label>
                                        <input type="number" name="countBooking" min="1"
                                               value="<%- typeof coupon != 'undefined' ? coupon.countBooking : '' %>"
                                               required>
                                    </div>
                                </div>

                                <% if(typeof coupon == 'undefined'){ %>
                                    <div class="col-md-6">
                                        <div class="input_field schedule_field">
                                            <label for="">Số lượng coupon</label>
                                            <input type="number" name="countCoupon" min="1" required>
                                        </div>
                                    </div>
                                <% } %>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Thương hiệu</label>
                                        <div class="select_form">
                                            <select class="" name="storeId">
                                                <option value="null">[[---------Chọn thương hiệu --------]]</option>
                                                <% stores.forEach(item=>{ %>
                                                    <option value="<%- item._id %>"><%- item.name %> </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giá trị đơn hàng tối thiểu</label>
                                        <input type="text" data-type="currency" name="minBillValue"
                                               value="<%- typeof coupon != 'undefined' ? coupon.minBillValue : '' %>"
                                               required>
                                    </div>
                                </div>
                            </div>

                            <div class="button_submit">
                                <a href="/store/quan-ly-coupon.html">Huỷ</a>
                                <button type="submit" name="update">Lưu</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script src="/template/datetimepicker/jquery.datetimepicker.full.min.js"></script>
<script src="/template/store/tao-coupon.js?v=<%- cacheVersion %>"></script>
