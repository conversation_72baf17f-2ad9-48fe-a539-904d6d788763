<script>
    var feeShip = 0;
</script>
<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a href="/admin/quan-ly-don-hang-doi-qua.html"><img src="/template/ui/img/arrow-left.png"
                                                                        alt=""></a> Chi tiết đơn hàng đổi quà
                        </h1>
                        <div class="status">
                            <% if(buyInfo.shippingStatus == 0){ %>
                                <span class="delivery_product" style="width: auto; cursor: pointer"
                                      onclick="updateStatus(3)">Giao hàng</span>
                                <span class="no_process" style="width: auto; cursor: pointer" onclick="updateStatus(2)">Từ chối đơn hàng</span>
                            <% } %>

                                <span class="no_process" style="width: auto; cursor: pointer"
                                      onclick="updateStatus(4)">Hoàn trả</span>
                                <span class="delivery_product" style="width: auto; cursor: pointer"
                                      onclick="updateStatus(1)">Đã hoàn thành</span>

                        </div>
                    </div>
                </div>
            </div>
        </div>


    </section>

    <section class="sec_product">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <% if(Number(buyInfo.shippingStatus) == 4){ %>
                        <div class="status_order_product" style="background: #F44336;">
                        <span class="status_order">
                            Hệ thống sẽ kiểm tra các chứng cứ bạn gửi và phản hồi lại bạn sớm.
                        </span>
                            <span class="detail">
                            Nếu có sai sót, bạn có thể nhấn hoàn thành đơn hàng khi người nhận đã nhận hàng
						</span>
                        </div>
                    <% }else{ %>
                        <div class="status_order_product"
                             style="background: <%- Number(buyInfo.shippingStatus) == 0 ? '#08BCA6' : Number(buyInfo.shippingStatus) == 1 ? '#00BCD4' : Number(buyInfo.shippingStatus) == 2 ? '#2196F3' : Number(buyInfo.shippingStatus) == 3 ? '#4CAF50' : '#9E9E9E' %>">
                        <span class="status_order">
                            <img src="/template/ui/img/truck_w.svg"
                                 alt="">
                            <strong><%- Number(buyInfo.shippingStatus) == 0 ? 'Đang chờ bạn xác nhận đơn hàng' : '' %></strong>
                            <strong><%- Number(buyInfo.shippingStatus) == 1 ? 'Đơn hàng đã hoàn thành' : '' %></strong>
                            <strong><%- Number(buyInfo.shippingStatus) == 2 ? 'Đơn hàng bị huỷ bỏ' : '' %></strong>
                            <strong><%- Number(buyInfo.shippingStatus) == 3 ? 'Đơn hàng đã giao' : '' %></strong>
                            <strong><%- Number(buyInfo.shippingStatus) == 4 ? 'Đơn hàng đã bị hoàn trả' : '' %></strong>
                        </span>
                        </div>
                    <% } %>

                    <div class="info_order_product">
                        <div class="info_order_product_container">
                            <div class="col_6">
                                <div class="box_info_order">
                                    <div class="title_box">
                                        <h6><img src="/template/ui/img/map.svg" alt=""> Địa chỉ nhận hàng</h6>
                                    </div>
                                    <div class="content_box">
                                        <ul>
                                            <li><%- buyInfo.shippingData.name %></li>
                                            <li><%- buyInfo.shippingData.phone %></li>
                                            <li><%- buyInfo.shippingData.address %> </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col_6">
                                <div class="box_info_order">
                                    <div class="title_box">
                                        <h6><img src="/template/ui/img/pay-per-click.svg" alt=""> Trạng thái
                                        </h6>
                                    </div>
                                    <div class="content_box">
                                        <p><%- buyInfo.statusText %></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="detail_order_container info_order_product">
                                <div class="title_box">
                                    <h6>Danh sách hàng mua</h6>
                                    <p class="code_product">Mã đơn hàng: <%- buyInfo._id %></p>
                                </div>
                                <%
                                // var TongTien = Number(buyInfo.totalPriceShop);
                                // let TongThanhToan = Number(buyInfo.totalPriceShop) + Number(buyInfo.transportFee);
                                buyInfo.products.forEach((p, i)=>{
                                    // console.log('buyInfo', buyInfo)
                                %>
                                    <div class="content_box">
                                        <div class="info_product_container">
                                            <div class="image_product">
                                                <figure>
                                                    <img src="<%- encodeURI(p.image.data.attributes.url) %>" alt="">
                                                </figure>
                                            </div>
                                            <div class="text_product">
                                                <div class="name_product"><%- p.productName %></div>
                                                <div class="price_product">
                                                    <%- priceFormat(p.point) %> Điểm
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                <% }) %>

                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </section>
</div>

<div class="popup" id="tuChoiDonHang">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Lý do từ chối đơn hàng
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup">
            <div class="info_confirm">
                <div class="text_info_confirm">
                    <p>Để giúp nguời dùng cải thiện thao tác đặt hàng hoặc thay đổi đơn hàng phù hợp với tiêu chí của
                        của hàng</p>
                </div>
                <div class="icon"><img src="/template/ui/img/check.svg" alt=""></div>
            </div>

            <div class="info_confirm">
                <textarea id="lyDoTuChoiDonHang" style="width: 100%; height: 100px;"></textarea>

            </div>
        </div>
        <div class="submit_popup text-right">
            <a href="javascript:;" onclick="guiLenhTuChoi()" class="confirm">Từ chối đơn hàng</a>
        </div>
    </div>
</div>


<div class="popup" id="donHangBiTraLaiViewBox">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Vui lòng cung cấp 3 ảnh chụp sản phậm bị trả lại<br> (có dấu đỏ hoặc biên lai trả hàng)
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup input_field upload_img_product schedule_field">
            <div class="upload_container img_child" style="padding: 22px;">
                <div class="upfile">
                    <label for="upload-product-1"><img src="/template/ui/img/upload.png" alt=""></label>
                    <input class="upload-new-picture upfile-input" type="file" name="myfile_1" id="upload-image-1"
                           accept="image/x-png,image/gif,image/jpeg">
                </div>
                <div class="upfile">
                    <label for="upload-product-2"><img src="/template/ui/img/upload.png" alt=""></label>
                    <input class="upload-new-picture upfile-input" type="file" name="myfile_2" id="upload-image-2"
                           accept="image/x-png,image/gif,image/jpeg">
                </div>
                <div class="upfile">
                    <label for="upload-product-3"><img src="/template/ui/img/upload.png" alt=""></label>
                    <input class="upload-new-picture upfile-input" type="file" name="myfile_3" id="upload-image-3"
                           accept="image/x-png,image/gif,image/jpeg">
                </div>
            </div>
        </div>
        <div class="submit_popup text-right">
            <a href="javascript:;" onclick="guiAdminreview()" class="confirm">Gửi ADMIM kiểm tra</a>
        </div>
    </div>
</div>
<script>
    var buyInfo = <%- JSON.stringify(buyInfo) %>
    console.log(buyInfo)
</script>
<script type="text/javascript" src="/template/admin/chi-tiet-don-hang-doi-qua.js?v=<%- cacheVersion %>"></script>
