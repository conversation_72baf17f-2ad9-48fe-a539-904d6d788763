<div class="content_boxed">

    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a href="/admin/cua-hang.html"><img src="/template/ui/img/arrow-left.png" alt=""></a>
                            Thông tin chủ kinh doanh
                        </h1>
                        <a style="display: inline-block; background: #FFFFFF; border: 1px solid #E2E2E2; border-radius: 4px; height: 46px; line-height: 42px; text-align: center; padding: 3px 10px;"
                           href="/admin/shop-trung-tam.html?managerId=<%- dataUser._id %>">
                            Quản lý shop & trung tâm
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <div class="tab_container">
                            <ul class="menu_tab">
                                <li class="active">H<PERSON> s<PERSON> người dùng</li>
                                <li>Đổi mật khẩu</li>
                            </ul>
                            <div class="content_tab">
                                <div class="inner_box">
                                    <form action="/admin/chinh-sua-yeu-cau/<%- dataUser._id %>.html"
                                          sRedirect='/admin/cua-hang.html' method="POST" accept-charset="utf-8"
                                          class="baseFormRequest">
                                        <div class="">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Họ và tên</label>
                                                        <input type="text" name="" disabled
                                                               value="<%- dataUser.fullName %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Số điện thoại</label>
                                                        <input type="text" name="" disabled
                                                               value="<%- dataUser.phone %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Email</label>
                                                        <input type="email" name="" disabled
                                                               value="<%- dataUser.email %>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Địa chỉ</label>
                                                        <input type="text" name="" value="<%- dataUser.address %>"
                                                               disabled>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Cửa hàng</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesStore">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesStore == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesStore == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Xưởng dịch vụ</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesExamination">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesExamination == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesExamination == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Điểm gửi xe</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesParking">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesParking == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesParking == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Spa</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesSpa">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesSpa == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesSpa == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Showroom</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesShowroom">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesShowroom == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesShowroom == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Trạm xăng</label>
                                                        <div class="select_form">
                                                            <select class="" name="servicesGas">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="0" <%- dataUser.servicesGas == 0 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="1" <%- dataUser.servicesGas == 1 ? 'selected' : '' %>>
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Tính phí</label>
                                                        <div class="select_form">
                                                            <select class="" name="fee">
                                                                <option value="all" disabled>Trạng thái</option>
                                                                <option value="1" <%- dataUser.fee == 1 ? 'selected' : '' %> >
                                                                    Mở
                                                                </option>
                                                                <option value="0" <%- dataUser.fee == 0 ? 'selected' : '' %> >
                                                                    Đóng
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Quỹ phí</label>
                                                        <input name="funds" type="text"
                                                               data-type="currency"
                                                               value="<%- priceFormat(dataUser.funds) %> VND">
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Số dư online</label>
                                                        <input name="wallet" type="text"
                                                               data-type="currency"
                                                               value="<%- priceFormat(dataUser.wallet) %> VND">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="button_submit">
                                            <a href="/admin/cua-hang.html">Quay lại</a>
                                            <button type="submit" name="save">Lưu</button>
                                            <p style="display: inline-block; margin: 0 3px; width: auto; padding: 14px 30px; border-radius: 4px; line-height: 18px; background: #4CAF50; color: #fff; cursor: pointer"
                                               onclick="successDoneScheduleViewBox()">Nạp tiền</p>

                                            <% if(dataUser.wallet > 0){ %>
                                                <p style="display: inline-block; margin: 0 3px; width: auto; padding: 14px 30px; border-radius: 4px; line-height: 18px; background: #17a2b8; color: #fff; cursor: pointer"
                                                   onclick="thanhToanSoDu()">Thanh toán số dư online</p>
                                            <% } %>

                                            <% if(dataUser.status == 1){ %>
                                                <p style="display: inline-block; margin: 0 3px; width: auto; padding: 14px 30px; border-radius: 4px; line-height: 18px; background: #F44336; color: #fff; cursor: pointer"
                                                   onclick="changeStatus(2)">Khoá tài khoản</p>
                                            <% }else{ %>
                                                <p style="display: inline-block; margin: 0 3px; width: auto; padding: 14px 30px; border-radius: 4px; line-height: 18px; background: #03A9F4; color: #fff; cursor: pointer"
                                                   onclick="changeStatus(1)">Mở khoá tài khoản</p>
                                            <% } %>
                                            <% if(dataUser.confirmEmail == 0){ %>
                                                <p style="display: inline-block; margin: 0 3px; width: auto; padding: 14px 30px; border-radius: 4px; line-height: 18px; background: #9f90f1; color: #fff; cursor: pointer"
                                                   onclick="changeStatusEmail()">Xác nhận email</p>
                                            <% } %>
                                        </div>
                                    </form>
                                </div>
                                <div class="inner_box">
                                    <form onsubmit="return false" class="container baseFormRequest"
                                          action="/admin/chinh-sua-mat-khau-nguoi-dung/<%- dataUser._id %>.html"
                                          sRedirect='/admin/quan-ly-nguoi-dung.html' method="POST"
                                          accept-charset="utf-8">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Nhập mật khẩu mới</label>
                                                    <input type="password" name="passwordNew">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Xác nhận mật khẩu mới</label>
                                                    <input type="password" name="passwordCFNew">
                                                </div>
                                            </div>
                                        </div>
                                        <div class="button_submit">
                                            <button type="submit" name="confirm">Xác nhận</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_income">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="income_container">
                        <div class="col_12">
                            <div class="box_income general">
                                <div class="title_box">
                                    Tổng quan
                                </div>
                                <div class="content_box">
                                    <div class="general_will_pay">
                                        <div class="general_container">
                                            <div class="inner_box">
                                                <h5>Số dư hiện có trong tài khoản</h5>
                                                <div class="tl">
                                                    Tổng cộng
                                                </div>
                                                <div class="price" style="color: #e01a22;">
                                                    <%- priceFormat(dataUser.funds) %> VNĐ
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="general_payed">
                                        <div>
                                            <h5 style="background: orange; color: #fff; padding: 10px 0;">Phí đã thanh
                                                toán</h5>
                                            <div class="general_container phi-thanh-toan">
                                                <div class="inner_box">
                                                    <div class="tl">
                                                        Hôm nay
                                                    </div>
                                                    <div class="price date-now">
                                                    </div>
                                                    <div class="tl">
                                                        Tuần này
                                                    </div>
                                                    <div class="price date-week">
                                                    </div>
                                                    <div class="tl">
                                                        Từ trước đến giờ
                                                    </div>
                                                    <div class="total_price price date-all">
                                                    </div>
                                                </div>
                                                <div class="inner_box">
                                                    <div class="tl">
                                                        Hôm qua
                                                    </div>
                                                    <div class="price date-yesterday">
                                                    </div>
                                                    <div class="tl">
                                                        Tháng này
                                                    </div>
                                                    <div class="price date-month">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div style="border-top: 1px solid #ccc; padding-top: 10px;">
                                            <h5 style="background: green; color: #fff; padding: 10px 0;">Thu nhập</h5>
                                            <div class="general_container thu-nhap">
                                                <div class="inner_box">
                                                    <div class="tl">
                                                        Hôm nay
                                                    </div>
                                                    <div class="price date-now">
                                                    </div>
                                                    <div class="tl">
                                                        Tuần này
                                                    </div>
                                                    <div class="price date-week">
                                                    </div>
                                                    <div class="tl">
                                                        Từ trước đến giờ
                                                    </div>
                                                    <div class="total_price price date-all">
                                                    </div>
                                                </div>
                                                <div class="inner_box">
                                                    <div class="tl">
                                                        Hôm qua
                                                    </div>
                                                    <div class="price date-yesterday">
                                                    </div>
                                                    <div class="tl">
                                                        Tháng này
                                                    </div>
                                                    <div class="price date-month">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <div class="box_income detail">
                                <div class="title_box">
                                    Lịch sử thanh toán tiền online
                                </div>
                                <div class="content_box">
                                    <div class="tab_container">
                                        <ul class="menu_tab">
                                        </ul>
                                        <div class="content_tab">
                                            <div class="inner_box">
                                                <div class="content_tab">
                                                    <div class="inner_box" style="display: block;">
                                                        <section class="sec_portfolio">
                                                            <div class="search_portfolio">
                                                                <form onsubmit="return false">
                                                                    <div class="input_container">
                                                                        <div class="input_field date_time ico_time"
                                                                             style="border-right: unset">
                                                                            <label for="">Thời gian bắt đầu</label>
                                                                            <input type="text" class="dateTimeHandle"
                                                                                   value='<%- moment(new Date().getTime() - 30 * 24 * 60 * 60 * 1000, "HH:mm, DD/MM/YYYY") %>'
                                                                                   id="time-start">
                                                                        </div>
                                                                        <div class="input_field date_time ico_time">
                                                                            <label for="">Thời gian kết thúc</label>
                                                                            <input type="text" class="dateTimeHandle"
                                                                                   value='<%- moment(new Date().getTime(), "HH:mm, DD/MM/YYYY") %>'
                                                                                   id="time-end">
                                                                        </div>

                                                                        <div class="search_submit_portfolio"
                                                                             style="width: 150px;">
                                                                            <button name="search_portfolio"
                                                                                    onclick="lichSuTraTienChoCuaHang()">
                                                                                <img src="/template/ui/img/search.svg"
                                                                                     alt="">
                                                                                Tìm
                                                                                kiếm
                                                                            </button>
                                                                        </div>
                                                                    </div>
                                                                </form>
                                                            </div>
                                                            <table class="table_portfolio table-0">
                                                                <thead>
                                                                <tr>
                                                                    <th class="text-center">STT</th>
                                                                    <th>Tiền thanh toán</th>
                                                                    <th>Thời gian</th>
                                                                    <th></th>
                                                                </tr>
                                                                </thead>
                                                                <tbody class="tbody-table">
                                                                </tbody>
                                                            </table>
                                                            <div class="pagination_container pagination-0">
                                                                <p></p>
                                                                <ul class="pagination-list">
                                                                </ul>
                                                            </div>
                                                        </section>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="box_income detail">
                                <div class="title_box">
                                    Chi tiết
                                </div>
                                <div class="content_box">
                                    <div class="tab_container">
                                        <ul class="menu_tab">
                                            <li>Lịch sử thu nhập</li>
                                            <li>Lịch sử trả phí</li>
                                            <li>Lịch sử nạp tiền</li>
                                        </ul>
                                        <div class="content_tab">
                                            <div class="inner_box table-box-1">
                                                <div class="will_pay text-center">
                                                    <img src="/template/ui/img/no-pay.png" alt="">
                                                    <p>Không có lịch giao dịch</p>
                                                </div>
                                                <div class="payed_container">
                                                    <table class="table-1">
                                                        <thead>
                                                        <tr>
                                                            <th>STT</th>
                                                            <th>Mã</th>
                                                            <th>Số tiền</th>
                                                            <th>Ngày</th>
                                                            <th>Loại dịch vụ</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="inner_box table-box-2">
                                                <div class="will_pay text-center">
                                                    <img src="/template/ui/img/no-pay.png" alt="">
                                                    <p>Không có lịch giao dịch</p>
                                                </div>
                                                <div class="payed_container">
                                                    <table class="table-2">
                                                        <thead>
                                                        <tr>
                                                            <th>STT</th>
                                                            <th>Mã</th>
                                                            <th>Số tiền</th>
                                                            <th>Ngày</th>
                                                            <th>Loại dịch vụ</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                            <div class="inner_box table-box-3">
                                                <div class="will_pay text-center">
                                                    <img src="/template/ui/img/no-pay.png" alt="">
                                                    <p>Không có lịch giao dịch</p>
                                                </div>
                                                <div class="payed_container">
                                                    <table class="table-3">
                                                        <thead>
                                                        <tr>
                                                            <th>STT</th>
                                                            <th>Ngày nạp</th>
                                                            <th>Số tiền</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<div class="popup" id="successDoneScheduleViewBox">
    <div class="popup_container popup_detail_product">
        <div class="title_popup">
            Nạp tiền cho người kinh doanh
            <span class="close_popup"><img src="/template/ui/img/close.svg" alt=""></span>
        </div>
        <div class="content_popup">
            <div class="info_confirm">
                <div class="text_info_confirm">
                    <p>- Bạn vui lòng nhập số tiền nạp cho tài khoản</p>
                </div>
            </div>

            <div class="info_confirm">
                <input id="gia" style="width: 100%;" type="text" name="currency-field"
                       value="" data-type="currency">
            </div>
        </div>
        <div class="submit_popup text-right">
            <a href="javascript:;" onclick="xacNhanHoanThanh()" class="confirm">Nạp</a>
        </div>
    </div>
</div>
<script>
    var dataUser = <%- JSON.stringify(dataUser) %>
</script>
<script type="text/javascript" src="/template/admin/chinh-sua-yeu-cau.js?v=<%- cacheVersion %>"></script>
