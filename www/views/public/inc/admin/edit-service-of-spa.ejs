<style>
    .upfile label::before {
        content: unset !important;
    }
</style>
<div class="content_boxed">

    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a onclick="goBack()"><img src="/template/ui/img/arrow-left.png"
                                                       alt=""></a> Chỉnh sửa dịch vụ
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form id="form-add-product" onsubmit="return false">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input_field schedule_field">
                                        <label for="">Tên dịch vụ</label>
                                        <input type="text" name="name"
                                               value="<%- product.name %>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="input_field schedule_field">
                                        <label for="">Danh mục</label>
                                        <div class="select_form">
                                            <select class="" name="categoryId">
                                                <option value="">[[---------Chọn danh mục --------]]</option>
                                                <% categories.forEach(item=>{ %>
                                                    <option <%- product.categoryId == item._id ? 'selected' : '' %>
                                                            value="<%- item._id %>"><%- item.name %>
                                                </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="input_field schedule_field">
                                        <label for="">Thương hiệu</label>
                                        <div class="select_form">
                                            <select class="" name="storeId">
                                                <option value="null">[[---------Chọn thương hiệu --------]]</option>
                                                <% stores.forEach(item=>{ %>
                                                    <option <%- product.storeId == item._id ? 'selected' : '' %>
                                                            value="<%- item._id %>"><%- item.name %> </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="input_field schedule_field">
                                        <label for="">Cơ sở</label>
                                        <div class="select_form">
                                            <div class="select_form">
                                                <select class="" name="branchId">
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả ngắn</label>
                                        <textarea name="shortDes" placeholder="Nhập nội dung"
                                                  style="height: 100px"><%- product.shortDes %></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả</label>
                                        <textarea name="description"
                                                  placeholder="Nhập nội dung"><%- product.description %></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giá</label>
                                        <input name="price" placeholder="Nhập giá" type="text"
                                               data-type="currency" value="<%- priceFormat(product.price) %> VND">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tình trạng</label>
                                        <div class="select_form">
                                            <select class="" name="typeService">
                                                <option value="all" disabled>Chọn tình trạng</option>
                                                <option <%- product.typeService == 0 ? 'selected' : '' %> value="0">Tạm ngừng
                                                </option>
                                                <option <%- product.typeService == 1 ? 'selected' : '' %> value="1">sẵn sàng
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="classify-list">
                                <% product.classify.forEach((item1, index)=>{ %>
                                    <div class="col-md-12 classify-item" id="classify-item-${count+1}">
                                        <div class="input_field schedule_field">
                                            <label for="" class="title-classify">Nhóm <%- index + 1 %></label>
                                            <div class="row type-product">
                                                <label for="">Tên nhóm</label>
                                                <div class="col-md-12">
                                                    <div class="input_field schedule_field">
                                                        <input placeholder="Tên nhóm con" name="name-type"
                                                               type="text" value="<%- item1.name %>">
                                                    </div>
                                                </div>
                                                <label for="">Phân loại dịch vụ</label>
                                                <div style="width: 100%;" class="type-list">
                                                    <% for ( let i = 0; i < item1.data.length; i++ ) { %>
                                                        <div class="col-md-12 type-item">
                                                            <div class="input_field schedule_field">
                                                                <input name="nameProd" placeholder="Nhập phân loại dịch vụ" type="text" class="mr-3" value="<%- item1.data[i].name %>">
                                                                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency" value="<%- item1.data[i].price %>">
                                                                <a href="javascript:;" class="delete-service"
                                                                   title="Xoá" onclick="deleteTypeItem(this)">
                                                                    <img src="/template/ui/img/delete-2-bage.svg"
                                                                         alt="">
                                                                </a>
                                                            </div>
                                                        </div>
                                                    <% } %>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <a class="button-classify"
                                                           style="background: white;border: solid 2px #8db7ff; color: #8db7ff;"
                                                           onclick="addType(this)"><img
                                                                    src="/template/ui/img/plus-nhat.svg" alt="">
                                                            Thêm</a>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <a class="button-classify"
                                                           style="background: white;border: solid 2px #FF1615; color: #FF1615;"
                                                           onclick="deleteClassify(this)"><img
                                                                    src="/template/ui/img/delete-2-bage-red.svg" alt="">
                                                            Xóa</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                </div>
                                <div class="col-md-4">
                                    <br>
                                    <br>
                                    <div class="input_field schedule_field">
                                        <a class="button-classify"
                                           style="background: white;border: solid 2px #1969EF; color: #1969EF;"
                                           onclick="addClassify()"><img src="/template/ui/img/plus-blue.svg" alt="">
                                            Thêm phân loại</a>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input_field schedule_field upload_img_product">
                                        <label for="">Ảnh bìa</label>
                                        <div class="upload_container img_cover">
                                            <div class="upfile">
                                                <label for="">
                                                    <button class="delete-image"  style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">Xóa</button>
                                                    <img src="<%- product.thumbail %>"
                                                         alt=""></label>
                                                <input type="file" name="myfile_1" id="upload-product-0" accept="image/x-png,image/gif,image/jpeg"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-9">
                                    <div class="input_field upload_img_product schedule_field">
                                        <label for="">Ảnh con</label>
                                        <div class="upload_container img_child">
                                            <div class="upfile">
                                                <label for="">
                                                    <button class="delete-image"  style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">Xóa</button>
                                                    <img src="<%- product.pictures[0] %>"
                                                         onerror="this.src='/template/ui/img/upload.png'"
                                                         alt=""></label>
                                                <input type="file" name="myfile_1"
                                                       urlOld="<%- product.pictures[0] ? product.pictures[0] : 0 %>"
                                                       id="upload-product-1" accept="image/x-png,image/gif,image/jpeg"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                            <div class="upfile">
                                                <label for="">
                                                    <button class="delete-image"  style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">Xóa</button>
                                                    <img src="<%- product.pictures[1] %>"
                                                         onerror="this.src='/template/ui/img/upload.png'"
                                                         alt=""></label>
                                                <input type="file" name="myfile_2"
                                                       urlOld="<%- product.pictures[1] ? product.pictures[1] : 0 %>"
                                                       id="upload-product-2" accept="image/x-png,image/gif,image/jpeg"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                            <div class="upfile">
                                                <label for="">
                                                    <button class="delete-image"  style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">Xóa</button>
                                                    <img src="<%- product.pictures[2] %>"
                                                         onerror="this.src='/template/ui/img/upload.png'"
                                                         alt=""></label>
                                                <input type="file" name="myfile_3" accept="image/x-png,image/gif,image/jpeg"
                                                       urlOld="<%- product.pictures[2] ? product.pictures[2] : 0 %>"
                                                       id="upload-product-3"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="button_submit">
                                <a onclick="goBack()">Quay lại</a>
                                <button type="submit" name="save">Lưu</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>
<script>
    function goBack() {
        window.history.back();
    }
    let pictureOlds = <%- JSON.stringify(product.pictures) %>;
    let productId = '<%- product._id %>';
    let storeId = '<%- product.storeId %>';
    let branchId = '<%- product.branchId %>';
</script>
<script type="text/javascript" src="/template/admin/edit-service-of-spa.js?v=<%- cacheVersion %>"></script>
