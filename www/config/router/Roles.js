"use strict";
const CfRole = require('../CfRole');
const UserSession = require('../../session/UserSession');
const UserModel = require('../../models/UserModel');
const CfJws = require('../CfJws');

CfRole.role.all = -1;
CfRole.role.noAccount = -2;
CfRole.role.account = -3;

module.exports = {
    bin: CfRole,

    authorization: function (req, res, next) {
        let currentRole = [];
        //{
        //   "admin": 0,
        //   "store": 1,
        //   "user": 2,
        //   "all": -1,
        //   "noAccount": -2,
        //   "account": -3
        // }
        for (let itemRole in this.bin.role) {
            if (res.bindingRole.config.auth.includes(this.bin.role[itemRole])) {
                currentRole.push(this.bin.role[itemRole]);
            }
        }

        let user;
        if (req.query.output && req.query.output === 'json') {
            let token = req.headers['access-token'];
            let authorization = req.headers['Authorization'];
            req.version = 'api';
            if (token || authorization) {
                if (currentRole.includes(-1) || currentRole.includes(-2)) {
                    CfJws.extraToken(token).then(function (rslCheckToken) {
                        if (rslCheckToken.error) {
                            continuE();
                        } else {
                            user = rslCheckToken.data;
                            continuE();
                        }
                    })
                } else {
                    CfJws.extraToken(token).then(function (rslCheckToken) {
                        if (rslCheckToken.error) {
                            return res.json({error: true, message: "Mã token đã hết hạn hoặc không hợp lệ"});
                        } else {
                            user = rslCheckToken.data;
                            continuE();
                        }
                    })
                }

            } else {
                if (currentRole.includes(-1) || currentRole.includes(-2)) {
                    CfJws.extraToken(token).then(function (rslCheckToken) {
                        if (rslCheckToken.error) {
                            continuE();
                        } else {
                            user = rslCheckToken.data;
                            continuE();
                        }
                    })
                } else {
                    return res.json({error: true, message: "Cần gửi tham số token hợp lệ"});
                }
            }
        } else {
            req.version = 'web';
            user = UserSession.getUser(req.session);
            continuE();
        }


        async function continuE() {
            if (user) {
                let userNew = await UserModel.MODEL.getUsersById(user._id);
                if (userNew) {
                    if (userNew.status == 2) {
                        if (req.version == 'web') {
                            if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                                return res.json({error: true, message: "ERR_ACCOUNT_HAS_BEEN_DELETED"});
                            } else {
                                res.redirect("/logout.html");
                            }
                        } else {
                            return res.json({error: true, message: "ERR_ACCOUNT_HAS_BEEN_DELETED"});
                        }
                    }

                    if (req.version == 'web') {
                        UserSession.saveUser(req.session, {
                            ...user,
                            fee: userNew.fee,
                            funds: userNew.funds,
                            wallet: userNew.wallet,
                            status: userNew.status,
                            countMessage: userNew.countMessage,
                            notifications: userNew.notifications,
                        })
                    }
                } else {
                    if (req.version == 'web') {
                        if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                            return res.json({error: true, message: "ERR_ACCOUNT_HAS_BEEN_DELETED"});
                        } else {
                            res.redirect("/logout.html");
                        }
                    } else {
                        return res.json({error: true, message: "ERR_ACCOUNT_HAS_BEEN_DELETED"});
                    }
                }
            }

            req.user = user;
            if (currentRole.includes(-1)) {
                next();
            } else if (currentRole.includes(-2)) {
                if (!user) {
                    next();
                } else {
                    if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                        return res.json({error: true, message: "Vượt quyền truy cập1"});
                    } else {
                        res.redirect("/");
                    }
                }
            } else if (currentRole.includes(-3)) {
                if (user) {
                    next();
                } else {
                    if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                        return res.json({error: true, message: "Vượt quyền truy cập2"});
                    } else {
                        res.redirect("/");
                    }
                }
            } else {
                if (!user) {
                    if (req.version == 'web') {
                        if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                            return res.json({error: true, message: "Vượt quyền truy cập3"});
                        } else {
                            res.redirect("/");
                        }
                    } else {
                        return res.json({error: true, message: "Vượt quyền truy cập4"});
                    }

                } else {
                    if (currentRole.includes(user[CfRole.nameAttribute])) {
                        next();
                    } else {
                        if (res.bindingRole.config[req.method.toLowerCase()] == 'json') {
                            return res.json({error: true, message: "Vượt quyền truy cập5"});
                        } else {
                            res.redirect("/");
                        }
                    }
                }
            }
        }
    }
};
