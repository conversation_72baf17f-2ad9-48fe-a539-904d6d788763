"use strict";

// Config for Production
const prodConfig = {
    protocol: 'https',
    domainRelease: 'admin.langnuoibienvandon.com',
    webName: 'langnuoibienvandon.com',
    tokenVersion: '1.0.0',
    host: '************',
    port: '4000',
    db: {
        name: 'myappdb',
        user: 'appuser',
        password: 'Thanhnx1Q2SS',
        host: '************',
        port: '27017'
    },
    apiUrlV2: 'http://************:1337'
};

// Config for Development
const devConfig = {
    protocol: 'http',
    domainRelease: 'localhost',
    webName: 'langnuoibienvandon.com',
    tokenVersion: '1.0.0',
    host: '************',
    port: '4000',
    db: {
        name: 'myappdb',
        user: 'appuser',
        password: 'Thanhnx1Q2SS',
        host: '************',
        port: '27017'
    },
    apiUrlV2: 'http://************:1337'
};

// Config for Local 
const localConfig = {
    protocol: 'http',
    domainRelease: 'localhost',
    webName: 'langnuoibienvandon.com',
    tokenVersion: '1.0.0',
    host: 'localhost',
    port: '4000',
    db: {
        name: 'myappdb',
        user: 'appuser',
        password: 'Thanhnx1Q2SS',
        host: '************',
        port: '27017'
    },
    apiUrlV2: 'http://************:1337'
};

// Determine the current environment
const environment = process.env.NODE_ENV || 'development';

// Select the correct config based on environment
let config;

switch (environment) {
    case 'production':
        config = {
            ...prodConfig,
            domain: `${prodConfig.protocol}://${prodConfig.domainRelease}`
        };
        break;
    case 'development':
        config = {
            ...devConfig,
            domain: `${devConfig.protocol}://${devConfig.domainRelease}:${devConfig.port}`
        };
        break;
    case 'local':
    default:
        config = {
            ...localConfig,
            domain: `${localConfig.protocol}://${localConfig.domainRelease}:${localConfig.port}`
        };
        break;
}

// Export the selected config
module.exports = {
    ...config
};
