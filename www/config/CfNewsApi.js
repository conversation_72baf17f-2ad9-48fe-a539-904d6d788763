const {apiUrlV2} = require("./CfApp");
const optionsNewsProxy = {
    target: apiUrlV2,
    // pathRewrite: {
    //     // '/list-cat': '/categories?include=1,54,52,53',
    //     // '/categories/*': '/categories/*',
    //     // '/category': '/categories',
    //     // '/post': '/categories?include=1,54,52,53',
    // },
    onProxyReq: function onProxyReq(proxyReq, req, res) {
        // Log outbound request to remote target
        console.log('-->  ', req.method, req.path, '->', proxyReq.path);
    },
    onError: function onError(err, req, res) {
        console.error(err);
        res.status(500);
        res.json({error: 'Error when connecting to remote server.'});
    },
    logLevel: 'debug',
    changeOrigin: true,
    // toProxy: true,
    // secure: false
};
exports.optionsNewsProxy = optionsNewsProxy
