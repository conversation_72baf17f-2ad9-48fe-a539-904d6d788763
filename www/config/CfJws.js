"use strict";

const tokenVersion = require('./CfApp').tokenVersion;
const jwt = require('jsonwebtoken');
const Promise = require('bluebird');

module.exports = {
    extraToken: function (token) {
        return new Promise(resolve => {
            jwt.verify(token, tokenVersion, function (err, decoded) {
                if (err) {
                    resolve({error: true, message: 'Failed to authenticate token.'});
                } else {
                    resolve({error: false, data: decoded});

                }
            });
        })
    },
    createToken: function (data) {
        // let dataCreateToken = {
        //     "type": data.type,
        //     "confirmPhone": data.confirmPhone,
        //     "status": data.status,
        //     "phone": data.phone,
        //     "email": data.email,
        // }
        return jwt.sign(data, tokenVersion, {expiresIn: '219000h'});
    }
};
