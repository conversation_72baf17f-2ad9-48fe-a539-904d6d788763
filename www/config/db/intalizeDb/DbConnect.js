"use strict";

let mongoose = require('mongoose');
const databaseConfig = require('../../CfApp').db;

const mongodUrl = databaseConfig.user === '' ? 'mongodb://' + databaseConfig.host + ':' + databaseConfig.port + '/' + databaseConfig.name :
    'mongodb://' + databaseConfig.user + ':' + encodeURIComponent(databaseConfig.password) + '@' + databaseConfig.host + ':' + databaseConfig.port + '/' + databaseConfig.name;
//console.log(mongodUrl)
mongoose = mongoose.createConnection(mongodUrl, { useMongoClient: true });
module.exports = mongoose;