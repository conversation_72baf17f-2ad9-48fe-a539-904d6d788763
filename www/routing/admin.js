"use strict";
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const CategoryModel = require('../models/CategoryModel');
const NewsModel = require('../models/NewsModel');
const PromotionModel = require('../models/PromotionModel');
const ServicesModel = require('../models/ServicesModel');
const FileUtils = require('../utils/FileUtils');
const StringUtils = require('../utils/StringUtils');
const APP = require('../../app');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const ServiceOfClinicModel = require('../models/ServiceOfClinicModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const SettingAdminModel = require('../models/SettingAdminModel');
const AdsSettingModel = require('../models/AdsSettingModel');
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const AccountBankModel = require('../models/AccountBankModel');
const HuongDanNapTienModel = require('../models/HuongDanNapTienModel');
const NotificationModel = require('../models/NotificationModel');
const RechargeModel = require('../models/RechargeModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookRoomModel = require('../models/BookRoomModel');
const BookSpaModel = require('../models/BookSpaModel');
const FundLogModel = require('../models/FundLogModel');
const HoTroModel = require('../models/HoTroModel');
const ProducModel = require('../models/ProducModel');
const CouponModel = require('../models/CouponModel');
const CodePasswordModel = require('../models/CodePasswordModel');
const CodeEmailModel = require('../models/CodeEmailModel');
const WalletOnlineModel = require('../models/WalletOnlineModel');
const PayingWalletLogModel = require('../models/PayingWalletLogModel');
const FcmTokensModel = require('../models/FcmTokensModel');
const MessageModel = require('../models/MessageModel');
const UserSettingModel = require('../models/UserSettingModel');
const VpnPayModel = require('../models/VpnPayModel');
const promise = require('bluebird');
const UserSession = require('../session/UserSession');
const BannerAppModel = require('../models/BannerAppModel');
const AdsStoreModel = require('../models/AdsStoreModel');
const BranchModel = require('../models/BranchModel');
const validate = require('validate.js')
const UserPointLogModel = require("../models/UserPointLogModel");
const DataSearchModel = require("../models/DataSearchModel");
const {TypeServices} = require("../models/enum/TypeServices");
const GHTK = require("../../www/utils/GiaoHang/ghtk");
const {ProductOrderStatus} = require("../models/enum/ProductOrderStatus");
const BookClassificationModel = require("../models/BookClassificationModel");
const ClassificationOfBrandModel = require("../models/ClassificationOfBrandModel");

module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/admin');
    }

    registerRouting(io) {
        let dateTimes = [];
        for (let i = 0; i <= 23; i++) {
            let time = '';
            if (i < 10) {
                time += `0${i} : `
            } else {
                time += `${i} : `
            }
            dateTimes.push(time + '00');
            dateTimes.push(time + '30')
        }

        return {
            '/': {
                config: {
                    auth: [this.roles.admin],
                    get: 'view',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.redirect(res, '/admin/cua-hang.html')
                    }],
                },
            },

            '/cua-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/cua-hang.ejs',
                    title: 'Cửa hàng',
                    get: 'view',
                },

                methods: {
                    get: [async function (req, res) {
                        let users = await UserModel.MODEL.getAllUsersByCondition({type: 1});
                        return ChildRouter.renderOrResponse(req, res, {users});
                    }],
                },
            },

            '/change-store-status-user/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([0, 1, 2].includes(status)) {
                            if (status == 1) {
                                // Khoá các service
                                ServicesModel.MODEL.updateWhereClause({
                                    userId: req.params.userId,
                                    status: 0
                                }, {status: 1})
                            } else {
                                // mở khoá các store
                                ServicesModel.MODEL.updateWhereClause({
                                    userId: req.params.userId,
                                    status: 1
                                }, {status: 0})
                            }

                            await UserModel.MODEL.updateUser(req.params.userId.trim(), {statusStore: status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            '/chinh-sua-yeu-cau/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-yeu-cau.ejs',
                    get: 'view',
                    title: 'Thông tin chủ kinh doanh',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'servicesStore', type: this.dataType.number, name: 'Cửa hàng'},
                                {key: 'servicesExamination', type: this.dataType.number, name: 'Xưởng dịch vụ'},
                                {key: 'servicesParking', type: this.dataType.number, name: 'Điểm gửi xe'},
                                {key: 'servicesSpa', type: this.dataType.number, name: 'Xưởng dịch vụ'},
                                {key: 'servicesShowroom', type: this.dataType.number, name: 'Showroom'},
                                {key: 'servicesGas', type: this.dataType.number, name: 'Trạm xăng'},
                                {key: 'fee', type: this.dataType.number, name: 'Tính phí'},
                                {key: 'funds', type: this.dataType.string, name: 'Nhập quỹ', min: 3},
                                {key: 'wallet', type: this.dataType.string, name: 'Số dư online', min: 3},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let dataUser = await UserModel.MODEL.getUsersById(req.params.userId);
                        return ChildRouter.renderOrResponse(req, res, {dataUser});

                    }],

                    post: [async function (req, res) {
                        let {
                            servicesStore,
                            servicesExamination,
                            // servicesHotel,
                            servicesParking,
                            servicesSpa,
                            servicesShowroom,
                            servicesGas,
                            fee,
                            funds,
                            wallet
                        } = req.body;
                        funds = funds || '';
                        wallet = wallet || '';
                        wallet = Number(wallet.replace("VND", '').split(",").join("").trim());
                        funds = Number(funds.replace("VND", '').split(",").join("").trim());

                        if (Number(servicesStore) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 0,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 0,
                                status: 1
                            }, {status: 0})
                        }


                        if (Number(servicesExamination) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 1,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 1,
                                status: 1
                            }, {status: 0})
                        }

                        if (Number(servicesParking) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 2,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 2,
                                status: 1
                            }, {status: 0})
                        }

                        if (Number(servicesSpa) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 3,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 3,
                                status: 1
                            }, {status: 0})
                        }

                        if (Number(servicesShowroom) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 4,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 4,
                                status: 1
                            }, {status: 0})
                        }


                        if (Number(servicesGas) == 0) {
                            // Mở
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 5,
                                status: 0
                            }, {status: 1})
                        } else {
                            // Đóng
                            ServicesModel.MODEL.updateWhereClause({
                                userId: req.params.userId,
                                type: 5,
                                status: 1
                            }, {status: 0})
                        }

                        await UserModel.MODEL.updateUser(req.params.userId, {
                            servicesStore,
                            servicesExamination,
                            servicesParking,
                            servicesSpa,
                            servicesShowroom,
                            servicesGas,
                            fee,
                            funds,
                            wallet
                        });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/nap-tien-cho-nguoi-kinh-doanh/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'price', type: this.dataType.number, name: 'Số tiền nạp'},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {price} = req.body;
                        await UserModel.MODEL.updateUser(req.params.userId, {$inc: {funds: price}})
                        await RechargeModel.MODEL.addRecharge({userId: req.params.userId, count: price})
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/quan-ly-nguoi-dung.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-nguoi-dung.ejs',
                    title: 'Người dùng',
                    get: 'view',
                },

                methods: {
                    get: [async function (req, res) {
                        let users = await UserModel.MODEL.getAllUsersByCondition({type: 2})
                        return ChildRouter.renderOrResponse(req, res, {users});
                    }],
                },
            },
            '/user-create.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/user-add.ejs',
                    title: 'Tạo tài khoản',
                    get: 'view',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Tên người dùng', min: 5},
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại', min: 5},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                    post: [async function (req, res) {
                        let {fullName, phone, password} = req.body;
                        let obj = {
                            fullName,
                            phone,
                            password: StringUtils.md5(password),
                            status: 1, // Active luôn
                            type: 2, //Khách hàng
                        }
                        let getUser = await UserModel.MODEL.getOneUsersByCondition({phone})
                        console.log(getUser);
                        if (getUser !== null) {
                            return ChildRouter.responseError("Tài khoản đã tồn tại", res);
                        } else {
                            let result = await UserModel.MODEL.addUser(obj)
                            return ChildRouter.responseSuccess("Thành công", res);
                        }

                    }]
                },
            },

            '/change-status-user/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([1, 2].includes(status)) {
                            if (status == 2) {
                                // Khoá các service
                                ServicesModel.MODEL.updateWhereClause({
                                    userId: req.params.userId,
                                    status: 1
                                }, {status: 0})
                            } else {
                                // mở khoá các store
                                ServicesModel.MODEL.updateWhereClause({
                                    userId: req.params.userId,
                                    status: 0
                                }, {status: 1})
                            }
                            await UserModel.MODEL.updateUser(req.params.userId.trim(), {status: status});
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            '/change-status-email/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await UserModel.MODEL.updateUser(req.params.userId.trim(), {confirmEmail: 1});
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/delete-user/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await UserModel.MODEL.deleteUser(req.params.userId)
                        await ServicesModel.MODEL.deleteServicesByCondition({userId: req.params.userId})
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/chinh-sua-nguoi-dung/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-nguoi-dung.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa người dùng',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 200},
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'address', type: this.dataType.string, name: 'Địa chỉ', min: 5, max: 200},
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại', min: 5, max: 200},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let dataUser = await UserModel.MODEL.getUsersById(req.params.userId)
                        return ChildRouter.renderOrResponse(req, res, {dataUser});

                    }],

                    post: [async function (req, res) {
                        let {fullName, email, address, phone} = req.body;
                        let rsl = await UserModel.MODEL.updateUser(req.params.userId, {fullName, email, address, phone})
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/quan-ly-danh-muc.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-danh-muc.ejs',
                    get: 'view',
                    title: 'Danh mục',
                },

                methods: {
                    get: [async function (req, res) {
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({status: 1});
                        return ChildRouter.renderOrResponse(req, res, {categories});
                    }],
                },
            },

            '/them-moi-danh-muc.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/them-moi-danh-muc.ejs',
                    get: 'view',
                    title: 'Thêm mới danh mục',
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên danh mục', min: 5, max: 200},
                                {key: 'typeCategory', type: this.dataType.number, name: 'Danh mục cấp cha'},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {name, typeCategory} = req.body;
                        if (req.upload && req.upload.picture) {
                            await CategoryModel.MODEL.addCategory({
                                name,
                                type: typeCategory,
                                picture: req.upload.picture[0].path
                            });
                        } else {
                            return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/chinh-sua-danh-muc/:categoryId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-danh-muc.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa danh mục',
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên danh mục', min: 5, max: 200},
                                {key: 'typeCategory', type: this.dataType.number, name: 'Danh mục cấp cha'},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let category = await CategoryModel.MODEL.getCategoryById(req.params.categoryId)
                        if (category) {
                            return ChildRouter.renderOrResponse(req, res, {category});
                        } else {
                            return ChildRouter.redirect(res, `/admin/quan-ly-danh-muc.html`);
                        }
                    }],

                    post: [async function (req, res) {
                        let {name, typeCategory} = req.body;
                        if (req.upload && req.upload.picture) {
                            let category = await CategoryModel.MODEL.getCategoryById(req.params.categoryId)
                            FileUtils.deleteFile(APP.BASE_DIR + category.picture)
                            await CategoryModel.MODEL.updateCategory(req.params.categoryId, {
                                name,
                                type: typeCategory,
                                picture: req.upload.picture[0].path
                            });
                        } else {
                            await CategoryModel.MODEL.updateCategory(req.params.categoryId, {name, type: typeCategory});
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/upload-ckeditor.html': {
                config: {
                    auth: [this.roles.admin, this.roles.store],
                    post: 'json',
                    upload: [{name: 'upload', maxCount: 1}],
                },
                methods: {
                    post: [function (req, res) {
                        let html = {
                            "uploaded": 1,
                            "fileName": req.files.upload[0].filename,
                            "url": req.files.upload[0].linkUrl
                        };
                        return res.send(html);
                    }]
                },
            },

            '/upload-multi-image.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                    upload: [{name: 'pictures', maxCount: 50}]
                },
                methods: {
                    post: [function (req, res) {
                        let images = req.files.pictures.map(p => {
                            return '/' + p.path;
                        });
                        return ChildRouter.responseSuccess("Thành công", res, {
                            images
                        });
                    }]
                },
            },

            '/quan-ly-tin-tuc.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-tin-tuc.ejs',
                    get: 'view',
                    title: 'Tin tức',
                },

                methods: {
                    get: [async function (req, res) {
                        let dataNews = await NewsModel.MODEL.getNewsByCondition({})
                        return ChildRouter.renderOrResponse(req, res, {dataNews});
                    }],
                },
            },

            '/them-moi-tin-tuc.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/them-moi-tin-tuc.ejs',
                    get: 'view',
                    title: 'Thêm mới tin tức',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {title, description, content} = req.body;
                        let obj = {
                            title,
                            description,
                            content,
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        } else {
                            return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
                        }
                        await NewsModel.MODEL.addNews(obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/chinh-sua-tin-tuc/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-tin-tuc.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa tin tức',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await NewsModel.MODEL.getNewsById(req.params.newsId)
                        return ChildRouter.renderOrResponse(req, res, {news});
                    }],

                    post: [async function (req, res) {
                        let {title, description, content} = req.body;
                        let obj = {
                            title,
                            description,
                            content,
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        await NewsModel.MODEL.updateNews(req.params.newsId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/quan-ly-banner-app.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-banner-app.ejs',
                    get: 'view',
                    title: 'Banner',
                },

                methods: {
                    get: [async function (req, res) {
                        let dataNews = await BannerAppModel.MODEL.getBannerByCondition()
                        return ChildRouter.renderOrResponse(req, res, {dataNews});
                    }],
                },
            },

            '/them-banner-app.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/them-banner-app.ejs',
                    get: 'view',
                    title: 'Thêm banner App',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'params', type: this.dataType.string, name: 'Link hoặc ID', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {name, link, screen, params, order} = req.body;
                        let obj = {
                            name,
                            link,
                            screen,
                            params,
                            order
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        } else {
                            return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
                        }
                        await BannerAppModel.MODEL.addBanner(obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/chinh-sua-banner-app/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-banner-app.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa banner',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'params', type: this.dataType.string, name: 'Link hoặc ID', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await BannerAppModel.MODEL.getBannerById(req.params.newsId)

                        return ChildRouter.renderOrResponse(req, res, {news});
                    }],

                    post: [async function (req, res) {
                        let {name, link, screen, params, order, province} = req.body;
                        let obj = {
                            name,
                            link,
                            screen,
                            params,
                            order,
                        }
                        if (province !== undefined) {
                            obj.province = province
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        //
                        await BannerAppModel.MODEL.updateBanner(req.params.newsId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/delete-banner/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await BannerAppModel.MODEL.getBannerById(req.params.newsId)
                        FileUtils.deleteFile(APP.BASE_DIR + news.thumbail)
                        await BannerAppModel.MODEL.deleteBanner(req.params.newsId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/change-status-banner/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query
                        status = Number(status)
                        if ([0, 1].includes(status)) {
                            await BannerAppModel.MODEL.updateBanner(req.params.newsId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            // Banner App store
            '/quan-ly-banner-store.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-banner-store.ejs',
                    get: 'view',
                    title: 'Banner',
                },

                methods: {
                    get: [async function (req, res) {
                        let dataNews = await AdsStoreModel.MODEL.getBannerByCondition({}, null, {
                            order: 1
                        })
                        return ChildRouter.renderOrResponse(req, res, {dataNews});
                    }],
                },
            },

            '/them-banner-store.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/them-banner-store.ejs',
                    get: 'view',
                    title: 'Thêm banner App',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'params', type: this.dataType.string, name: 'Link hoặc ID', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {name, tag, link, screen, params, order} = req.body;
                        let obj = {
                            name,
                            tag,
                            link,
                            screen,
                            params,
                            order
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        } else {
                            return ChildRouter.responseError('Cần tải lên đủ ảnh', res);
                        }
                        await AdsStoreModel.MODEL.addBanner(obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/chinh-sua-banner-store/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-banner-store.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa banner',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'params', type: this.dataType.string, name: 'Link hoặc ID', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await AdsStoreModel.MODEL.getBannerById(req.params.newsId)

                        return ChildRouter.renderOrResponse(req, res, {news});
                    }],

                    post: [async function (req, res) {
                        let {name, tag, link, screen, params, order, province} = req.body;
                        let obj = {
                            name,
                            tag,
                            link,
                            screen,
                            params,
                            order,
                        }
                        if (req.upload && req.upload.thumbail) {
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        if (province !== undefined) {
                            obj.province = province
                        }
                        //
                        await AdsStoreModel.MODEL.updateBanner(req.params.newsId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/delete-banner-store/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await AdsStoreModel.MODEL.getBannerById(req.params.newsId)
                        FileUtils.deleteFile(APP.BASE_DIR + news.thumbail)
                        await AdsStoreModel.MODEL.deleteBanner(req.params.newsId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/change-status-banner-store/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query
                        status = Number(status)
                        if ([0, 1].includes(status)) {
                            await AdsStoreModel.MODEL.updateBanner(req.params.newsId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            // News
            '/change-status-news/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query
                        status = Number(status)
                        if ([0, 1].includes(status)) {
                            await NewsModel.MODEL.updateNews(req.params.newsId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            '/delete-news/:newsId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let news = await NewsModel.MODEL.getNewsById(req.params.newsId)
                        FileUtils.deleteFile(APP.BASE_DIR + news.thumbail)
                        await NewsModel.MODEL.deleteNews(req.params.newsId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/delete-category/:categoryId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let category = await CategoryModel.MODEL.getCategoryById(req.params.categoryId)
                        //TODO: change cloud storage thì mở để xoá file
                        // FileUtils.deleteFile(APP.BASE_DIR + category.picture)
                        await CategoryModel.MODEL.deleteCategory(req.params.categoryId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/setting-admin.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/setting-admin.ejs',
                    get: 'view',
                    title: 'Cài đặt',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.email, name: 'Email', min: 5, max: 50},
                                {key: 'username', type: this.dataType.username, name: 'Username', min: 5, max: 50},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5},

                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let setting = await SettingAdminModel.MODEL.getSettingAdmin();
                        return ChildRouter.renderOrResponse(req, res, {setting});
                    }],

                    post: [async function (req, res) {
                        let {email, password, username} = req.body;
                        await SettingAdminModel.MODEL.updateSettingAdmin({email, username, password});
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/ho-tro.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/ho-tro.ejs',
                    get: 'view',
                    title: 'Hỗ trợ',
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let posts = await HoTroModel.MODEL.getPosts();
                        return ChildRouter.renderOrResponse(req, res, {...posts});
                    }],

                    post: [async function (req, res) {
                        await HoTroModel.MODEL.updatePost(req.body);
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/quan-ly-coupon.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-coupon.ejs',
                    get: 'view',
                    title: 'Quản lý coupon',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res, {});
                    }],
                },
            },

            '/tao-coupon.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/tao-coupon.ejs',
                    get: 'view',
                    title: 'Tạo coupon',
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res, {});
                    }],

                    post: [async function (req, res) {
                        let {
                            startTime,
                            endTime,
                            countBooking,
                            countCoupon,
                            type,
                            value,
                            minBillValue,
                        } = req.body;
                        let typeCode = 0
                        if (!startTime) startTime = -1;
                        if (!endTime) endTime = -1;
                        if (!countBooking) countBooking = -1;
                        if (!countCoupon) countCoupon = 1;
                        type = Number(type);
                        value = Number(value);
                        minBillValue = Number(minBillValue);
                        await CouponModel.MODEL.createCoupon(Number(startTime), Number(endTime), Number(countBooking), Number(countCoupon), type, value, minBillValue, typeCode);
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/sua-coupon.html/:id': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/tao-coupon.ejs',
                    get: 'view',
                    title: 'Sửa coupon',
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let coupon = await CouponModel.MODEL.getDataById(req.params.id);
                        return ChildRouter.renderOrResponse(req, res, {coupon});
                    }],

                    post: [async function (req, res) {
                        let coupon = await CouponModel.MODEL.getDataById(req.params.id);
                        let {
                            startTime,
                            endTime,
                            countBooking,
                            type,
                            value,
                            minBillValue
                        } = req.body;
                        if (!startTime) startTime = -1;
                        if (!endTime) endTime = -1;
                        if (!countBooking) countBooking = -1;
                        let status = 1;
                        if (coupon.currentBooking >= Number(countBooking)) {
                            status = 0;
                        } else {
                            status = 1;

                            if (new Date().getTime() > coupon.endTime) {
                                status = 0;
                            }
                        }

                        await CouponModel.MODEL.updateById(req.params.id, {
                            startTime: Number(startTime),
                            endTime: Number(endTime),
                            countBooking: Number(countBooking),
                            type: Number(type),
                            value: Number(value),
                            minBillValue: Number(minBillValue),
                            status
                        });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/get-coupon-status.html/:status': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let coupons = await CouponModel.MODEL.getCoupponWithStatus(Number(req.params.status));
                        return ChildRouter.responseSuccess("Thanh cong", res, {coupons});
                    }],
                },
            },

            '/change-coupon-status.html/:id/:status': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await CouponModel.MODEL.updateById(req.params.id, {status: req.params.status});
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/setting-admin-fee.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'percent', type: this.dataType.number, name: 'Lợi nhuận', min: 0, max: 100},
                                {key: 'fee', type: this.dataType.string, name: 'Phí dịch vụ', min: 3},
                            ]
                        },
                    },
                },

                methods: {

                    post: [async function (req, res) {
                        let {percent, fee} = req.body;
                        fee = fee || '';
                        fee = Number(fee.replace("VND", '').split(",").join("").trim());
                        await SettingAdminModel.MODEL.updateSettingAdmin({percent, fee});
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/setting-admin-time.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/setting-admin.ejs',
                    get: 'view',
                    title: 'Cài đặt',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'timeMinute', type: this.dataType.number, name: 'Thời gian thông báo'},
                            ]
                        },
                    },
                },

                methods: {

                    post: [async function (req, res) {
                        let {timeMinute} = req.body;
                        await SettingAdminModel.MODEL.updateSettingAdmin({timeMinute})
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/setting-ads.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/cai-dat-quang-cao.ejs',
                    get: 'view',
                    title: 'Cài đặt',
                    post: 'json',
                    upload: [
                        {name: 'homeCode', maxCount: 1},
                        {name: 'shopCode1', maxCount: 1},
                        {name: 'shopCode2', maxCount: 1},
                        {name: 'banner1', maxCount: 1},
                        {name: 'banner2', maxCount: 1},
                        {name: 'banner3', maxCount: 1},
                        {name: 'banner4', maxCount: 1}
                    ]
                },

                methods: {
                    get: [async function (req, res) {
                        let ads = await AdsSettingModel.MODEL.getSettingAds();

                        return ChildRouter.renderOrResponse(req, res, {ads});
                    }],

                    post: [async function (req, res) {
                        let obj = {};
                        if (req.files && req.files.homeCode) {
                            obj.homeCode = '/' + req.files.homeCode[0].path
                        }
                        if (req.files && req.files.shopCode1) {
                            obj.shopCode1 = '/' + req.files.shopCode1[0].path
                        }
                        if (req.files && req.files.shopCode2) {
                            obj.shopCode2 = '/' + req.files.shopCode2[0].path
                        }
                        if (req.files && req.files.banner1) {
                            obj.banner1 = '/' + req.files.banner1[0].path
                        }
                        if (req.files && req.files.banner2) {
                            obj.banner2 = '/' + req.files.banner2[0].path
                        }
                        if (req.files && req.files.banner3) {
                            obj.banner3 = '/' + req.files.banner3[0].path
                        }
                        if (req.files && req.files.banner4) {
                            obj.banner4 = '/' + req.files.banner4[0].path
                        }

                        await AdsSettingModel.MODEL.updateSettingAds(obj);
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/quan-ly-don-hang-tra-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-don-hang-tra-hang.ejs',
                    get: 'view',
                    title: 'Quản lý đơn hàng trả hàng',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                },
            },

            '/lay-danh-sach-don-hang-tra-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page, search, adminConfirm} = req.query;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {}
                        if (search != '') {
                            condition = {
                                $or: [
                                    {userName: new RegExp(search, 'i')},
                                    {"products.name": new RegExp(search, 'i')},
                                    {codeString: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        if (adminConfirm && adminConfirm != 'all') {
                            condition.adminConfirm = Number(adminConfirm)
                        }
                        condition.status = 4;
                        let bills = await RequestBuyProductModel.MODEL.getRequestForPageAdmin(condition, page, limit);
                        let countBill = await RequestBuyProductModel.MODEL.getCountRequestForPage(condition)
                        bills.forEach(bill => {
                            bill.listProduct = '';
                            bill.totalMoney = 0;
                            bill.products.forEach(item => {
                                bill.listProduct += item.name + " ;";
                                bill.totalMoney += Number(item.price) * Number(item.count)
                            })
                        });
                        return ChildRouter.responseSuccess("Thanh cong", res, {bills, countBill});
                    }],
                },
            },

            '/chi-tiet-don-hang-tra-hang/:id.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chi-tiet-don-hang-tra-hang.ejs',
                    get: 'view',
                    title: 'Chi tiết đơn hàng trả hàng',
                },

                methods: {
                    get: [async function (req, res) {
                        let requestBuyId = req.params.id;
                        let info = await RequestBuyProductModel.MODEL.getRequestById(requestBuyId);
                        if (!info || info.status != 4) return ChildRouter.redirect(res, '/admin/quan-ly-don-hang-tra-hang.html');
                        let coupon = null;
                        let giaTriGiam = '0VND';
                        if (info.couponId && info.couponId.trim() != '') {
                            coupon = await CouponModel.MODEL.getDataById(info.couponId);
                            if (coupon) {
                                giaTriGiam = coupon.type == 1 ? coupon.value + '%' : NumberUtils.baseFormatNumber(coupon.value) + ' VNĐ';
                            }
                        }
                        if (!coupon) {
                            coupon = {value: 0, type: 2}
                        }
                        return ChildRouter.renderOrResponse(req, res, {buyInfo: info, giaTriGiam, coupon});
                    }],
                },
            },

            '/quan-ly-doanh-thu.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-doanh-thu.ejs',
                    get: 'view',
                    title: 'Quản lý doanh thu',
                },

                methods: {
                    get: [async function (req, res) {
                        let banks = await AccountBankModel.MODEL.getBankByCondition({userId: req.user._id});
                        let huongDan = await HuongDanNapTienModel.MODEL.getData();
                        return ChildRouter.renderOrResponse(req, res, {banks, huongDan});
                    }],
                },
            },

            '/chinh-sua-huong-dan-nap-tien.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let content = req.body.content || '';
                        await HuongDanNapTienModel.MODEL.updateData({content});
                        return ChildRouter.responseSuccess("Thanh cong", res);
                    }],
                },
            },

            '/lich-su-thu-phi-he-thong.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let fundHistories = await FundLogModel.MODEL.getFundLogByCondition({}, 1000, {createAt: -1});
                        let mapFunc = fundHistories.map((item) => {
                            return new promise(async resolve => {
                                switch (item.typeService) {
                                    case 0:
                                        let request = await RequestBuyProductModel.MODEL.getRequestByIdNoData(item.requestId);
                                        item.storeFullName = (await UserModel.MODEL.getUsersById(request.storeId) || {fullName: 'OLD DB'}).fullName;
                                        item.storeId = request.storeId;
                                        item.code = request.code;
                                        break;
                                    case 1:
                                        let bookExam = await BookExaminationModel.MODEL.getBookExaminationByIdNoData(item.bookId);
                                        item.storeFullName = (await UserModel.MODEL.getUsersById(bookExam.storeManagerId) || {fullName: 'OLD DB'}).fullName
                                        item.storeId = bookExam.storeManagerId;
                                        item.code = bookExam.code;
                                        break;
                                    case 2:
                                        let bookRoom = await BookRoomModel.MODEL.getBookRoomByIdNoData(item.bookId);
                                        item.storeFullName = (await UserModel.MODEL.getUsersById(bookRoom.storeManagerId) || {fullName: 'OLD DB'}).fullName
                                        item.storeId = bookRoom.storeManagerId;
                                        item.code = bookRoom.code;
                                        break;
                                    case 3:
                                        let bookSpa = await BookSpaModel.MODEL.getBookSpayIdNoData(item.bookId);
                                        item.storeFullName = (await UserModel.MODEL.getUsersById(bookSpa.storeManagerId) || {fullName: 'OLD DB'}).fullName
                                        item.storeId = bookSpa.storeManagerId;
                                        item.code = bookSpa.code;
                                        break;
                                }
                                return resolve();
                            })
                        });
                        let walletOnline = null;
                        mapFunc.push(new promise(async resolve => {
                            walletOnline = await WalletOnlineModel.MODEL.getWallet();
                            return resolve();
                        }));
                        if (mapFunc.length > 0) await promise.all(mapFunc);
                        let fundDay = 0;
                        let funYesterday = 0;
                        let funWeek = 0;
                        let funMonth = 0;
                        let funAll = 0;
                        fundHistories.forEach(item => {
                            let timeDay = 86400000;
                            let timeToDay = new Date().setHours(0, 0, 0, 0)
                            let timeDayNow = new Date().getTime();
                            if (item.createAt >= timeToDay && item.createAt < timeDayNow) {
                                fundDay += item.fee
                            }
                            if (item.createAt >= (timeToDay - timeDay) && item.createAt < timeToDay) {
                                funYesterday += item.fee
                            }
                            let dayWeek = new Date().getDay();
                            if (dayWeek == 0) {
                                dayWeek = 7
                            }
                            if (item.createAt >= (timeToDay - (dayWeek - 1) * timeDay) && item.createAt < timeDayNow) {
                                funWeek += item.fee
                            }
                            let dayMonth = new Date().getDate();
                            if (item.createAt >= (timeToDay - (dayMonth - 1) * timeDay) && item.createAt < timeDayNow) {
                                funMonth += item.fee
                            }
                            funAll += item.fee
                        });
                        return ChildRouter.responseSuccess('Thành công', res, {
                            fundHistories,
                            fundDay,
                            funYesterday,
                            funWeek,
                            funMonth,
                            funAll,
                            walletOnline
                        });

                    }],
                },
            },


            '/lich-su-nap-tien.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let recharges = await RechargeModel.MODEL.getRechargeByConditionForSystem();

                        let fundDay = 0;
                        let funYesterday = 0;
                        let funWeek = 0;
                        let funMonth = 0;
                        let funAll = 0;
                        recharges.forEach(item => {
                            let timeDay = 86400000;
                            let timeToDay = new Date().setHours(0, 0, 0, 0)
                            let timeDayNow = new Date().getTime()
                            if (item.createAt >= timeToDay && item.createAt < timeDayNow) {
                                fundDay += item.count
                            }
                            if (item.createAt >= (timeToDay - timeDay) && item.createAt < timeToDay) {
                                funYesterday += item.count
                            }
                            let dayWeek = new Date().getDay()
                            if (dayWeek == 0) {
                                dayWeek = 7
                            }
                            if (item.createAt >= (timeToDay - (dayWeek - 1) * timeDay) && item.createAt < timeDayNow) {
                                funWeek += item.count
                            }
                            let dayMonth = new Date().getDate();
                            if (item.createAt >= (timeToDay - (dayMonth - 1) * timeDay) && item.createAt < timeDayNow) {
                                funMonth += item.count
                            }
                            funAll += item.count
                        });


                        return ChildRouter.responseSuccess("Thanh cong", res, {
                            recharges,
                            fundDay,
                            funYesterday,
                            funWeek,
                            funMonth,
                            funAll
                        });
                    }],
                },
            },


            '/lay-thong-tin-phi-chu-shop.html/:userId': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let userId = req.params.userId;
                        let fundHistories = await FundLogModel.MODEL.getFundLogByCondition({userId})
                        let mapFunc = fundHistories.map((item) => {
                            return new promise(async resolve => {
                                switch (item.typeService) {
                                    case 0:
                                        let request = await RequestBuyProductModel.MODEL.getRequestByIdNoData(item.requestId)
                                        item.code = request.code
                                        break;
                                    case 1:
                                        let bookExam = await BookExaminationModel.MODEL.getBookExaminationByIdNoData(item.bookId)
                                        item.code = bookExam.code
                                        break;
                                    case 2:
                                        let bookRoom = await BookRoomModel.MODEL.getBookRoomByIdNoData(item.bookId)
                                        item.code = bookRoom.code
                                        break;
                                    case 3:
                                        let bookSpa = await BookSpaModel.MODEL.getBookSpayIdNoData(item.bookId)
                                        item.code = bookSpa.code
                                        break;
                                }
                                return resolve();
                            })
                        });

                        if (mapFunc.length > 0) await promise.all(mapFunc)
                        let fundDay = 0
                        let funYesterday = 0
                        let funWeek = 0
                        let funMonth = 0
                        let funAll = 0;
                        fundHistories.forEach(item => {
                            let timeDay = 86400000
                            let timeToDay = new Date().setHours(0, 0, 0, 0)
                            let timeDayNow = new Date().getTime()
                            if (item.createAt >= timeToDay && item.createAt < timeDayNow) {
                                fundDay += item.fee
                            }
                            if (item.createAt >= (timeToDay - timeDay) && item.createAt < timeToDay) {
                                funYesterday += item.fee
                            }
                            let dayWeek = new Date().getDay()
                            if (dayWeek == 0) {
                                dayWeek = 7
                            }
                            if (item.createAt >= (timeToDay - (dayWeek - 1) * timeDay) && item.createAt < timeDayNow) {
                                funWeek += item.fee
                            }
                            let dayMonth = new Date().getDate()
                            if (item.createAt >= (timeToDay - (dayMonth - 1) * timeDay) && item.createAt < timeDayNow) {
                                funMonth += item.fee
                            }
                            funAll += item.fee
                        })
                        return ChildRouter.responseSuccess('Thành công', res, {
                            fundHistories,
                            fundDay,
                            funYesterday,
                            funWeek,
                            funMonth,
                            funAll
                        });

                    }],
                },
            },

            '/lay-thong-thu-nhap-chu-shop.html/:userId': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let userId = req.params.userId;

                        let services = await ServicesModel.MODEL.getServicesByCondition({
                            userId: userId,
                            // businessTypes: {$in: ['1','2','3']}
                            type: {$in: [1, 2, 3]}
                        });
                        let listServiceBytype = {
                            1: [],
                            2: [],
                            3: [],
                        };
                        services.forEach(item => {
                            listServiceBytype[item.type].push(item._id.toString())
                            listServiceBytype[item._id] = item
                        });
                        let bookExams = [], bookRooms = [], bookSpas = [], requests = [], funcs = [];

                        funcs.push(new promise(async resolve => {
                            bookExams = await BookExaminationModel.MODEL.getBookExaminationByConditionDetail({
                                storeId: {$in: listServiceBytype[1]},
                                status: 3
                            }, {timeCheckIn: -1});
                            return resolve();
                        }));

                        funcs.push(new promise(async resolve => {
                            bookRooms = await BookRoomModel.MODEL.getBookRoomByConditionDetail({
                                storeId: {$in: listServiceBytype[2]},
                                status: 3
                            }, {timeCheckIn: -1})
                            return resolve();
                        }));

                        funcs.push(new promise(async resolve => {
                            bookSpas = await BookSpaModel.MODEL.getBookSpaByConditionDetail({
                                storeId: {$in: listServiceBytype[3]},
                                status: 3
                            }, {timeCheckIn: -1})
                            return resolve();
                        }));

                        funcs.push(new promise(async resolve => {
                            requests = await RequestBuyProductModel.MODEL.getRequestByCondition({
                                status: ProductOrderStatus.COMPLETED,
                                storeUserId: userId
                            });
                            return resolve();
                        }));


                        await promise.all(funcs);

                        let thuNhaps = [];
                        bookExams.forEach(item => {
                            thuNhaps.push({
                                code: item.code,
                                price: item.price,
                                type: 1,
                                timeCheckIn: item.timeCheckIn,
                                id: item._id
                            })
                        });
                        bookRooms.forEach(item => {
                            thuNhaps.push({
                                code: item.code,
                                price: item.price,
                                type: 2,
                                timeCheckIn: item.timeCheckIn,
                                id: item._id
                            })
                        });
                        bookSpas.forEach(item => {
                            thuNhaps.push({
                                code: item.code,
                                price: item.price,
                                type: 3,
                                timeCheckIn: item.timeCheckIn,
                                id: item._id
                            })
                        });
                        requests.forEach(item => {
                            let totalMoney = 0
                            item.products.forEach(item => {
                                totalMoney += Number(item.count) * Number(item.price)
                            });
                            thuNhaps.push({
                                code: item.code,
                                price: totalMoney,
                                type: 0,
                                timeCheckIn: item.createAt,
                                id: item._id
                            })
                        });

                        let fundDay = 0
                        let funYesterday = 0
                        let funWeek = 0
                        let funMonth = 0
                        let funAll = 0;
                        thuNhaps.forEach(item => {
                            let timeDay = 86400000
                            let timeToDay = new Date().setHours(0, 0, 0, 0)
                            let timeDayNow = new Date().getTime()
                            if (item.timeCheckIn >= timeToDay && item.timeCheckIn < timeDayNow) {
                                fundDay += item.price
                            }
                            if (item.timeCheckIn >= (timeToDay - timeDay) && item.timeCheckIn < timeToDay) {
                                funYesterday += item.price
                            }
                            let dayWeek = new Date().getDay()
                            if (dayWeek == 0) {
                                dayWeek = 7
                            }
                            if (item.timeCheckIn >= (timeToDay - (dayWeek - 1) * timeDay) && item.timeCheckIn < timeDayNow) {
                                funWeek += item.price
                            }
                            let dayMonth = new Date().getDate()
                            if (item.timeCheckIn >= (timeToDay - (dayMonth - 1) * timeDay) && item.timeCheckIn < timeDayNow) {
                                funMonth += item.price
                            }
                            funAll += item.price
                        })
                        return ChildRouter.responseSuccess('Thành công', res, {
                            thuNhaps,
                            fundDay,
                            funYesterday,
                            funWeek,
                            funMonth,
                            funAll
                        });

                    }],
                },
            },

            '/lay-thong-tin-nap-tien-chu-shop.html/:userId': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let userId = req.params.userId;
                        let recharges = await RechargeModel.MODEL.getRechargeByCondition({userId});
                        return ChildRouter.responseSuccess('Thành công', res, {recharges});

                    }],
                },
            },

            '/quan-ly-thong-bao-da-gui.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-thong-bao-da-gui.ejs',
                    get: 'view',
                    title: 'Quản lý thông báo đã gửi',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'typeUser', type: this.dataType.number, name: 'Người nhận', min: 0},
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 1, max: 65},
                                {key: 'content', type: this.dataType.string, name: 'Nội dung', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {
                            typeUser,
                            userIds,
                            title,
                            content,
                            typeOs,
                            typeNotify,
                            version,
                            screen_open,
                            screen_param,
                            screen_param_spaId,
                            buttonText
                        } = req.body;
                        var userReceives = [], users = [];

                        // 0: Tất cả
                        // 1: Ios
                        // 2: Android
                        typeOs = typeOs | 0;

                        let fcmType = 2 // 2 app người dùng, 1 app chủ shop

                        // Combine the new fields into extraData
                        let extraData = screen_open && screen_param ? JSON.stringify({
                            screen_open: screen_open,
                            screen_param: screen_param,
                            screen_param_spaId: screen_param_spaId,
                            buttonText: buttonText
                        }) : ""


                        //TODO: dump để test
                        // typeUser = 9
                        // userIds = ['63ef56ecf5a65a7530a3c355']

                        switch (Number(typeUser)) {
                            case 0: // người dùng
                                users = await UserModel.MODEL.getAllUsersByCondition({type: 2})
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                });
                                break;
                            case 1:
                                fcmType = 1 // chủ shop
                                users = await UserModel.MODEL.getAllUsersByCondition({type: 1})
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                })
                                break;
                            case 2:
                                fcmType = 1 // chủ shop
                                users = await UserModel.MODEL.getAllUsersByCondition({type: 1, fee: 0})
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                });
                                break;
                            case 3:
                                fcmType = 1 // chủ shop
                                users = await UserModel.MODEL.getAllUsersByCondition({
                                    businessTypes: {$in: ['1']},
                                    fee: 1
                                })
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                });
                                break;
                            case 4:
                                fcmType = 1 // chủ shop
                                let shops = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['0']}});
                                shops.forEach(item => {
                                    if (!userReceives.includes(item.userId)) {
                                        userReceives.push(item.userId)
                                    }
                                });
                                break;
                            case 5:
                                fcmType = 1 // chủ shop
                                let examinations = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['1']}})
                                examinations.forEach(item => {
                                    if (!userReceives.includes(item.userId)) {
                                        userReceives.push(item.userId)
                                    }
                                })
                                break;
                            case 6:
                                fcmType = 1 // chủ shop
                                let rooms = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['2']}})
                                rooms.forEach(item => {
                                    if (!userReceives.includes(item.userId)) {
                                        userReceives.push(item.userId)
                                    }
                                })
                                break;
                            case 7:
                                fcmType = 1 // chủ shop
                                let spas = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['3']}})
                                spas.forEach(item => {
                                    if (!userReceives.includes(item.userId)) {
                                        userReceives.push(item.userId)
                                    }
                                });
                                break;
                            case 8: // gửi thông báo cho tất cả
                                fcmType = 0 // tất cả
                                users = await UserModel.MODEL.getAllUsersByCondition({})
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                });
                                break;
                            case 9: // người dùng cụ thể
                                if (!Array.isArray(userIds)) {
                                    userIds = [userIds]
                                }
                                users = await UserModel.MODEL.getUsersInIds(userIds)
                                users.forEach(item => {
                                    userReceives.push(item._id)
                                });
                                break;
                        }
                        //TODO: chưa hiểu nên comment lại
                        let notify = await NotificationModel.MODEL.addNewNotification(req.user._id, req.user._id, title, 12, io, {
                            message: content,
                            typeUser,
                            watched: 1,
                            typeOs,
                            typeNotify,
                            version,
                            fcmType,
                            extraData
                        });

                        // trường hợp gửi cho nhiều người thì danh sách user nhận sẽ là 1 mảng
                        for (const item of userReceives) {
                            const userGetNotifId = item.toString()
                            const create = await NotificationModel.MODEL.addNewNotification(userGetNotifId, req.user._id, title, 12, io, {
                                message: content,
                                // typeUser,
                                parentId: notify._id.toString(),
                                typeOs,
                                typeNotify,
                                version,
                                fcmType,
                                extraData
                                // watched: 1
                            })
                        }

                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/sua-thong-bao-da-gui/:notificationId.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', max: 65},
                                {key: 'content', type: this.dataType.string, name: 'Nội dung'},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {title, content} = req.body;
                        NotificationModel.MODEL.updateNotification(req.params.notificationId, {
                            message: content,
                            title
                        })
                        NotificationModel.MODEL.updateNotificationByCondition({parentId: req.params.notificationId}, {
                            message: content,
                            title
                        })
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/delete-notification/:notificationId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        NotificationModel.MODEL.deleteNotification(req.params.notificationId)
                        NotificationModel.MODEL.deleteNotificationByCondition({parentId: req.params.notificationId})
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/delete-notifications.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {listIds} = req.body
                        listIds.forEach(item => {
                            NotificationModel.MODEL.deleteNotification(item)
                            NotificationModel.MODEL.deleteNotificationByCondition({parentId: item})
                        })
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/lay-du-lieu-thong-bao-da-gui.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let notifications = await NotificationModel.MODEL.getAllNotificationsByUserId({
                            userId: req.user._id,
                            type: 12
                        }, {createAt: -1})
                        return ChildRouter.responseSuccess("Thành công", res, {notifications})
                    }],
                },
            },


            '/lay-thong-tin-doanh-thu/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let userId = req.params.userId
                        let services = await ServicesModel.MODEL.getServicesByCondition({
                            userId,
                            // businessTypes: {$in: ['1','2','3']}
                            type: {$in: [1, 2, 3]}
                        });
                        let listServiceBytype = {
                            1: [],
                            2: [],
                            3: [],
                        }
                        services.forEach(item => {
                            listServiceBytype[item.type].push(item._id.toString())
                            listServiceBytype[item._id] = item
                        })
                        let bookExams = await BookExaminationModel.MODEL.getBookExaminationByConditionDetail({
                            storeId: {$in: listServiceBytype[1]},
                            status: 3
                        }, {timeCheckIn: -1})
                        let bookRooms = await BookRoomModel.MODEL.getBookRoomByConditionDetail({
                            storeId: {$in: listServiceBytype[2]},
                            status: 3
                        }, {timeCheckIn: -1})
                        let bookSpas = await BookSpaModel.MODEL.getBookSpaByConditionDetail({
                            storeId: {$in: listServiceBytype[3]},
                            status: 3
                        }, {timeCheckIn: -1})
                        let requests = await RequestBuyProductModel.MODEL.getRequestByCondition({
                            type: 3,
                            userId
                        })
                        let thuNhaps = []
                        bookExams.forEach(item => {
                            thuNhaps.push({
                                price: item.price,
                            })
                        })
                        bookRooms.forEach(item => {
                            thuNhaps.push({
                                price: item.price,
                            })
                        })
                        bookSpas.forEach(item => {
                            thuNhaps.push({
                                price: item.price,
                            })
                        })
                        requests.forEach(item => {
                            let totalMoney = 0
                            item.products.forEach(item => {
                                totalMoney += Number(item.count) * Number(item.price)
                            })
                            thuNhaps.push({
                                price: totalMoney,
                            })
                        })
                        let doanhThu = 0;
                        thuNhaps.forEach(item => {
                            doanhThu += item.price
                        })
                        return ChildRouter.responseSuccess('Thành công', res, {
                            doanhThu,
                        });

                    }],
                },
            },

            '/chinh-sua-mat-khau-nguoi-dung/:userId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    title: 'Thông tin tài khoản',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'passwordNew',
                                    type: this.dataType.string,
                                    name: 'Nhập mật khẩu hiện tại',
                                    min: 5,
                                    max: 50
                                },
                                {
                                    key: 'passwordCFNew',
                                    type: this.dataType.string,
                                    name: 'Xác nhận mật khẩu mới',
                                    min: 5,
                                    max: 50
                                },
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {passwordNew, passwordCFNew} = req.body;
                        if (passwordNew != passwordCFNew) {
                            return ChildRouter.responseError('Nhập lại mật khẩu không giống', res);
                        }
                        await UserModel.MODEL.updateUser(req.params.userId, {password: passwordNew})
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/reset-count-notification-bill-return.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await UserModel.MODEL.resetNotificationBillReturn(req.user._id, {notificationsBillReturn: 0});
                        let user = req.user;
                        user.notificationsBillReturn = 0;
                        UserSession.saveUser(req.session, user);
                        if (req.version !== 'api') {
                            UserSession.saveUser(req.session, user);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/shop-trung-tam.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/shop-trung-tam.ejs',
                    title: 'Quản lý cửa hàng',
                    get: 'view',
                },

                methods: {
                    get: [async function (req, res) {
                        let {managerId} = req.query;
                        if (!managerId) managerId = '';
                        return ChildRouter.renderToView(req, res, {managerId});
                    }]
                },
            },

            '/tai-danh-sach-cua-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {searchKey, typePet, status, page, limit, typeServices, managerId, isFeatured} = req.body;

                        let condition = {};
                        if (managerId && managerId.trim() != '') {
                            condition.userId = managerId;
                        }
                        if (status != 'all') {
                            if (Number(status) == 0) {
                                condition.status = {$in: [1, 2]}
                            } else {
                                condition.status = 0;
                            }
                        }

                        if (typePet !== undefined && typePet != 'all' && Number(typeServices) < 4) {
                            condition.typePet = {
                                $in: [Number(typePet), 3]
                            }
                        }

                        if (isFeatured != undefined && isFeatured != 'all') {
                            if (Number(isFeatured) == 0) {
                                condition.isFeatured = {$ne: true};
                            } else {
                                condition.isFeatured = true;
                            }
                        }
                        if (typeServices)
                        {
                            condition.businessTypes = { $in: [Number(typeServices)] }
                        }

                        if (searchKey && searchKey.trim().length > 0) {
                            searchKey = searchKey.trim();
                            let code = searchKey.toLowerCase().trim();
                            code = code.trim().replace("ch", '');
                            code = code.trim().replace("pk", '');
                            code = code.trim().replace("ks", '');
                            code = code.trim().replace("spa", '');
                            if (code.trim().length > 0) {
                                if (Number(code.trim())) {
                                    condition['$or'] = [
                                        {code: Number(code.trim())},
                                        {nameUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                        {addressUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                    ]
                                } else {
                                    condition['$or'] = [
                                        {nameUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                        {addressUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                    ]
                                }
                            } else {
                                condition['$or'] = [
                                    {nameUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                    {addressUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
                                ]
                            }
                        }

                        let services = await ServicesModel.MODEL.getServicesByConditionWithPage(condition, Number(page), {createAt: -1}, Number(limit));
                        let mapFunc = services.map((item) => {
                            return new promise(async resolve => {
                                switch (Number(typeServices)) {
                                    case 0:
                                        let request = await RequestBuyProductModel.MODEL.getOneRequestByConditionNoData({shopId: item._id})
                                        if (!request) {
                                            item.canDelete = true
                                        }
                                        break;
                                    case 1:
                                        let bookExam = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({storeId: item._id})
                                        if (!bookExam) {
                                            item.canDelete = true
                                        }
                                        break;
                                    case 2:
                                        let bookRoom = await BookRoomModel.MODEL.getOneBookRoomByCondition({storeId: item._id})
                                        if (!bookRoom) {
                                            item.canDelete = true
                                        }
                                        break;
                                    case 3:
                                        let bookSpa = await BookSpaModel.MODEL.getOneBookSpaByCondition({storeId: item._id})
                                        if (!bookSpa) {
                                            item.canDelete = true
                                        }
                                    case 4:
                                        let showroom = await BookClassificationModel.MODEL.getOneByCondition({storeId: item._id})
                                        if (!showroom) {
                                            item.canDelete = true
                                        }
                                    case 5:
                                        let gas = await BookClassificationModel.MODEL.getOneByCondition({storeId: item._id})
                                        if (!gas) {
                                            item.canDelete = true
                                        }
                                        break;
                                }
                                return resolve();
                            })
                        });
                        if (mapFunc.length > 0) await promise.all(mapFunc)
                        let total = await ServicesModel.MODEL.countDataWhere(condition);
                        return ChildRouter.responseSuccess("success", res, {
                            services,
                            total,
                            page
                        });
                    }]
                },
            },

            '/delete-service/:serviceId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let service = await ServicesModel.MODEL.getServicesById(req.params.serviceId);
                        await ServicesModel.MODEL.updateServices(req.params.serviceId, {status: 0});
                        switch (service.type) {
                            case 0:
                                ProducModel.MODEL.updateWhereClause({storeId: service._id}, {status: 2});
                                break;
                        }
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/delete-all-service/:serviceId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let service = await ServicesModel.MODEL.getServicesById(req.params.serviceId);
                        let checkDelete = null
                        switch (service.type) {
                            case 0:
                                let request = await RequestBuyProductModel.MODEL.getOneRequestByConditionNoData({shopId: service._id})
                                if (!request) {
                                    let products = await ProducModel.MODEL.getProductByConditionNoData({storeId: service._id})
                                    products.forEach(item => {
                                        item.pictures.forEach(picture => {
                                            FileUtils.deleteFile(APP.BASE_DIR + picture)
                                        })
                                        FileUtils.deleteFile(APP.BASE_DIR + item.thumbail)
                                    })
                                    await ProducModel.MODEL.removeProductByCondition({storeId: service._id})
                                    checkDelete = true
                                }
                                break;
                            case 1:
                                let bookExam = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({storeId: service._id})
                                if (!bookExam) {
                                    checkDelete = true
                                }
                                break;
                            case 2:
                                let bookRoom = await BookRoomModel.MODEL.getOneBookRoomByCondition({storeId: service._id})
                                if (!bookRoom) {
                                    checkDelete = true
                                }
                                break;
                            case 3:
                                let bookSpa = await BookSpaModel.MODEL.getOneBookSpaByCondition({storeId: service._id})
                                if (!bookSpa) {
                                    checkDelete = true
                                }
                                break;
                        }
                        if (!checkDelete) {
                            return ChildRouter.responseError('Đã có lỗi xảy ra', res);
                        }
                        service.pictures.forEach(picture => {
                            FileUtils.deleteFile(APP.BASE_DIR + picture)
                        })
                        await ServicesModel.MODEL.deleteServicesById(req.params.serviceId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/show-service/:serviceId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let service = await ServicesModel.MODEL.getServicesById(req.params.serviceId);
                        await ServicesModel.MODEL.updateServices(req.params.serviceId, {status: 1});
                        let condition = { storeId: req.params.serviceId}

                        // shop
                        if (service.businessTypes.includes(TypeServices.SHOP.toString()))
                        {
                            await ProducModel.MODEL.updateWhereClause(condition, {status: 1}); // show sản phẩm đang ẩn
                        }

                        // spa
                        if (service.businessTypes.includes(TypeServices.SPA.toString()))
                        {
                            await ServiceOfSpaModel.MODEL.updateWhereClause(condition, {status: 1});
                        }


                        if (service.businessTypes.includes(TypeServices.HOTEL.toString()))
                        {
                            await RoomOfHotelModel.MODEL.updateWhereClause(condition, {status: 1});
                        }

                        // gara
                        if (service.businessTypes.includes(TypeServices.CLINIC.toString()))
                        {
                            await ServiceOfClinicModel.MODEL.updateWhereClause(condition, {status: 1});
                        }

                        //SHOWROOM
                        if (service.businessTypes.includes(TypeServices.SHOWROOM.toString()))
                        {
                            await ClassificationOfBrandModel.MODEL.updateWhereClause(condition, {status: 1});
                        }

                        //GAS
                        if (service.businessTypes.includes(TypeServices.GAS.toString()))
                        {
                            await ClassificationOfBrandModel.MODEL.updateWhereClause(condition, {status: 1});
                        }

                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/hide-service/:serviceId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let service = await ServicesModel.MODEL.getServicesById(req.params.serviceId);
                        await ServicesModel.MODEL.updateServices(req.params.serviceId, {status: 0});
                        let condition = { storeId: req.params.serviceId }
                        // shop
                        if (service.businessTypes.includes(TypeServices.SHOP.toString()))
                        {
                            await ProducModel.MODEL.updateWhereClause(condition, {status: 0}); // show sản phẩm đang ẩn
                        }

                        // spa
                        if (service.businessTypes.includes(TypeServices.SPA.toString()))
                        {
                            await ServiceOfSpaModel.MODEL.updateWhereClause(condition, {status: 0});
                        }


                        if (service.businessTypes.includes(TypeServices.HOTEL.toString()))
                        {
                            await RoomOfHotelModel.MODEL.updateWhereClause(condition, {status: 0});
                        }

                        // gara
                        if (service.businessTypes.includes(TypeServices.CLINIC.toString()))
                        {
                            await ServiceOfClinicModel.MODEL.updateWhereClause(condition, {status: 0});
                        }

                        //SHOWROOM
                        if (service.businessTypes.includes(TypeServices.SHOWROOM.toString()))
                        {
                            await ClassificationOfBrandModel.MODEL.updateWhereClause(condition, {status: 0});
                        }

                        //GAS
                        if (service.businessTypes.includes(TypeServices.GAS.toString()))
                        {
                            await ClassificationOfBrandModel.MODEL.updateWhereClause(condition, {status: 0});
                        }

                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/chinh-sua-thuong-hieu/:serviceId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/store/brand-edit.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa thương hiệu',
                    upload: [{name: 'thumbnail', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên thương hiệu', min: 5},
                                // {key: 'thumbnail', type: this.dataType.string, name: 'Anh dai dien', min: 5},
                                {key: 'content', type: this.dataType.string, name: 'Giới thiệu'},
                                {key: 'province', type: this.dataType.string, name: 'Tỉnh / Thành phố'},
                                {key: 'district', type: this.dataType.string, name: 'Quận / Huyện'},
                                {key: 'ward', type: this.dataType.string, name: 'Phường / Xã'},
                                {key: 'businessTypes', type: this.dataType.array, name: 'Loại hình kinh doanh', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let dataService = await ServicesModel.MODEL.getDataById(req.params.serviceId);
                        return ChildRouter.renderOrResponse(req, res, {
                            dataService,
                            dateTimes
                        });
                    }],

                    post: [async function (req, res) {
                        let dataService = await ServicesModel.MODEL.getDataById(req.params.serviceId);

                        let {
                            name,
                            content,
                            timeOpen,
                            timeClose,
                            thumbnail,
                            pictureOlds,
                            betweenTime,
                            province,
                            district,
                            ward,
                            street,
                            location,
                            isFeatured,
                            businessTypes,
                            saleServices,
                            saleName,
                            saleValue,
                            saleDate,
                        } = req.body;
                        location ? '' : location = ''
                        street ? '' : street = ''
                        if (location == '' && street == '') {
                            return ChildRouter.responseError('Đường/Phố hoặc Địa chỉ không được rỗng', res);
                        }
                        if (saleDate){
                            saleDate = saleDate.split(" ");
                            saleDate = saleDate[0] + " " + saleDate[1].split('/')[1] + "/" + saleDate[1].split('/')[0] + "/" + saleDate[1].split('/')[2]

                            saleDate = new Date(saleDate).getTime();

                        }

                        let address = `${location} ${street}, ${ward}, ${district}, ${province}`.trim()
                        let obj = {
                            name,
                            address,
                            nameUTF: StringUtils.removeUtf8(name),
                            addressUTF: StringUtils.removeUtf8(address),
                            content,
                            province,
                            district,
                            ward,
                            street,
                            location,
                            provinceUTF: StringUtils.removeUtf8(province),
                            // isFeatured,
                            isFeatured: isFeatured === 'on',
                            businessTypes,
                            saleServices,
                            saleName,
                            saleValue,
                            saleDate,
                        };

                        if (!betweenTime || betweenTime == 'null') {
                            return ChildRouter.responseError('Bạn chưa chọn thời gian phân biệt nửa ngày', res);
                        }
                        obj.betweenTime = betweenTime;

                        if (timeOpen == 'null' || timeClose == 'null') {
                            return ChildRouter.responseError('Thời gian không hợp lệ', res)
                        }

                        obj = {...obj, timeOpen, timeClose};
                        pictureOlds = JSON.parse(pictureOlds);

                        if (req.upload && req.upload.pictures && req.upload.pictures.length > 0) {
                            let imageNews = req.upload.pictures;
                            imageNews.forEach((img, index) => {
                                pictureOlds.push(img.path);
                            });
                        }

                        dataService.pictures.forEach(item => {
                            if (obj.pictures && !obj.pictures.includes(item)) {
                                FileUtils.deleteFile(APP.BASE_DIR + item)
                            }
                        });


                        if (req.upload && req.upload.thumbnail) {
                            obj.thumbnail = req.upload.thumbnail[0].path
                        }
                        let rs = await ServicesModel.MODEL.updateServices(req.params.serviceId, obj);
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/mng-products.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-san-pham.ejs',
                    get: 'view',
                    title: 'Quản lý sản phẩm',
                },

                methods: {
                    get: [async function (req, res) {
                        let {shopId} = req.query;
                        let shops = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['0']}})
                        if (!shopId) shopId = '';
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 0, 1, 2 ] }});
                        return ChildRouter.renderOrResponse(req, res, {categories, shopId, shops});
                    }],
                },
            },
            '/mng-spa-wash.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-dich-vu.ejs',
                    get: 'view',
                    title: 'Quản lý dịch vụ',
                },

                methods: {
                    get: [async function (req, res) {
                        let {shopId} = req.query;
                        let shops = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['3']}})
                        if (!shopId) shopId = '';
                        return ChildRouter.renderOrResponse(req, res, {shopId, shops});
                    }],
                },
            },
            '/change-status-service/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([0, 1, 2].includes(status)) {
                            await ServiceOfSpaModel.MODEL.updateService(req.params.productId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },
            '/lay-danh-sach-san-pham.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, status, shopId} = req.query;
                        let {typeProduct} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {};
                        if (search != '') {
                            condition = {
                                $or: [
                                    {name: new RegExp(search, 'i')},
                                    {storeName: new RegExp(search, 'i')},
                                    {codeText: new RegExp(search, 'i')},
                                    {categoryName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        if (typeProduct && typeProduct != 'all') {
                            condition.typeProduct = Number(typeProduct)
                        }
                        // if (categoryId && categoryId != 'all') {
                        //     condition.categoryId = categoryId
                        // }
                        if (status) {
                            condition.status = Number(status)
                        }
                        if (shopId && shopId.trim() != '' && shopId != 'all') {
                            condition.storeId = shopId;
                        }
                        let {products, countProduct} = await ProducModel.MODEL.adminGetProducts(condition, page, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {products, countProduct});
                    }],
                },
            },
            '/lay-danh-sach-dich-vu.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, status, shopId} = req.query;
                        let {typeService, categoryId} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {};
                        if (search != '') {
                            condition = {
                                $or: [
                                    {name: new RegExp(search, 'i')},
                                    {storeName: new RegExp(search, 'i')},
                                    {codeText: new RegExp(search, 'i')},
                                    {categoryName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        if (typeService && typeService != 'all') {
                            condition.typeService = Number(typeService)
                        }
                        if (categoryId && categoryId != 'all') {
                            condition.categoryId = categoryId
                        }
                        if (status) {
                            condition.status = Number(status)
                        }
                        if (shopId && shopId.trim() != '' && shopId != 'all') {
                            condition.storeId = shopId;
                        }
                        let products = await ServiceOfSpaModel.MODEL.getServicePageByCondition(condition, page, limit);
                        let countProduct = await ServiceOfSpaModel.MODEL.getCountServicePageByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, {products, countProduct});
                    }],
                },
            },

            '/lay-danh-sach-san-pham-dropdown.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        try {
                            console.log('Fetching products for dropdown...');

                            // Sử dụng query đơn giản hơn
                            let products = await ProducModel.MODEL.getDataWhere(
                                { status: 1 }, // Chỉ lấy sản phẩm hiển thị
                                ProducModel.MODEL.FIND_MANY(),
                                { name: 1 } // Sắp xếp theo tên
                            );

                            // Chỉ lấy các field cần thiết và giới hạn số lượng
                            let filteredProducts = products.slice(0, 100).map(p => ({
                                _id: p._id,
                                name: p.name || 'Không có tên',
                                code: p.code || 0
                            }));

                            console.log(`Found ${filteredProducts.length} products`);
                            return ChildRouter.responseSuccess("Thành công", res, { products: filteredProducts });
                        } catch (error) {
                            console.error('Error fetching products for dropdown:', error);
                            return ChildRouter.responseError("Lỗi khi lấy danh sách sản phẩm: " + error.message, res);
                        }
                    }],
                },
            },

            // Test endpoint không cần auth để debug
            '/test-simple.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        try {
                            console.log('Simple test...');
                            return ChildRouter.responseSuccess("Test đơn giản thành công", res, {
                                message: "API hoạt động bình thường",
                                timestamp: new Date().toISOString()
                            });
                        } catch (error) {
                            console.error('Simple test error:', error);
                            return ChildRouter.responseError("Test lỗi: " + error.message, res);
                        }
                    }],
                },
            },

            '/change-status-product/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([0, 1, 2].includes(status)) {
                            await ProducModel.MODEL.updateProducts(req.params.productId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },
            '/edit-product/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chinh-sua-san-pham.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa sản phẩm',
                    upload: [{name: 'thumbail', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'name',
                                    type: this.dataType.string,
                                    name: 'Tên sản phẩm',
                                    min: 5,
                                    max: 200,
                                },
                                {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Cửa hàng',
                                },
                                { key: 'branchId', type: this.dataType.string, name: 'Cơ sở' },
                                {
                                    key: 'categoryId',
                                    type: this.dataType.string,
                                    name: 'Danh mục',
                                },
                                {
                                    key: 'description',
                                    type: this.dataType.string,
                                    name: 'Mô tả',
                                    min: 5,
                                },
                                {
                                    key: 'trademark',
                                    type: this.dataType.string,
                                    name: 'Thương hiệu',
                                    min: 2,
                                    max: 200,
                                },
                                {
                                    key: 'price',
                                    type: this.dataType.number,
                                    name: 'Giá',
                                    min: 5,
                                },
                                {
                                    key: 'typeProduct',
                                    type: this.dataType.number,
                                    name: 'Tình trạng',
                                },
                                {
                                    key: 'transport',
                                    type: this.dataType.string,
                                    name: 'Vận chuyển',
                                    min: 2,
                                },
                                {
                                    key: 'weight',
                                    type: this.dataType.number,
                                    name: 'Khối lượng vận chuyển',
                                },
                            ],
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        // let categories = await CategoryModel.MODEL.getAllCategory();
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 0, 1, 2 ] }});
                        let stores = await ServicesModel.MODEL.getServicesByCondition({
                            userId: req.user._id,
                        });
                        let branch = await BranchModel.MODEL.getBranchById(
                            req.params.branchId
                        );
                        let product = await ProducModel.MODEL.getProductsById(
                            req.params.productId
                        );
                        if (product && !product.classify) product.classify = [];
                        return ChildRouter.renderOrResponse(req, res, {
                            categories,
                            product,
                            stores,
                            branch,
                        });
                    }],

                    post: [
                        async function (req, res) {
                            let {
                                name,
                                storeId,
                                categoryId,
                                branchId,
                                description,
                                trademark,
                                classify,
                                price,
                                priceOld,
                                typeProduct,
                                typeShip,
                                pictureOlds,
                                transport,
                                weight,
                            } = req.body;
                            classify = JSON.parse(classify);

                            // fix bug nếu là 1 cơ sở thì client post string cần convert to array
                            if(!Array.isArray(branchId)){
                                branchId = [branchId]
                            }

                            let obj = {
                                userId: req.user._id,
                                name,
                                nameUTF: StringUtils.removeUtf8(name),
                                descriptionUTF: StringUtils.removeUtf8(description),
                                storeId,
                                branchId,
                                categoryId,
                                description,
                                trademark,
                                price,
                                priceOld,
                                typeProduct,
                                typeShip,
                                classify,
                                transport,
                                weight: Number(weight),
                            };
                            pictureOlds = JSON.parse(pictureOlds);

                            if (categoryId == 'null') {
                                return ChildRouter.responseError('Danh mục không phù hợp', res);
                            }
                            let product = await ProducModel.MODEL.getProductsById(
                                req.params.productId
                            );
                            if (req.upload && req.upload.thumbail) {
                                FileUtils.deleteFile(APP.BASE_DIR + product.thumbail);
                                obj.thumbail = req.upload.thumbail[0].path;
                            }
                            if (
                                req.upload &&
                                req.upload.pictures &&
                                req.upload.pictures.length > 0
                            ) {
                                let pictures = req.upload.pictures;
                                pictures.forEach((img, index) => {
                                    if (!pictureOlds.includes(img))
                                    {
                                        pictureOlds.push(img.path);
                                    }
                                });
                            }
                            obj.pictures = pictureOlds;

                            product.pictures.forEach((item) => {
                                if (obj.pictures && !obj.pictures.includes(item)) {
                                    FileUtils.deleteFile(APP.BASE_DIR + item);
                                }
                            });
                            const rsUpdate = await ProducModel.MODEL.updateProducts(req.params.productId, obj);
                            return ChildRouter.responseSuccess('Thành công', res);
                        },
                    ],
                },
            },

            // Admin Room
            '/mng-parking.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/room/room-list.ejs',
                    get: 'view',
                    title: 'Quản lý dịch vụ',
                },

                methods: {
                    get: [async function (req, res) {
                        let {shopId} = req.query;
                        let shops = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['3']}})
                        if (!shopId) shopId = '';
                        return ChildRouter.renderOrResponse(req, res, {shopId, shops});
                    }],
                },
            },
            '/get-room-list': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, status, shopId} = req.query;
                        let {typeService, categoryId} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {};
                        if (search != '') {
                            condition = {
                                $or: [
                                    {name: new RegExp(search, 'i')},
                                    {storeName: new RegExp(search, 'i')},
                                    {codeText: new RegExp(search, 'i')},
                                    {categoryName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        if (typeService && typeService != 'all') {
                            condition.typeService = Number(typeService)
                        }
                        if (categoryId && categoryId != 'all') {
                            condition.categoryId = categoryId
                        }
                        if (status) {
                            condition.status = Number(status)
                        }
                        if (shopId && shopId.trim() != '' && shopId != 'all') {
                            condition.storeId = shopId;
                        }
                        let products = await RoomOfHotelModel.MODEL.getRoomPageByCondition(condition, page, limit);
                        let countProduct = await RoomOfHotelModel.MODEL.getCountRoomByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, {products, countProduct});
                    }],
                },
            },
            '/change-status-room/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([0, 1, 2].includes(status)) {
                            await RoomOfHotelModel.MODEL.updateRoom(req.params.productId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },
            '/edit-room/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/room/room-edit.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa điểm gửi xe',
                    upload: [{name: 'thumbail', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên phòng', min: 5, max: 200},
                                {key: 'storeId', type: this.dataType.string, name: 'Thương hiệu'},
                                {key: 'branchId', type: this.dataType.string, name: 'Cơ sở'},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                                {key: 'price', type: this.dataType.number, name: 'Giá', min: 5},
                                {key: 'typeService', type: this.dataType.number, name: 'Tình trạng'},
                                {key: 'shortDes', type: this.dataType.string, name: 'Mô tả ngắn'},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let stores = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['2']}})
                        let branch = await BranchModel.MODEL.getBranchById(req.params.branchId);
                        let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                            status: {$ne: 2},
                            // storeId: storeId
                        });
                        let product = await RoomOfHotelModel.MODEL.getRoomById(req.params.productId);
                        if (product && !product.classify) product.classify = [];
                        return ChildRouter.renderOrResponse(req, res, {
                            product,
                            stores,
                            danhsachCoSo,
                            branch
                        });
                    }],

                    post: [async function (req, res) {
                        console.log('Admin edit room')
                        let {
                            name,
                            storeId,
                            description,
                            classify,
                            price,
                            typeService,
                            pictureOlds,
                            shortDes
                        } = req.body;
                        classify = JSON.parse(classify)
                        classify.forEach((item) => {
                            item.data = item?.data.filter(item => (!validate.isEmpty(item.name)) && (!validate.isEmpty(item.price)))
                        })
                        let obj = {
                            userId: req.user._id,
                            name,
                            nameUTF: StringUtils.removeUtf8(name),
                            descriptionUTF: StringUtils.removeUtf8(description),
                            storeId,
                            description,
                            price,
                            typeService,
                            classify,
                            shortDes
                        }

                        let product = await RoomOfHotelModel.MODEL.getRoomById(req.params.productId);
                        if (req.upload && req.upload.thumbail) {
                            FileUtils.deleteFile(APP.BASE_DIR + product.thumbail)
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        await RoomOfHotelModel.MODEL.updateRoom(req.params.productId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },
            // End Admin Room

            // Admin Clinic
            '/quan-ly-dich-vu-kham.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/clinic/clinic-list.ejs',
                    get: 'view',
                    title: 'Quản lý Garage',
                },

                methods: {
                    get: [async function (req, res) {
                        let {shopId} = req.query;
                        let shops = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['3']}})
                        if (!shopId) shopId = '';
                        return ChildRouter.renderOrResponse(req, res, {shopId, shops});
                    }],
                },
            },
            '/get-clinic-list': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, status, shopId} = req.query;
                        let {typeService, categoryId} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {};
                        if (search != '') {
                            condition = {
                                $or: [
                                    {name: new RegExp(search, 'i')},
                                    {storeName: new RegExp(search, 'i')},
                                    {codeText: new RegExp(search, 'i')},
                                    {categoryName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        if (typeService && typeService != 'all') {
                            condition.typeService = Number(typeService)
                        }
                        if (status) {
                            condition.status = Number(status)
                        }
                        if (shopId && shopId.trim() != '' && shopId != 'all') {
                            condition.storeId = shopId;
                        }
                        let products = await ServiceOfClinicModel.MODEL.getClinicPageByCondition(condition, page, limit);
                        let countProduct = await ServiceOfClinicModel.MODEL.getCountClinicByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, {products, countProduct});
                    }],
                },
            },
            '/change-status-clinic/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query;
                        status = Number(status);
                        if ([0, 1, 2].includes(status)) {
                            await ServiceOfClinicModel.MODEL.updateClinic(req.params.productId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            // End Admin Clinic

            '/thanh-toan-online.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/thanh-toan-online.ejs',
                    get: 'view',
                    title: 'Thanh toán online',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                },
            },

            '/tai-vi-he-thong.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let wallet = await WalletOnlineModel.MODEL.getWallet();
                        return ChildRouter.responseSuccess("Thanh cong", res, {wallet});
                    }],
                },
            },

            '/thong-ke-du-lieu.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/thong-ke-du-lieu.ejs',
                    get: 'view',
                    title: 'Thống kê dữ liệu',
                },

                methods: {
                    get: [async function (req, res) {
                        let banks = await AccountBankModel.MODEL.getBankByCondition({userId: req.user._id});
                        let huongDan = await HuongDanNapTienModel.MODEL.getData();
                        return ChildRouter.renderOrResponse(req, res, {banks, huongDan});
                    }],
                },
            },

            '/lay-thong-ke-du-lieu.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let dataObject = {}
                        dataObject.userNormal = await UserModel.MODEL.getAllUsersByCondition({type: 2});
                        dataObject.userStore = await UserModel.MODEL.getAllUsersByCondition({type: 1});
                        dataObject.cuaHang = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['0']}})
                        dataObject.phongKham = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['1']}})
                        dataObject.khachSan = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['2']}})
                        dataObject.Spa = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['3']}})

                        function getObjectDataByDay(arrs) {
                            let obj = {
                                fundDay: 0,
                                funYesterday: 0,
                                funWeek: 0,
                                funMonth: 0,
                                funAll: 0
                            };
                            arrs.forEach(item => {
                                let timeDay = 86400000;
                                let timeToDay = new Date().setHours(0, 0, 0, 0)
                                let timeDayNow = new Date().getTime()
                                if (item.createAt >= timeToDay && item.createAt < timeDayNow) {
                                    obj.fundDay++
                                }
                                if (item.createAt >= (timeToDay - timeDay) && item.createAt < timeToDay) {
                                    obj.funYesterday++;
                                }
                                let dayWeek = new Date().getDay();
                                if (dayWeek == 0) {
                                    dayWeek = 7
                                }
                                if (item.createAt >= (timeToDay - (dayWeek - 1) * timeDay) && item.createAt < timeDayNow) {
                                    obj.funWeek++
                                }
                                let dayMonth = new Date().getDate();
                                if (item.createAt >= (timeToDay - (dayMonth - 1) * timeDay) && item.createAt < timeDayNow) {
                                    obj.funMonth++
                                }
                                obj.funAll++
                            });
                            return obj
                        }

                        let objThongKe = {
                            'nguoi-dung': getObjectDataByDay(dataObject.userNormal),
                            'nguoi-kd': getObjectDataByDay(dataObject.userStore),
                            'cua-hang': getObjectDataByDay(dataObject.cuaHang),
                            'phong-kham': getObjectDataByDay(dataObject.phongKham),
                            'khach-san': getObjectDataByDay(dataObject.khachSan),
                            'spa-service': getObjectDataByDay(dataObject.Spa),
                        };

                        return ChildRouter.responseSuccess("Thanh cong", res, {
                            objThongKe
                        });
                    }],
                },
            },

            '/quan-ly-don-hang-doi-qua.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-don-hang-doi-qua.ejs',
                    get: 'view',
                    title: 'Quản lý đơn hàng đổi quà',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                },
            },

            '/change-don-hang-doi-qua/:id.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let shippingStatus = req.query.status;

                        await UserPointLogModel.MODEL.updateById(req.params.id.trim(), { shippingStatus });
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },

            '/quan-ly-don-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-don-hang.ejs',
                    get: 'view',
                    title: 'Quản lý đơn hàng',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                },
            },

            '/lay-danh-sach-don-hang.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, index, timeStart, timeEnd, payments} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let codition = {}
                        if (search != '') {
                            codition = {
                                $or: [
                                    {userName: new RegExp(search, 'i')},
                                    {"products.name": new RegExp(search, 'i')},
                                    {codeString: new RegExp(search, 'i')},
                                    {storeName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        // codition.createAt = {$gte: Number(timeStart), $lte: Number(timeEnd)}
                        if (payments && payments != 'all') {
                            codition.payments = Number(payments)
                        }
                        codition.status = Number(index);
                        let bills = await RequestBuyProductModel.MODEL.getBillForPageAdmin(codition, page, limit);
                        let countBill = await RequestBuyProductModel.MODEL.getCountRequestForPage(codition)
                        bills.forEach(bill => {
                            bill.listProduct = '';
                            bill.totalMoney = 0;
                            bill.products.forEach(item => {
                                bill.listProduct += item.name + "\n";
                                bill.totalMoney += Number(item.price) * Number(item.count)
                            })
                        });
                        return ChildRouter.responseSuccess("Thành công", res, {bills, countBill});
                    }],
                },
            },
            '/lay-danh-sach-don-hang-doi-qua.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, search, index, timeStart, timeEnd} = req.body;
                        if (!page) page = 1;
                        let limit = 10;
                        let condition = {}
                        if (search != '' && search !== undefined) {
                            condition = {
                                $or: [
                                    // {userName: new RegExp(search, 'i')},
                                    // {"products.name": new RegExp(search, 'i')},
                                    // {codeString: new RegExp(search, 'i')},
                                    // {storeName: new RegExp(search, 'i')},
                                ],
                            }
                        }
                        condition.createAt = {$gte: Number(timeStart), $lte: Number(timeEnd)}
                        condition.shippingStatus = {$eq: Number(index)}
                        condition.point = {$lt: 0}

                        let data = null;

                        data = await UserPointLogModel.MODEL.getAllByConditionWithPage(condition, {createAt: -1}, page, limit);

                        let totalPage = await UserPointLogModel.MODEL.getTotalPage(condition, limit)

                        return ChildRouter.responseSuccess("Thành công", res, {
                            result: data,
                            total: data.length,
                            ...totalPage
                        });

                        // let bills = await RequestBuyProductModel.MODEL.getBillForPageAdmin(codition, page, limit);
                        // let countBill = await RequestBuyProductModel.MODEL.getCountRequestForPage(codition)
                        // bills.forEach(bill => {
                        //     bill.listProduct = '';
                        //     bill.totalMoney = 0;
                        //     bill.products.forEach(item => {
                        //         bill.listProduct += item.name + "\n";
                        //         bill.totalMoney += Number(item.price) * Number(item.count)
                        //     })
                        // });
                        // return ChildRouter.responseSuccess("Thành công", res, {bills, countBill});
                    }],
                },
            },
            '/chi-tiet-don-hang-doi-qua/:id.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chi-tiet-don-hang-doi-qua.ejs',
                    get: 'view',
                    title: 'Chi tiết đơn hàng đổi quà',
                },

                methods: {
                    get: [async function (req, res) {
                        let recordId = req.params.id;
                        let info = await UserPointLogModel.MODEL.getById(recordId);
                        if (!info) return ChildRouter.redirect(res, '/store/quan-ly-don-hang-doi-qua.html');
                        let products = []
                        if (info.data)
                        {
                            products.push(JSON.parse(info.data))
                        }

                        if (info.shippingData != undefined)
                        {
                            info['shippingData'] = JSON.parse(info.shippingData)
                        }

                        info['products'] = products

                        if (info.shippingStatus == 0)
                        {
                            info['statusText'] = 'Chờ xác nhận'
                        }

                        if (info.shippingStatus == 1)
                        {
                            info['statusText'] = 'Hoàn thành'
                        }

                        if (info.shippingStatus == 2)
                        {
                            info['statusText'] = 'Huỷ Bỏ'
                        }

                        if (info.shippingStatus == 3)
                        {
                            info['statusText'] = 'Giao hàng'
                        }

                        if (info.shippingStatus == 4)
                        {
                            info['statusText'] = 'Hoàn trả'
                        }


                        return ChildRouter.renderOrResponse(req, res, {buyInfo: info});
                    }],
                },
            },
            '/chi-tiet-don-hang/:id.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chi-tiet-don-hang.ejs',
                    get: 'view',
                    title: 'Chi tiết đơn hàng',
                },

                methods: {
                    get: [async function (req, res) {
                        let requestBuyId = req.params.id;
                        let info = await RequestBuyProductModel.MODEL.getRequestById(requestBuyId);
                        if (!info) return ChildRouter.redirect(res, '/store/quan-ly-don-hang.html');

                        let coupon = null;
                        let giaTriGiam = '0VND';
                        if (info.couponId && info.couponId.trim() != '') {
                            coupon = await CouponModel.MODEL.getDataById(info.couponId);
                            if (coupon) {
                                giaTriGiam = coupon.type == 1 ? coupon.value + '%' : NumberUtils.baseFormatNumber(coupon.value) + ' VNĐ';
                            }
                        }
                        if (!coupon) {
                            coupon = {value: 0, type: 2}
                        }


                        if (Number(info.type) == 5) {
                            if (info.adminConfirm == 1) {
                                info.messageReject = "Khách hàng không nhận hàng";
                            } else {
                                let notify = await NotificationModel.MODEL.getDataWhere({
                                    requestId: requestBuyId,
                                    type: {$in: [3, 6]}
                                }, NotificationModel.MODEL.FIND_ONE());
                                if (notify) {
                                    info.messageReject = notify.message;
                                } else {
                                    info.messageReject = "Không xác định";
                                }
                            }
                        }
                        return ChildRouter.renderOrResponse(req, res, {buyInfo: info, coupon, giaTriGiam});
                    }],
                },
            },


            '/ds-store-co-quy-online.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page, itemsPerPage} = req.query;
                        if (!search) search = '';
                        if (!page) page = 1;
                        if (!itemsPerPage) itemsPerPage = 30;

                        let where = {wallet: {$nin: [0]}, type: 1};
                        if (search != '') {
                            where['$or'] = [
                                {userName: new RegExp(search, 'i')},
                                {email: new RegExp(search, 'i')},
                                {phone: new RegExp(search, 'i')},
                                {address: new RegExp(search, 'i')},
                                {fullName: new RegExp(search, 'i')},
                            ]
                        }
                        let totalPage = Math.ceil(await UserModel.MODEL.countDataWhere(where) / itemsPerPage)

                        let users = await UserModel.MODEL.getDataForPage(where, UserModel.MODEL.FIND_MANY(), Number(page), {wallet: -1}, Number(itemsPerPage));

                        return ChildRouter.responseSuccess("Thanh cong", res, {totalPage, users});
                    }],
                }
            },

            '/lich-su-thanh-toan-online.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page, itemsPerPage, timeStart, timeEnd, userId} = req.query;
                        if (!search) search = '';
                        if (!page) page = 1;
                        if (!itemsPerPage) itemsPerPage = 30;

                        let where = {};
                        if (search != '') {
                            where['$or'] = [
                                {userName: new RegExp(search, 'i')},
                                {email: new RegExp(search, 'i')},
                                {phone: new RegExp(search, 'i')},
                                {address: new RegExp(search, 'i')},
                                {fullName: new RegExp(search, 'i')},
                            ]
                        }
                        if (userId) {
                            where['userId'] = userId;
                        }

                        let histories = await PayingWalletLogModel.MODEL.getLogsData((timeEnd != '' && timeStart != '') ? {
                            createAt: {
                                $lte: new Date(timeEnd).getTime() + 60000,
                                $gte: new Date(timeStart).getTime() - 60000
                            }
                        } : {}, where, Number(page), Number(itemsPerPage));
                        let totalPage;
                        if (!userId) {
                            totalPage = Math.ceil(await PayingWalletLogModel.MODEL.getLogsCount((timeEnd != '' && timeStart != '') ? {
                                createAt: {
                                    $lte: new Date(timeEnd).getTime() + 60000,
                                    $gte: new Date(timeStart).getTime() - 60000
                                }
                            } : {}, where) / itemsPerPage)
                        }

                        return ChildRouter.responseSuccess("Thanh cong", res, {totalPage, histories});
                    }],
                }
            },


            '/thanh-toan-wallet-online.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {userId, amount} = req.body;
                        amount = Number(amount);
                        let user = await UserModel.MODEL.getUsersById(userId);
                        if (user) {
                            if (Number(amount) > user.wallet) {
                                return ChildRouter.responseError("Số tiền thanh toán lớn hơn số dư hiện có", res);
                            }

                            await UserModel.MODEL.updateById(userId, {$inc: {wallet: -amount}});
                            await WalletOnlineModel.MODEL.updateWallet({$inc: {total: -amount, storeValue: -amount,}});
                            await PayingWalletLogModel.MODEL.addLogs({
                                userId, amount
                            });

                        }
                        return ChildRouter.responseSuccess("Thanh toán thành công", res);
                    }],
                }
            },

            '/xoa-lich-su-thanh-toan-online.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {historyId} = req.body;
                        await PayingWalletLogModel.MODEL.removeDataById(historyId);
                        return ChildRouter.responseSuccess("Xoá thành công", res);
                    }],
                }
            },

            '/quan-ly-lich-hen.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/quan-ly-lich-hen.ejs',
                    get: 'view',
                    title: 'Quản lý lịch hẹn',
                },

                methods: {
                    get: [async function (req, res) {
                        // let services = await ServicesModel.MODEL.getServicesByCondition({
                        //     // type: {$in: [1, 2, 3]}
                        // })
                        // console.log(services)
                        // let listServiceBytype = {
                        //     1: [],
                        //     2: [],
                        //     3: []
                        // }
                        // services.forEach(item => {
                        //     listServiceBytype[item.type].push(item._id.toString())
                        //     listServiceBytype[item._id] = item
                        // })
                        let bookExams = await BookExaminationModel.MODEL.getBookExaminationByConditionDetail({}, {createAt: -1})
                        let bookRooms = await BookRoomModel.MODEL.getBookRoomByConditionDetail({}, {createAt: -1})
                        let bookSpas = await BookSpaModel.MODEL.getBookSpaByConditionDetail({}, {createAt: -1})
                        let bookClassification = await BookClassificationModel.MODEL.getByConditionDetail({}, { createAt: -1 })

                        // bookExams.forEach(item => {
                        //     item.storeName = item.branchName
                        // })
                        // bookRooms.forEach(item => {
                        //     item.storeName = item.branchName
                        // })
                        // bookSpas.forEach(item => {
                        //     item.storeName = item.branchName
                        // })
                        return ChildRouter.renderOrResponse(req, res, {bookExams, bookRooms, bookSpas, bookClassification});
                    }],
                },
            },


            '/chi-tiet-lich-hen/:scheduleId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/chi-tiet-lich-hen.ejs',
                    get: 'view',
                    title: 'Chi tiết lịch hẹn',
                },

                methods: {
                    get: [async function (req, res) {
                        let {index} = req.query
                        index = Number(index)
                        if ([1, 2, 3, 4, 5].includes(index)) {
                            var dataBook = null
                            if (index == 1) {
                                dataBook = await BookExaminationModel.MODEL.getBookExaminationById(req.params.scheduleId)
                            } else if (index == 2) {
                                dataBook = await BookRoomModel.MODEL.getBookRoomById(req.params.scheduleId)
                            } else if (index == 3) {
                                dataBook = await BookSpaModel.MODEL.getBookSpaById(req.params.scheduleId)
                            } else if (index == 4 || index == 5) {
                                dataBook = await BookClassificationModel.MODEL.getDataById(req.params.scheduleId)
                            }
                            if (!dataBook) {
                                return ChildRouter.redirect(res, '/admin/quan-ly-lich-hen.html');
                            }
                            let service = await ServicesModel.MODEL.getServicesById(dataBook.storeId)
                            dataBook.storeName = service.name;
                            dataBook.storeAddress = service.address;
                            dataBook.type = index;

                            if (Number(dataBook.status) == 2) {
                                let notify = await NotificationModel.MODEL.getDataWhere({
                                    bookId: req.params.scheduleId,
                                    type: {$in: [5, 7]}
                                }, NotificationModel.MODEL.FIND_ONE());
                                if (notify) {
                                    dataBook.messageReject = notify.message;
                                } else {
                                    dataBook.messageReject = "Không xác định";
                                }
                            }
                            return ChildRouter.renderOrResponse(req, res, {dataBook});
                        } else {
                            return ChildRouter.redirect(res, '/admin/quan-ly-lich-hen.html');
                        }
                    }],
                },
            },

            '/xoa-nguoi-dung.html/:userId': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {userId} = req.params;
                        let user = await UserModel.MODEL.getUsersById(userId);
                        if (!user) return ChildRouter.responseError('Tài khoản không tồn tại', res);
                        if (user.type != 2) return ChildRouter.responseError('Chỉ xoá được người dùng', res);

                        await UserModel.MODEL.removeDataById(userId);
                        BookExaminationModel.MODEL.removeDataWhere({userId});
                        BookRoomModel.MODEL.removeDataWhere({userId});
                        BookSpaModel.MODEL.removeDataWhere({userId});
                        CodeEmailModel.MODEL.removeDataWhere({$or: [{userId}, {email: user.email}]});
                        CodePasswordModel.MODEL.removeDataWhere({$or: [{userId}, {email: user.email}]});
                        FcmTokensModel.MODEL.removeDataWhere({userId});
                        FundLogModel.MODEL.removeDataWhere({userId});
                        MessageModel.MODEL.removeDataWhere({$or: [{userId}, {userSendId: userId}]});
                        NotificationModel.MODEL.removeDataWhere({$or: [{userId}, {userNotiId: userId}]});
                        RequestBuyProductModel.MODEL.removeDataWhere({userId});
                        UserSettingModel.MODEL.removeDataWhere({userId});
                        UserSettingModel.MODEL.removeDataWhere({userId});
                        VpnPayModel.MODEL.removeDataWhere({userId});
                        return ChildRouter.responseSuccess('Xoá thành công', res);
                    }],
                },
            },
            '/edit-service-of-spa/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/edit-service-of-spa.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa dịch vụ',
                    upload: [{name: 'thumbail', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên dịch vụ', min: 5, max: 200},
                                {key: 'storeId', type: this.dataType.string, name: 'Spa'},
                                {key: 'branchId', type: this.dataType.string, name: 'Cơ sở'},
                                {key: 'categoryId', type: this.dataType.string, name: 'Danh mục'},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                                {key: 'price', type: this.dataType.number, name: 'Giá', min: 5},
                                {key: 'typeService', type: this.dataType.number, name: 'Tình trạng'},
                                {key: 'shortDes', type: this.dataType.string, name: 'Mô tả ngắn'},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
                        let stores = await ServicesModel.MODEL.getServicesByCondition({ businessTypes: {$in: ['3']} })
                        let branch = await BranchModel.MODEL.getBranchById(req.params.branchId);
                        let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                            status: {$ne: 2},
                            // storeId: storeId
                        });
                        let product = await ServiceOfSpaModel.MODEL.getServiceById(req.params.productId);
                        if (product && !product.classify) product.classify = [];
                        return ChildRouter.renderOrResponse(req, res, {
                            categories,
                            product,
                            stores,
                            danhsachCoSo,
                            branch
                        });
                    }],

                    post: [async function (req, res) {
                        let {
                            name,
                            storeId,
                            description,
                            classify,
                            price,
                            typeService,
                            categoryId,
                            pictureOlds,
                            shortDes
                        } = req.body;
                        classify = JSON.parse(classify)
                        classify.forEach((item) => {
                            item.data = item?.data.filter(item => (!validate.isEmpty(item.name)) && (!validate.isEmpty(item.price)))
                        })
                        let obj = {
                            userId: req.user._id,
                            name,
                            nameUTF: StringUtils.removeUtf8(name),
                            descriptionUTF: StringUtils.removeUtf8(description),
                            categoryId,
                            storeId,
                            description,
                            price,
                            typeService,
                            classify,
                            shortDes
                        }
                        pictureOlds = JSON.parse(pictureOlds)
                        let product = await ServiceOfSpaModel.MODEL.getServiceById(req.params.productId);
                        if (req.upload && req.upload.thumbail) {
                            FileUtils.deleteFile(APP.BASE_DIR + product.thumbail)
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        if (req.upload && req.upload.pictures && req.upload.pictures.length > 0) {
                            let i = 0
                            pictureOlds.forEach((pictureOld, index) => {
                                if (pictureOld == 'false') {
                                    pictureOlds[index] = req.upload.pictures[i].path
                                    i++
                                }
                            });
                            obj.pictures = pictureOlds
                        }
                        product.pictures.forEach(item => {
                            if (obj.pictures && !obj.pictures.includes(item)) {
                                FileUtils.deleteFile(APP.BASE_DIR + item)
                            }
                        })
                        await ServiceOfSpaModel.MODEL.updateService(req.params.productId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },
            '/edit-clinic/:productId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/clinic/clinic-edit.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa dịch vụ gara',
                    upload: [{name: 'thumbail', maxCount: 1}, {name: 'pictures', maxCount: 15}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên dịch vụ', min: 5, max: 200},
                                {key: 'storeId', type: this.dataType.string, name: 'Thương hiệu'},
                                {key: 'branchId', type: this.dataType.string, name: 'Cơ sở'},
                                {key: 'categoryId', type: this.dataType.string, name: 'Danh mục'},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                                // {key: 'price',type: this.dataType.number,name: 'Giá',min: 1},
                                {key: 'typeService', type: this.dataType.number, name: 'Tình trạng'},
                                {key: 'shortDes', type: this.dataType.string, name: 'Mô tả ngắn'},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        // let brands = await ServiceOfClinicModel.MODEL.getServicesByCondition({
                        //     userId: req.user._id,
                        //     status: 1
                        // });
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({type: { $in: [ 3 ] }});
                        let brands = await ServicesModel.MODEL.getServicesByCondition({businessTypes: {$in: ['1']}})
                        let clinic = await ServiceOfClinicModel.MODEL.getClinicById(req.params.productId);
                        let danhsachCoSo = await BranchModel.MODEL.getBranchByCondition({
                            status: {$ne: 2},
                            storeId: clinic.storeId
                        });

                        if (clinic && !clinic.classify) clinic.classify = [];
                        return ChildRouter.renderOrResponse(req, res, {
                            categories,
                            clinic,
                            brands,
                            danhsachCoSo,
                            // branchIds
                        });
                    }],

                    post: [async function (req, res) {
                        let {name, storeId, branchId,categoryId, description, classify, price, typeService, shortDes} = req.body;
                        classify = JSON.parse(classify)
                        classify.forEach((item) => {
                            item.data = item?.data.filter(item => (!validate.isEmpty(item.name)) && (!validate.isEmpty(item.price)))
                        })
                        let obj = {
                            userId: req.user._id,
                            name,
                            nameUTF: StringUtils.removeUtf8(name),
                            descriptionUTF: StringUtils.removeUtf8(description),
                            storeId,
                            categoryId,
                            branchId,
                            description,
                            price,
                            typeService,
                            classify,
                            shortDes
                        }

                        let room = await ServiceOfClinicModel.MODEL.getClinicById(req.params.productId);
                        if (req.upload && req.upload.thumbail) {
                            FileUtils.deleteFile(APP.BASE_DIR + room.thumbail)
                            obj.thumbail = req.upload.thumbail[0].path
                        }
                        //Update DB
                        await ServiceOfClinicModel.MODEL.updateClinic(req.params.productId, obj)
                        // Update Tags for Brand
                        // await ServiceUpdateData.updateTags(storeId, obj.name);
                        // End Update Tags for Brand
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },
            // Promotion
            '/promotions.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/promotion/promotion-list.ejs',
                    get: 'view',
                    title: 'Danh sách chương trình khuyến mại',
                },

                methods: {
                    get: [async function (req, res) {
                        let data = await PromotionModel.MODEL.getPromotionByCondition({})
                        let dataNews = data.result ? data.result : null;
                        return ChildRouter.renderOrResponse(req, res, {dataNews});
                    }],
                },
            },

            '/promotion-add.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/promotion/promotion-add.ejs',
                    get: 'view',
                    title: 'Thêm mới khuyến mãi',
                    upload: [{name: 'thumbnail', maxCount: 1}, {name: 'cover', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let brandList = await ServicesModel.MODEL.getAll();
                        return ChildRouter.renderOrResponse(req, res, {brandList});
                    }],

                    post: [async function (req, res) {
                        let {title, description, content, stores, startTime, endTime} = req.body;
                        let obj = {
                            title,
                            description,
                            content,
                            stores,
                            startTime,
                            endTime,
                        }
                        if (req.upload && req.upload.thumbnail) {
                            obj.thumbnail = req.upload.thumbnail[0].path
                        }
                        if (req.upload && req.upload.cover) {
                            obj.cover = req.upload.cover[0].path
                        }
                        await PromotionModel.MODEL.addPromotion(obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/promotion-edit/:promotionId.html': {
                config: {
                    auth: [this.roles.admin],
                    view: 'public/index.ejs',
                    inc: 'inc/admin/promotion/promotion-edit.ejs',
                    get: 'view',
                    title: 'Chỉnh sửa khuyến mãi',
                    upload: [{name: 'thumbnail', maxCount: 1}, {name: 'cover', maxCount: 1}],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 5},
                                {key: 'description', type: this.dataType.string, name: 'Mô tả', min: 5},
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let promotion = await PromotionModel.MODEL.getPromotionById(req.params.promotionId)
                        let brandList = await ServicesModel.MODEL.getAll();
                        if (!Array.isArray(promotion.stores)) {
                            promotion.stores = []
                        }
                        return ChildRouter.renderOrResponse(req, res, {promotion, brandList});
                    }],

                    post: [async function (req, res) {
                        let {title, description, stores, content, contentTerm, startTime, endTime} = req.body;
                        let obj = {
                            title,
                            description,
                            stores,
                            content,
                            contentTerm,
                            startTime,
                            endTime
                        }
                        if (req.upload && req.upload.thumbnail) {
                            obj.thumbnail = req.upload.thumbnail[0].path
                        }
                        if (req.upload && req.upload.cover) {
                            obj.cover = req.upload.cover[0].path
                        }
                        await PromotionModel.MODEL.updatePromotion(req.params.promotionId, obj)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }]
                },
            },

            '/promotion-change-status/:promotionId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {status} = req.query
                        status = Number(status)
                        if ([0, 1].includes(status)) {
                            await PromotionModel.MODEL.updatePromotion(req.params.promotionId.trim(), {status})
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            '/promotion-delete/:promotionId.html': {
                config: {
                    auth: [this.roles.admin],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let promotion = await PromotionModel.MODEL.getPromotionById(req.params.promotionId)
                        FileUtils.deleteFile(APP.BASE_DIR + promotion.thumbail)
                        await PromotionModel.MODEL.deletePromotion(req.params.promotionId)
                        return ChildRouter.responseSuccess('Thành công', res);
                    }],
                },
            },
            // END Promotion,
                '/admin-confirm-return-product/:id.html': {
                config: {
                    auth: [this.roles.admin],
                    post: 'json',
                },

                methods: {
                    post: [
                        async function (req, res) {
                            let buyId = req.params.id
                            let info = await RequestBuyProductModel.MODEL.getRequestById(
                                buyId
                            );
                            if (!info)
                                return ChildRouter.responseError('Đơn hàng không tồn tại', res);

                            await RequestBuyProductModel.MODEL.updateById(buyId, {
                                adminConfirm: 1,
                            });
                            return ChildRouter.responseSuccess(
                                'Cập nhật trạng thái thành công',
                                res
                            );
                        },
                    ],
                },
            },
        }
    }
};
