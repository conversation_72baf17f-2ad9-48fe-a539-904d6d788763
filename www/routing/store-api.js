"use strict";
const promise = require('bluebird');
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const CfJws = require('../config/CfJws');
const CategoryModel = require('../models/CategoryModel');
const ProducModel = require('../models/ProducModel');
const NewsModel = require('../models/NewsModel');
const MessageModel = require('../models/MessageModel');
const ServicesModel = require('../models/ServicesModel');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const ServiceOfClinicModel = require('../models/ServiceOfClinicModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const BookRoomModel = require('../models/BookRoomModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookSpaModel = require('../models/BookSpaModel');
const ReportModel = require('../models/ReportModel');
const NotificationModel = require('../models/NotificationModel');
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const MailUser = require('../mailer/module/MailUser');
const CodePasswordModel = require('../models/CodePasswordModel');
const FileUtils = require('../utils/FileUtils');
const StringUtils = require('../utils/StringUtils');
const {TypeServices} = require('../models/enum/TypeServices');
const NumberUtils = require('../utils/NumberUtils');
const TimeUtils = require('../utils/TimeUtils');
const UserSession = require('../session/UserSession');
const {v4} = require('uuid');
const moment = require('moment');
const VpnPayModel = require('../models/VpnPayModel');
const CodeEmailModel = require('../models/CodeEmailModel');
const BannerAppModel = require('../models/BannerAppModel');
const BranchModel = require('../models/BranchModel');
const BookClassificationModel = require('../models/BookClassificationModel');
const BookingUtil = require("../utils/BookingUtil");
const CouponUtil = require("../utils/CouponUtil");
const {ProductOrderStatus} = require("../models/enum/ProductOrderStatus");
const {BookingStatus} = require("../models/enum/BookingStatus");
const {firebaseAdmin} = require("../config/CfFirebaseAdmin");
const _ = require('lodash');
const CouponModel = require("../models/CouponModel");
const SettingAdminModel = require("../models/SettingAdminModel");
const FundLogModel = require("../models/FundLogModel");
const WalletOnlineModel = require("../models/WalletOnlineModel");
const PointUtil = require("../utils/PointUtil");
const DataSearchModel = require("../models/DataSearchModel");
const PromotionModel = require("../models/PromotionModel");
const FcmTokensModel = require("../models/FcmTokensModel");
const GHTK = require("../../www/utils/GiaoHang/ghtk");

function groupSumArr(data) {
    let map = data.reduce((prev, next) => {
        if (next.date in prev) {
            prev[next.date].totalPrice += next.totalPrice;
        } else {
            prev[next.date] = next;
        }
        return prev;
    }, {});

    return Object.keys(map).map(id => map[id]);
}

module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/store/api');
    }

    registerRouting(io) {
        return {
            '/check-token': {
                config: {
                    auth: [this.roles.all],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        let {token} = req.body;
                        CfJws.extraToken(token).then(async (rslCheckToken) => {
                            if (rslCheckToken.error) {
                                return ChildRouter.responseError('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại', res);
                            } else {
                                if (rslCheckToken && rslCheckToken.data) {
                                    let user = await UserModel.MODEL.getUsersById(rslCheckToken.data._id);
                                    delete user.password;
                                    if (user.type != 1) {
                                        return ChildRouter.responseError('Tài khoản không hợp lệ', res);
                                    }
                                    if (user.status == 2) {
                                        return ChildRouter.responseError('Tài khoản của bạn đã bị khoá', res);
                                    } else {
                                        return ChildRouter.responseSuccess('Thành công', res, {user, token});
                                    }
                                } else {
                                    return ChildRouter.responseError('Cần đăng nhập lại', res);

                                }
                            }
                        })
                    }]
                },
            },
            '/login': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 2, max: 200},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 50},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {email, password} = req.body;
                        let result = await UserModel.MODEL.signInAccount(email, password);
                        if (result.error) return ChildRouter.responseError(result.message, res);
                        let user = result.user;
                        if (user.type != 1) return ChildRouter.responseError("ACCOUNT_NOT_ROLE_STORE", res);
                        if (user.status == 2) return ChildRouter.responseError("Tài khoản đã bị khoá", res);
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("Đăng nhập thành công", res, {user, token});
                    }],
                },
            },
            '/register': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 100},
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 2, max: 100},
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại', min: 9, max: 12},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 50},
                                {
                                    key: 'cfpassword',
                                    type: this.dataType.string,
                                    name: 'Xác thực mật khẩu',
                                    min: 5,
                                    max: 50
                                },
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {
                            fullName,
                            email,
                            phone,
                            password,
                            cfpassword,
                            address,
                            cuaHang,
                            phongKham,
                            khachSan,
                            spa,
                            showroom,
                            gas
                        } = req.body;

                        if (!cuaHang && !phongKham && !khachSan && !spa) {
                            return ChildRouter.responseError("Vui lòng chọn ít nhất một hình thức kinh doanh", res);
                        }
                        if (password !== cfpassword) {
                            return ChildRouter.responseError("Mật khẩu và mật khẩu xác nhận không giống nhau", res);
                        }
                        let result = await UserModel.MODEL.registerUser({
                            fullName,
                            email,
                            phone,
                            password,
                            address,
                            type: 1,
                            servicesStore: cuaHang ? 0 : 1,
                            servicesExamination: phongKham ? 0 : 1,
                            servicesHotel: khachSan ? 0 : 1,
                            servicesSpa: spa ? 0 : 1,
                            servicesShowroom: showroom ? 0 : 1,
                            servicesGas: gas ? 0 : 1,
                        });
                        if (result.error) {
                            return ChildRouter.response(res, result);
                        }
                        result.user.password = password; // Gán lại mật khẩu ko mã hoá
                        return ChildRouter.responseSuccess("Đăng ký thành công.", res, result);
                    }],
                },
            },
            '/forgot-password': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 2, max: 200},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {email} = req.body;
                        email = email.toString().toLowerCase().trim();
                        let user = await UserModel.MODEL.getOneUsersByCondition({
                            email
                        });
                        if (user && user.type != 2) {
                            let code = await CodePasswordModel.MODEL.randomCodeEmail(user._id, user.email, 6);
                            await MailUser.sendCodeResetPassword(user.email, code);
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Email không tồn tại trong hệ thống!', res);
                        }
                    }]
                },
            },
            '/check-code-password': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'code', type: this.dataType.string, name: 'Mã', min: 5, max: 200},
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {code, email} = req.body;
                        let dataCode = await CodePasswordModel.MODEL.getCode(email.toLowerCase(), code);

                        if (dataCode) {
                            if (new Date().getTime() < (new Date(dataCode.createAt).getTime() + (60 * 24 * 60 * 1000))) {
                                return ChildRouter.responseSuccess("Thành công", res, {code, email});
                            } else {
                                return ChildRouter.responseError("Mã khôi phục đã hết hạn", res);
                            }
                        } else {
                            return ChildRouter.responseError("Mã khôi phục không đúng hoặc đã sử dụng.", res);
                        }
                    }]
                },
            },
            '/recover-password': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'code', type: this.dataType.string, name: 'Mã xác nhận', min: 5, max: 200},
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 200},
                                {
                                    key: 'confirmPassword',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {password, confirmPassword, email, code} = req.body;
                        if (password != confirmPassword) {
                            return ChildRouter.responseError("Nhập lại mật khẩu không đúng", res);
                        }
                        // Todo: Can lam them check code tranh bypass
                        let rsl = await UserModel.MODEL.updateUserByEmail(email, {password});
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Lấy lại mật khẩu thành công", res);
                    }]
                },
            },
            '/change-password': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'currentPass',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu hiện tại',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'newPass',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu mới',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'newPassConf',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu mới',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {currentPass, newPass, newPassConf} = req.body;
                        if (newPass != newPassConf) return ChildRouter.responseError("Nhập lại mật khẩu không khớp", res);
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        if (user.password != StringUtils.md5(currentPass)) return ChildRouter.responseError("Mật khẩu hiện tại không đúng", res);
                        let rsl = await UserModel.MODEL.updateUser(req.user._id, {password: newPass});
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Thay đổi mật khẩu thành công", res);
                    }]
                },
            },
            '/update-profile': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 1, max: 100},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {fullName, email, picture, phone, birthday, addressList} = req.body;

                        let obj = {
                            fullName,
                            // phone,
                            birthday,
                            email,
                            picture,
                            addressList,
                        };

                        for (let key in obj) {
                            if (!obj[key] && obj[key] != 0) {
                                delete obj[key]
                            }
                        }
                        // TODO: cần test xem đổi cả birthday nữa.
                        try {
                            const day = parseInt(birthday.split('/')[0])
                            const month = parseInt(birthday.split('/')[1])
                            const year = parseInt(birthday.split('/')[2])
                            obj.birthday = new Date(year, month - 1, day).getTime()
                        } catch (e) {

                        }

                        let rsl = await UserModel.MODEL.updateUser(req.user._id, obj);
                        if (rsl.error) {
                            return ChildRouter.responseError(rsl.message, res);
                        }
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        delete user.password;
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("Thành công", res, {user, token});
                    }]
                },
            },
            '/update-avatar': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}],
                },

                methods: {
                    post: [async function (req, res) {
                        if (req.upload && req.upload.picture) {
                            if (req.user.picture && req.user.picture.includes('files')) {
                                let user = await UserModel.MODEL.getUsersById(req.user._id);
                                const path = '/files' + user.picture.split('files')[1]
                                FileUtils.deleteFileDropBox(path)
                            }

                            await UserModel.MODEL.updateUser(req.user._id, {picture: req.upload.picture[0].path});
                            return ChildRouter.responseSuccess("Thành công", res, {picture: req.upload.picture[0].path});
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }]
                },
            },
            '/upload-image': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}],
                },

                methods: {
                    post: [async function (req, res) {
                        if (req.upload && req.upload.picture) {
                            return ChildRouter.responseSuccess("Thành công", res, {picture: req.upload.picture[0].path});
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }]
                },
            },
            '/get-brand-list/': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {

                        let userId = req.user._id;
                        let brandList = await ServicesModel.MODEL.getServicesByCondition({
                            userId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, brandList);
                    }]
                },
            },
            '/get-store-detail/:storeId': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        // let totalService = null;
                        let storeId = req.params.storeId;
                        let store = await ServicesModel.MODEL.getServicesById(storeId);

                        let totalSpa = await ServiceOfSpaModel.MODEL.getServiceTotal({
                            storeId,
                            status: 1
                        })
                        totalSpa = totalSpa.totalService ? totalSpa.totalService : 0; // Model nay return khac nen can convert sang

                        let totalRoom = await RoomOfHotelModel.MODEL.getRoomTotal({
                            storeId,
                            status: 1
                        })
                        let totalClinic = await ServiceOfClinicModel.MODEL.getClinicTotal({
                            storeId,
                            status: 1
                        })
                        let totalService = {
                            totalSpa, totalRoom, totalClinic
                        }
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: storeId})
                        return ChildRouter.responseSuccess("Thành công", res, {store, totalService, branchStore});
                    }]
                },
            },
            // API Order Store
            '/get-store-order/:storeId': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let storeId = req.params.storeId;
                        let {startTime, endTime, status} = req.query;

                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        if (!store) {
                            return ChildRouter.responseError('Thương hiệu không hợp lệ', res, {storeId});
                        }
                        status = Number(status)

                        let condition = {
                            storeId
                        };
                        if (startTime && endTime) {
                            startTime = startTime.length === 13 ? Number(startTime) : startTime
                            endTime = endTime.length === 13 ? Number(endTime) : endTime
                            let startTimestamp = new Date(startTime).getTime();
                            let endTimestamp = new Date(endTime).getTime();

                            condition.createAt = {"$gte": startTimestamp, "$lt": endTimestamp}
                        }
                        // Get by status.
                        if ([0, 1, 2, 3].includes(status)) {
                            condition.status = status
                        }
                        let bookExams = await BookExaminationModel.MODEL.getBookExaminationByCondition(condition, {createAt: -1})
                        let bookRooms = await BookRoomModel.MODEL.getBookRoomByCondition(condition, {createAt: -1})
                        let bookSpas = await BookSpaModel.MODEL.getBookSpaByCondition(condition, {createAt: -1})
                        let orderShop = await RequestBuyProductModel.MODEL.getRequestByCondition(condition)

                        return ChildRouter.responseSuccess('Thành công', res, {
                            bookExams,
                            bookRooms,
                            bookSpas,
                            orderShop,
                            store
                        });

                    }]
                },
            },
            // API Doanh Thu Store
            '/get-store-revenue/:storeId': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {

                        let storeId = req.params.storeId;
                        let {startTime, endTime} = req.query;

                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        if (!store) {
                            return ChildRouter.responseError('Thương hiệu không hợp lệ', res, {storeId});
                        }

                        let condition = {
                            storeId
                        };

                        if (startTime && endTime) {

                            startTime = startTime.length === 13 ? Number(startTime) : startTime
                            endTime = endTime.length === 13 ? Number(endTime) : endTime
                            startTime = new Date(startTime);
                            endTime = new Date(endTime);
                            // startTime = TimeUtils.newDate(new Date(startTime)).set({
                            //     'hour': 0,
                            //     'minute': 0,
                            //     'seconds': 0
                            // }).toISOString();
                            // endTime = TimeUtils.newDate(new Date(endTime)).set({
                            //     'hour': 23,
                            //     'minute': 59,
                            //     'seconds': 59
                            // }).toISOString();

                            condition.fullDate = {"$gte": startTime}

                            let result = await ReportModel.MODEL.getReport(condition)
                            return ChildRouter.responseSuccess('Thành công', res, {
                                startTime,
                                endTime,
                                result
                            });
                        } else {
                            return ChildRouter.responseError('Thời gian không hợp lệ', res);
                        }

                    }],
                },
            },
            // API Doanh Thu For Chart
            '/get-store-revenue-for-chart/:storeId': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {

                        let storeId = req.params.storeId;
                        let {startTime, endTime} = req.query;
                        let labels = [];
                        let datasets = [];

                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        if (!store) {
                            return ChildRouter.responseError('Thương hiệu không hợp lệ', res, {storeId});
                        }

                        let condition = {
                            storeId
                        };
                        if (startTime && endTime) {
                            startTime = startTime.length === 13 ? Number(startTime) : startTime
                            endTime = endTime.length === 13 ? Number(endTime) : endTime
                            startTime = new Date(startTime);
                            endTime = new Date(endTime);

                            condition.fullDate = {"$gte": startTime}

                            let result = await ReportModel.MODEL.getReportWithDate(condition)

                            labels = result.arrDate;
                            datasets = [
                                {data: result.arrTotalRevenue},
                                {dataBookSpa: result.arrTotalBookSpa},
                                {dataBookRoom: result.arrTotalBookRoom},
                                {dataBookClinic: result.arrTotalBookClinic},
                                {dataSales: result.arrTotalSale},
                            ];

                            return ChildRouter.responseSuccess('Thành công', res, {
                                labels, datasets
                            });

                        } else {
                            return ChildRouter.responseError('Thời gian không hợp lệ', res);
                        }

                    }],
                },
            },
            '/get-branches-store/:storeId': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {

                        let storeId = req.params.storeId;

                        let branches = await BranchModel.MODEL.getBranchByCondition({
                            status: { $ne: 2 },
                            storeId: storeId,
                        });

                        return ChildRouter.responseSuccess('Thành công', res, {
                            branches
                        });

                    }],
                },
            },
            '/get-order-detail/:type/:orderId': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let orderId = req.params.orderId;
                        let order = null;
                        let type = req.params.type;
                        let store = await ServicesModel.MODEL.getServicesByCondition({
                            userId: req.user._id
                        });
                        if (!store) {
                            return ChildRouter.responseError('Không có quyền truy cập', res, {});
                        }
                        type = Number(type);

                        switch (type) {
                            case TypeServices.SHOP:
                                order = await RequestBuyProductModel.MODEL.getOrderDetail({
                                    orderId,
                                    // storeUserId: req.user._id
                                });
                                break;
                            case TypeServices.HOTEL:
                                order = await BookRoomModel.MODEL.getOneBookRoomByCondition({
                                    orderId
                                });
                                break;
                            case TypeServices.CLINIC:
                                order = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({
                                    orderId
                                });
                                break;
                            case TypeServices.SPA:
                                order = await BookSpaModel.MODEL.getOneBookSpaByCondition({
                                    orderId
                                });
                                break;
                        }
                        if (order == null) {
                            return ChildRouter.responseError("Đơn hàng hoặc lịch hẹn không hợp lệ", res);
                        }

                        return ChildRouter.responseSuccess("Thành công", res, {detail: order});
                    }],
                },
            },
            /*
                API Cập nhật trạng thái đơn hàng:
                Query: status:Number
                orderId: _id
             */
            '/change-order-status/:type/:orderId': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let order = null;
                        let result = null;
                        let orderId = req.params.orderId; // ._id
                        let type = req.params.type;
                        let {status, isPayOnline} = req.query;
                        let {message, noteCancel} = req.body;
                        let reasonCancel = message || '';
                        noteCancel = noteCancel || '';

                        let priceBook = 0;

                        let store = await ServicesModel.MODEL.getServicesByCondition({
                            userId: req.user._id
                        });
                        if (!store) {
                            return ChildRouter.responseError('Không có quyền truy cập', res, {});
                        }
                        type = Number(type);
                        status = Number(status);

                        if (![0, 1, 2, 3, 4, 5].includes(status)) {
                            return ChildRouter.responseError('Dữ liệu không hợp lệ', res);
                        }

                        // Check for Booking Service
                        if ([1, 2, 3, 4].includes(type)) {
                            if (status === BookingStatus.CANCELED) {
                                // return ChildRouter.responseError('Lịch hẹn đã bị huỷ, Bạn không thể thay đổi.', res);
                            }
                        }


                        switch (type) {
                            case TypeServices.SHOP:
                                order = await RequestBuyProductModel.MODEL.getRequestById(orderId)
                                priceBook = order.totalPriceShop
                                // validate nếu trạng thái truyền lên = trạng thái trong database thì ko cho phép gọi nữa
                                if (status === order.status)
                                    return ChildRouter.responseError('Trạng thái đơn hàng đã được cập nhật trước đó. Vui lòng kiểm tra lại đơn hàng', res);

                                if (status === ProductOrderStatus.CANCELED && reasonCancel == '') {
                                    return ChildRouter.responseError('Lý do không hợp lệ', res);
                                }
                                if (status === ProductOrderStatus.PENDING) {
                                    return ChildRouter.responseError('Đơn hàng đã được xác nhận rồi', res);
                                }
                                if (status === ProductOrderStatus.COMPLETED && order.status == ProductOrderStatus.CANCELED) {
                                    return ChildRouter.responseError(
                                        'Đơn hàng đã được hoàn thành. Bạn không thể huỷ đơn hàng này', res
                                    );
                                }

                                if (status === ProductOrderStatus.COMPLETED && order.status == ProductOrderStatus.RETURN) {
                                    return ChildRouter.responseError(
                                        'Đơn hàng đang được hoàn trả. Không thể hoàn thành', res
                                    );
                                }

                                if (status === ProductOrderStatus.RETURN  && order.status == ProductOrderStatus.CANCELED) {
                                    return ChildRouter.responseError(
                                        'Đơn hàng đã được đánh dấu trả. Bạn không thể huỷ đơn hàng này', res
                                    );
                                }

                                if (status === ProductOrderStatus.PENDING  && order.status == ProductOrderStatus.WAITING_SHIPPING) {
                                    return ChildRouter.responseError(
                                        'Cập nhập trạng thái cập nhập không cho phép', res
                                    );
                                }

                                // let totalMoney = 0;
                                // request.products.forEach((item) => {
                                //     totalMoney += Number(item.price) * Number(item.count);
                                // });

                                // TODO can sua lai phan nay copy code trước khi xoá api '/change-status-bill/:billId.html' bên store.js tạm thời chưa dùng đến
                                // if (request.couponId && request.couponId.trim() != '') {
                                //     let coupon = await CouponModel.MODEL.getDataById(
                                //         request.couponId
                                //     );
                                //     if (coupon) {
                                //         let valueDiscount = 0;
                                //         if (coupon.type == 1) {
                                //             valueDiscount = (totalMoney * Number(coupon.value)) / 100;
                                //         } else {
                                //             valueDiscount =
                                //                 totalMoney >= Number(coupon.value)
                                //                     ? Number(coupon.value)
                                //                     : totalMoney;
                                //         }
                                //         totalMoney = Number(totalMoney - valueDiscount).toFixed(2);
                                //     }
                                // }
                                //
                                // if (req.user.fee == 1) {
                                //     // Trừ quỹ nếu user này có tính phí
                                //     let settingAd =
                                //         await SettingAdminModel.MODEL.getSettingAdmin();
                                //     let fee = (totalMoney * settingAd.percent) / 100;
                                //
                                //     await UserModel.MODEL.updateUser(req.user._id, {
                                //         $inc: { funds: -fee },
                                //     });
                                //
                                //     FundLogModel.MODEL.addFundLog({
                                //         userId: req.user._id,
                                //         storeId: request.storeUserId,
                                //         requestId: req.params.billId,
                                //         fee: fee,
                                //         typeService: 0,
                                //     });
                                // }
                                //
                                // // Nếu đây là đơn hàng thanh toán online, tiến hành cộng tiền cho user
                                // if (request.paymentMethod == 1 && request.isPayOnline == 1) {
                                //     UserModel.MODEL.updateUser(req.user._id, {
                                //         $inc: { wallet: totalMoney },
                                //     });
                                //
                                //     //Cộng quỹ online cho hệ thống
                                //     WalletOnlineModel.MODEL.updateWallet({
                                //         $inc: { storeValue: Number(totalMoney) },
                                //     });
                                // }

                                isPayOnline  = order.isPayOnline !== 1 && Number(isPayOnline) == 1 ? Number(isPayOnline) : order.isPayOnline // nếu truyền vào giá trị = 1 và status trong db != 1 thì cập nhật giá trị mới
                                result = await RequestBuyProductModel.MODEL.updateRequests(orderId, {
                                    status: status,
                                    reasonCancel,
                                    noteCancel,
                                    isPayOnline
                                })
                                break;
                            case TypeServices.HOTEL:
                                order = await BookRoomModel.MODEL.getBookRoomById(orderId);
                                priceBook = order.price
                                // Check neu order da xac nhan, Thi Ko cho thay doi trang thai Khac hoan thanh
                                if (order.status === BookingStatus.CONFIRMED && status !== BookingStatus.COMPLETED) {
                                    return ChildRouter.responseError('Cập nhập trạng thái cập nhập không cho phép', res);
                                }

                                isPayOnline  = order.isPayOnline !== 1 && Number(isPayOnline) == 1 ? Number(isPayOnline) : order.isPayOnline // nếu truyền vào giá trị = 1 và status trong db != 1 thì cập nhật giá trị mới

                                result = await BookRoomModel.MODEL.updateBookRoom(orderId, {status: status, isPayOnline})
                                break;
                            case TypeServices.CLINIC:
                                order = await BookExaminationModel.MODEL.getBookExaminationById(orderId);
                                priceBook = order.price
                                // Check neu order da xac nhan, Thi Ko cho thay doi trang thai Khac hoan thanh
                                if (order.status === BookingStatus.CONFIRMED && status !== BookingStatus.COMPLETED) {
                                    return ChildRouter.responseError('Cập nhập trạng thái cập nhập không cho phép', res);
                                }
                                isPayOnline  = order.isPayOnline !== 1 && Number(isPayOnline) == 1 ? Number(isPayOnline) : order.isPayOnline // nếu truyền vào giá trị = 1 và status trong db != 1 thì cập nhật giá trị mới
                                result = await BookExaminationModel.MODEL.updateBookExamination(orderId, {status: status, isPayOnline})
                                break;
                            case TypeServices.SPA:
                                order = await BookSpaModel.MODEL.getBookSpaById(orderId);
                                priceBook = order.price
                                // Check neu order da xac nhan, Thi Ko cho thay doi trang thai Khac hoan thanh
                                if (order.status === BookingStatus.CONFIRMED && status !== BookingStatus.COMPLETED) {
                                    return ChildRouter.responseError('Cập nhập trạng thái cập nhập không cho phép', res);
                                }
                                isPayOnline  = order.isPayOnline !== 1 && Number(isPayOnline) == 1 ? Number(isPayOnline) : order.isPayOnline // nếu truyền vào giá trị = 1 và status trong db != 1 thì cập nhật giá trị mới
                                result = await BookSpaModel.MODEL.updateBookSpa(orderId, {status: status, isPayOnline})
                                break;
                            case TypeServices.SHOWROOM:
                            case 5: // BookClassification with typeBooking = 5
                                order = await BookClassificationModel.MODEL.getDataById(orderId);
                                priceBook = order.price
                                // Check neu order da xac nhan, Thi Ko cho thay doi trang thai Khac hoan thanh
                                if (order.status === BookingStatus.CONFIRMED && status !== BookingStatus.COMPLETED) {
                                    return ChildRouter.responseError('Cập nhập trạng thái cập nhập không cho phép', res);
                                }
                                isPayOnline  = order.isPayOnline !== 1 && Number(isPayOnline) == 1 ? Number(isPayOnline) : order.isPayOnline // nếu truyền vào giá trị = 1 và status trong db != 1 thì cập nhật giá trị mới
                                result = await BookClassificationModel.MODEL.updateById(orderId, {status: status, isPayOnline})
                                break;
                        }
                        if (result) {
                            let typeNotification = 0;
                            let message = 'Thông báo';
                            let prefixMessage = ''
                            let suffixMessage = ''
                            if ([TypeServices.CLINIC, TypeServices.HOTEL, TypeServices.SPA, TypeServices.SHOWROOM].includes(type)) {
                                switch (status) {
                                    case 0:
                                        prefixMessage = 'Đang chờ chấp nhận '
                                        break;
                                    case 1:
                                        typeNotification = 4;
                                        prefixMessage = 'Đã chấp nhận '
                                        break;
                                    case 2:
                                        typeNotification = 5;
                                        prefixMessage = 'Đã hủy '
                                        break;
                                    case 3:
                                        prefixMessage = 'Đã hoàn thành '
                                        break;
                                }
                            } else if (type === TypeServices.SHOP){
                                // PENDING: 0,
                                //     WAITING_SHIPPING: 1,
                                //     ON_SHIPPING: 2,
                                //     COMPLETED: 3,
                                //     RETURN: 4,
                                //     CANCELED: 5,
                                switch (status) {
                                    case ProductOrderStatus.WAITING_SHIPPING:
                                        typeNotification = 2;
                                        prefixMessage = 'Đã chấp nhận '
                                        break;
                                    case ProductOrderStatus.COMPLETED:
                                        typeNotification = 12;
                                        prefixMessage = 'Đã hoàn thành '
                                        break;
                                    case ProductOrderStatus.CANCELED:
                                        typeNotification = 3;
                                        prefixMessage = 'Đã từ chối '
                                        break;
                                }
                            }

                            switch (type){
                                case TypeServices.SHOP:
                                    suffixMessage = 'đơn hàng'
                                    break;
                                case TypeServices.CLINIC:
                                    suffixMessage = 'lịch kiểm tra xe'
                                    break;
                                case TypeServices.HOTEL:
                                    suffixMessage = 'đặt điểm gửi xe'
                                    break;
                                case TypeServices.SPA:
                                    suffixMessage = 'dịch vụ Spa'
                                    break
                                case TypeServices.SHOWROOM:
                                    suffixMessage = 'showRoom'
                                    break;
                            }
                            message = prefixMessage + suffixMessage;

                            // cập nhật report
                            await RequestBuyProductModel.MODEL.updateRequests(
                                order._id.toString(),
                                { status }
                            );

                            // Cập nhật tích điểm vào bảng Book tương ứng.
                            if (priceBook > 0 && Number(status) === 3) {
                                const pointInsert = await PointUtil.MoneyToPoint(priceBook);
                                PointUtil.UpdatePointOrder(
                                    order.orderId,
                                    pointInsert,
                                    type
                                ); // Cập nhật vào bảng book
                                await PointUtil.UpdateUserPoint(order.userId, order.orderId, pointInsert, type , `Bạn được cộng điểm từ đơn hàng ${order.orderId}`); // Cập nhật User Point
                                // TODO: gửi thông báo tích điểm
                            } else {
                                // xóa report tổng hợp lại ngày nào có orderId đó
                                const rs = await ReportModel.MODEL.removeDataWhere({orders: order.orderId})
                            }
                            // End

                            if (prefixMessage && suffixMessage) {
                                // trường hợp nếu cả 2 đều có giá trị thì mới gửi thông báo
                                NotificationModel.MODEL.addNewNotification(order.userId, req.user._id, message, typeNotification, io, {
                                    typeService: type,
                                    bookId: order._id.toString(),
                                    orderId: order.orderId
                                })
                            }
1
                            return ChildRouter.responseSuccess("Thành công", res, {detail: order});
                        } else {
                            return ChildRouter.responseError("Đã xảy ra lỗi", res);
                        }
                    }],
                },
            },
            '/tao-don-van': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                },

                methods: {
                    post: [
                        async function (req, res) {
                            let { buyId, dv, feeShip, branchPickupId } = req.body;
                            let info = await RequestBuyProductModel.MODEL.getRequestById(
                                buyId
                            );
                            if (!info)
                                return ChildRouter.responseError('Đơn hàng không tồn tại', res);
                            if (info.shippingCode && info.shippingCode != '')
                                return ChildRouter.responseError(
                                    'Đơn hàng này đã có đơn vị vận chuyển',
                                    res
                                );
                            switch (dv) {
                                case 'ghtk':
                                    feeShip = Number(feeShip);
                                    if (feeShip == 1) {
                                        feeShip = info.transportFee;
                                    }
                                    let response = await GHTK.taoDonHang(buyId, feeShip, branchPickupId);
                                    if (!response.success) {
                                        return ChildRouter.responseError(response.message, res);
                                    }

                                    await RequestBuyProductModel.MODEL.updateById(buyId, {
                                        shippingCode: response.order.label,
                                        shippingStatus: 1,
                                        status: 2,
                                        // feeShip,
                                        transportOrder: JSON.stringify(response.order),
                                    });

                                    return ChildRouter.responseSuccess(
                                        'Vui lòng chờ đơn vị vận chuyển đến địa chỉ shop bạn lấy hàng',
                                        res,
                                        response
                                    );
                                    break;
                                default:
                                    return ChildRouter.responseError(
                                        'Đơn vị vận chuyển không hợp lệ',
                                        res
                                    );
                                    break;
                            }
                        },
                    ],
                },
            },
            '/fake-report': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {

                        // Todo: Fake Report
                        let storeId = "5f7eec1aac29623f14445110";
                        // Get report by store ID;
                        for (let i = 1; i <= 31; i++) {
                            i = i < 10 ? '0' + i : i;


                            let totalBookRoom = NumberUtils.randomFixLength(4) * 1000;
                            let totalBookSpa = NumberUtils.randomFixLength(4) * 1000;
                            let totalBookClinic = NumberUtils.randomFixLength(4) * 1000;
                            let totalSale = NumberUtils.randomFixLength(4) * 1000;
                            let totalRevenue = totalBookRoom + totalBookSpa + totalBookClinic + totalSale;

                            let fullDate = '2020-10-' + i;
                            let obj = {
                                storeId,
                                day: i,
                                month: '10',
                                year: '2020',
                                totalBookRoom,
                                totalBookSpa,
                                totalBookClinic,
                                totalSale,
                                totalRevenue,
                                fullDate: new Date(fullDate),
                            }
                        }


                        let start = new Date("09-30-2020");
                        let end = new Date("12-30-2020");
                        let condition = {
                            storeId,
                            fullDate: {
                                "$gte": start, "$lt": end
                            }
                        }

                        let result = await ReportModel.MODEL.getReportWithDate(condition)

                        // await NotificationModel.MODEL.addNewNotification('5f6c3b1d1cf1450c172e1927', '5f68128a4a439506adb900c9', "Khách hàng đặt dịch vụ", 1, io, {
                        //     bookId: '5faa009e738e2103407b1a6e',
                        //     typeService: 3, // booking spa
                        //     storeId: '5f7eec1aac29623f14445110',
                        //     orderId: '2020101095310'
                        // });

                        return ChildRouter.responseSuccess('Thanh Cong', res, result);
                    }],
                },
            },
            '/get-promotions': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let conditions = {
                            status: 1 // chỉ get status hiện
                        }
                        let { branchId } = req.query
                        if (branchId)
                        {
                            // case get những promotes chưa join
                            conditions.stores =  { "$nin" : [branchId]}
                        }
                        let data = await PromotionModel.MODEL.getPromotionByCondition(conditions);
                        return ChildRouter.responseSuccess("Thành công", res, {...data});
                    }],
                },
            },
            '/get-join-promotions': {
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let conditions = {
                            status: 1 // chỉ get status hiện
                        }
                        let { branchId } = req.query
                        if (branchId)
                        {
                            // case get đã join
                            conditions.stores =  { "$in" : [branchId]}
                        }
                        let data = await PromotionModel.MODEL.getPromotionByCondition(conditions);
                        return ChildRouter.responseSuccess("Thành công", res, {...data});
                    }],
                },
            },
            '/logout': { // TODO: đang ko sử dụng vì dùng api v2
                config: {
                    auth: [this.roles.store],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        let data = await FcmTokensModel.MODEL.removeAllTokenForUser(userId);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/cover-data-user-to-store': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let migrated = true;
                        if (migrated) {
                            return ChildRouter.responseSuccess("Vui long cau hinh de thuc hien", res);
                        }
                        let users = await UserModel.MODEL.getUsersByCondition({
                            status: 1,
                            type: 1,
                            // _id: '5ef9d45e8399115aaa723ca2'
                        })
                        console.log('Count User Chu Shop', users.length)
                        for (const user of users) {
                            const userId = user._id.toString();
                            let businessTypes = []
                            if (user.servicesSpa === 0) {
                                businessTypes.push(TypeServices.SPA.toString())
                            }
                            if (user.servicesParking === 0) {
                                businessTypes.push(TypeServices.HOTEL.toString())
                            }
                            if (user.servicesStore === 0) {
                                businessTypes.push(TypeServices.SHOP.toString())
                            }
                            if (user.servicesExamination === 0) {
                                businessTypes.push(TypeServices.CLINIC.toString())
                            }
                            await ServicesModel.MODEL.updateWhereClause({
                                userId,
                                status: 1
                            }, {
                                businessTypes
                            })
                            console.log('====================')
                            console.log('User Name', user.fullName)
                            console.log('businessTypes', businessTypes)
                            console.log('====================')
                        }

                        console.log('Migrated DB completed', users.length)
                        return ChildRouter.responseSuccess("Thanh Cong", res, {});
                    }],
                },
            },
            '/test.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let storeId = '5f7eec1aac29623f14445110';
                        let rs = await BookSpaModel.MODEL.getRevenueValue({
                            storeId,
                            status: 3,
                            isPayOnline: 1,
                        })
                        return ChildRouter.responseSuccess('Thanh Cong', res, {rs});
                    }],
                },
            },
        }
    }
};
