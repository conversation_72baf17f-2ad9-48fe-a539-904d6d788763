"use strict";

const ObjectId = require('mongoose').Types.ObjectId;
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const CfRocketChat = require('../config/CfRocketChat')
const RocketChatUtils = require('../utils/RocketChatUtils')


module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/rocket-chat/api');
    }

    registerRouting(io) {
        return {
            '/migrate-user/': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        const migrated = false;

                        if (migrated) {
                            return ChildRouter.response(res, {
                                msg: "Đã hoàn tất. Chạy lại vui lòng chuyển Migrated = false!!!"
                            });
                        }
                        let users = await UserModel.MODEL.getAllUsersByCondition({})
                        let countMigrated = 0;
                        let countUsers = users.length;

                        for (let user of users){
                            let userPhone = user.phone.replace('+', '');
                            let userName = user.type == 1 ? user.email : userPhone;
                            let userEmail = user.email || userPhone + '@gmail.com';
                            userName = userName ? userName.split("@")[0] : '';

                            let userRole = ['user'];
                            if (user.type == 1){
                                userRole.push('shop');
                            }

                            let customFields = {
                                code: user.code,
                            };
                            let objUser = {
                                username: userName,
                                email: userEmail,
                                password: user._id.toString(),
                                name: user.fullName,
                                roles: userRole,
                                customFields
                            }
                            console.log({objUser})

                            try {
                                let rs = await RocketChatUtils.registerUser(objUser);
                            } catch (e) {

                            }

                        }
                        return ChildRouter.responseSuccess("Thành công", res, {countMigrated,countUsers});
                    }]
                }
            },
        }
    }
};
