"use strict";

const ObjectId = require('mongoose').Types.ObjectId;
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const BookSpaModel = require('../models/BookSpaModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookRoomModel = require('../models/BookRoomModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const ServiceOfClinicModel = require('../models/ServiceOfClinicModel');
const CfJws = require('../config/CfJws');
const UserSession = require('../session/UserSession');
const MailUser = require('../mailer/module/MailUser');
const StringUtils = require('../utils/StringUtils');
const CodePasswordModel = require('../models/CodePasswordModel');
const AccountBankModel = require('../models/AccountBankModel');
const NotificationModel = require('../models/NotificationModel');
const FcmTokensModel = require('../models/FcmTokensModel');
const VpnPayModel = require('../models/VpnPayModel');
const ServicesModel = require('../models/ServicesModel');
const BranchModel = require('../models/BranchModel');
const CfVpnPay = require('../config/CfVpnPay');
const querystring = require('qs');
const CodeEmailModel = require('../models/CodeEmailModel');
const WalletOnlineModel = require('../models/WalletOnlineModel');
const sha256 = require('sha256');
const TimeUtils = require('../utils/TimeUtils');
const XLSX = require('xlsx-style');
const FileUtils = require('../utils/FileUtils');
const NumberUtils = require('../utils/NumberUtils');
const PointUtil = require('../utils/PointUtil');
const MapUtil = require('../utils/MapUtil');
const xl = require('excel4node');
const _ = require('lodash');
const moment = require('moment');
const APP = require('../../app');
const {
    firebaseAdmin
} = require("../config/CfFirebaseAdmin");
var numeral = require('numeral');
const CfApp = require("../config/CfApp");
const BookingUtil = require("../utils/BookingUtil");
const ReportUtil = require("../utils/ReportUtil");
const {TypeServices} = require("../models/enum/TypeServices");

module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/web/api');
    }

    registerRouting(io) {
        return {
            '/get-store-detail/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        const storeId = req.params.id;
                        let data = await ServicesModel.MODEL.getServicesById(req.params.id);
                        let spa = await ServiceOfSpaModel.MODEL.getServicePageByCondition({
                            storeId,
                            status: 1
                        }, 1, 500);
                        let hotel = await RoomOfHotelModel.MODEL.getRoomForStore(storeId);
                        let clinic = await ServiceOfClinicModel.MODEL.getClinicForStore(storeId);
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId})
                        data.spa = spa;
                        data.hotel = hotel;
                        data.clinic = clinic;
                        data.branchs = branchStore;
                        return ChildRouter.responseSuccess("Thành công", res,data);
                    }]
                }
            },
            '/get-service-detail/:type/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        const id = req.params.id;
                        let type = req.params.type;
                        type = Number(type);
                        let data = null;
                        switch (type){
                            case 0:
                                break;
                            case 1:
                                data = await ServiceOfClinicModel.MODEL.getClinicById(id);
                                break;
                            case 2:
                                data = await RoomOfHotelModel.MODEL.getRoomById(id);
                                break;
                            case 3:
                                data = await ServiceOfSpaModel.MODEL.getServiceById(id);
                                break;
                        }
                        return ChildRouter.responseSuccess("Thành công", res,data);
                    }]
                }
            },
        }
    }
};
