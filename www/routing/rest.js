"use strict";

const ChildRouter = require("../config/router/ChildRouting");
const UserModel = require("../models/UserModel");
const CategoryModel = require("../models/CategoryModel");
const ProducModel = require("../models/ProducModel");
const FileUtils = require("../utils/FileUtils");
const NumberUtils = require("../utils/NumberUtils");
const ServicesModel = require("../models/ServicesModel");
const MessageModel = require("../models/MessageModel");
const BookExaminationModel = require("../models/BookExaminationModel");
const BookRoomModel = require("../models/BookRoomModel");
const BookSpaModel = require("../models/BookSpaModel");
const RequestBuyProductModel = require("../models/RequestBuyProductModel");
const StringUtils = require("../utils/StringUtils");
const {sendHasMessageNotification} = require("../utils/Notification");
const NotificationModel = require("../models/NotificationModel");
const htmlspecialchars = require("htmlspecialchars");
const SettingAdminModel = require("../models/SettingAdminModel");
const FundLogModel = require("../models/FundLogModel");
const promise = require("bluebird");
const RechargeModel = require("../models/RechargeModel");
const HuongDanNapTienModel = require("../models/HuongDanNapTienModel");
const UserSettingModel = require("../models/UserSettingModel");
const AccountBankModel = require("../models/AccountBankModel");
const CouponModel = require("../models/CouponModel");
const WalletOnlineModel = require("../models/WalletOnlineModel");
const PayingWalletLogModel = require("../models/PayingWalletLogModel");
const BranchModel = require("../models/BranchModel");
const ServiceOfSpaModel = require("../models/ServiceOfSpaModel");
const ServiceOfClinicModel = require("../models/ServiceOfClinicModel");
const APP = require("../../app");
const {geo} = require("../config/CfGmap");

module.exports = class Auth extends ChildRouter {
	constructor() {
		super("/rest/v1");
	}

	registerRouting(io) {
		return {
			"/branches": {
				config: {
					auth: [this.roles.store],
				},

				methods: {
					get: [
						async function (req, res) {
							const {storeId} = req.query;
							// Check user owner branch or not
							const branches = await BranchModel.MODEL.getBranchByCondition({
								storeId: storeId,
								userId: req.user._id
							});
							return ChildRouter.response(res, {branches});
						}
					]
				}
			},

			"/branches-by-admin": {
				config: {
					auth: [this.roles.admin],
				},

				methods: {
					get: [
						async function (req, res) {
							const {storeId} = req.query;
							// Check user owner branch or not
							const branches = await BranchModel.MODEL.getBranchByCondition({
								storeId: storeId
							});
							return ChildRouter.response(res, {branches});
						}
					]
				}
			},

			"/store/toggle-featured": {
				config: {
					auth: [this.roles.admin],
				},

				methods: {
					post: [
						async function (req, res) {
							const {storeId} = req.body;
							// Check user owner branch or not
							const stores = await ServicesModel.MODEL.getServicesByCondition({
								_id: storeId
							});
							if (!stores || stores.length == 0) {
								return ChildRouter.responseError('Không tìm thấy cửa hàng.', res);
							}
							const store = stores[0];
							store.isFeatured = !store.isFeatured;
							await ServicesModel.MODEL.updateServices(storeId, {...store});
							return ChildRouter.responseSuccess('Cập nhật thành công.', res);
						}
					]
				}
			},
			"/service/toggle-featured": {
				config: {
					auth: [this.roles.admin, this.roles.store],
				},

				methods: {
					post: [
						async function (req, res) {
							const {serviceId} = req.body;
							// Check user owner branch or not
							const service = await ServiceOfSpaModel.MODEL.getServiceById(serviceId)
							if (!service) {
								return ChildRouter.responseError('Không tìm tháy dịch vụ.', res);
							}
							service.isFeatured = !service.isFeatured;
							await ServiceOfSpaModel.MODEL.updateService(serviceId, {...service});
							return ChildRouter.responseSuccess('Cập nhật thành công.', res);
						}
					]
				}
			},
			"/gara/toggle-featured": {
				config: {
					auth: [this.roles.admin, this.roles.store],
				},

				methods: {
					post: [
						async function (req, res) {
							const {serviceId} = req.body;
							// Check user owner branch or not
							const service = await ServiceOfClinicModel.MODEL.getClinicById(serviceId)
							if (!service) {
								return ChildRouter.responseError('Không tìm tháy dịch vụ.', res);
							}
							service.isFeatured = !service.isFeatured;
							await ServiceOfClinicModel.MODEL.updateClinic(serviceId, {...service});
							return ChildRouter.responseSuccess('Cập nhật thành công.', res);
						}
					]
				}
			},
			"/product/toggle-featured": {
				config: {
					auth: [this.roles.admin, this.roles.store],
				},

				methods: {
					post: [
						async function (req, res) {
							const {id} = req.body;
							// Check user owner branch or not
							const service = await ProducModel.MODEL.getServiceById(id)
							if (!service) {
								return ChildRouter.responseError('Không tìm tháy dịch vụ.', res);
							}
							service.isFeatured = !service.isFeatured;
							await ServiceOfSpaModel.MODEL.updateService(serviceId, {...service});
							return ChildRouter.responseSuccess('Cập nhật thành công.', res);
						}
					]
				}
			},
			"/services/filter": {
				config: {
					auth: [this.roles.all],
					post: 'json',
				},

				methods: {
					post: [async function (req, res) {
						let {searchKey, typePet, status, page, limit, typeServices, isFeatured} = req.body;

						// if (req.headers['location']) {
						// 	const location = JSON.parse(req.headers['location'])
						// 	const res = await geo.reverse({ lat: location.lat, lon: location.lng });
						// 	console.log(res)
						// }

						if (!page) page = 1;
						page = Number(page);
						if (!limit) limit = 20;
						limit = Number(limit);

						let condition = {type: Number(typeServices)};
						if (status && status != 'all') {
							condition.status = status;
						}

						if ( Number(isFeatured) == 0 ) {
							condition.isFeatured = {$ne: true};
						} else if( Number(isFeatured) == 1) {
							condition.isFeatured = true;
						}

						if(req.headers['province'])
						{
							let province = req.headers['province']
							condition['provinceUTF'] = {'$regex': StringUtils.removeUtf8(province)}
						}

						if (searchKey && searchKey.trim().length > 0) {
							searchKey = searchKey.trim();
							let code = searchKey.toLowerCase().trim();
							code = code.trim().replace("ch", '');
							code = code.trim().replace("pk", '');
							code = code.trim().replace("ks", '');
							code = code.trim().replace("spa", '');
							if (code.trim().length > 0) {
								if (Number(code.trim())) {
									condition['$or'] = [
										{code: Number(code.trim())},
										{nameUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
										{addressUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
										{provinceUTF: {'$regex': StringUtils.removeUtf8(search)}}
									]
								}
							} else {
								condition['$or'] = [
									{nameUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
									{addressUTF: {'$regex': StringUtils.removeUtf8(searchKey)}},
									{provinceUTF: {'$regex': StringUtils.removeUtf8(search)}}
								]
							}
						}
						try {
							let data = await ServicesModel.MODEL.getServicesByConditionWithPageLookup(condition, page,limit, {createAt: -1});

							return ChildRouter.responseSuccess("success", res, {
								services: data.result,
								total: data.total,
								page: data.totalPage
							});
						} catch (error) {
							return ChildRouter.responseError(error.message, res, error);
						}
					}]
				},
			},
			'/upload-centralize': {
				config: {
					auth: [this.roles.store],
					upload: [{name: 'pictures'}],
					post: 'json',
				},

				methods: {
					post: [async function (req, res) {
						let obj = {};
						if (req.upload && req.upload.pictures && req.upload.pictures.length > 0) {
							obj.pictures = [];
							for (let j = 0; j < req.upload.pictures.length; j++) {
								const filename = req.upload.pictures[j].path.split('/').pop();
								obj.pictures.push(filename)
							}
							console.log(`Rest uploaded files: `, obj );
						} else {
							return ChildRouter.responseError('Cần tải lên ít nhất 1 ảnh', res);
						}
						return ChildRouter.responseSuccess('Thành công', res, obj);
					}]
				},
			},
			"/get-users": {
				config: {
					auth: [this.roles.admin],
				},

				methods: {
					get: [
						async function (req, res) {
							const users = await UserModel.MODEL.getAllUsersByCondition({});
							return ChildRouter.response(res, {users});
						}
					]
				}
			},
		};
	}
};
