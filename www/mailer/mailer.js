"use strict";

let mailer = require("nodemailer");
let cfMailer = require('../config/CfMailer');
let {webName} = require('../config/CfApp');
const SettingAdminModel = require('../models/SettingAdminModel');

module.exports =async  function (to, subject, content, attachments = [], callback) {
    let setting = await SettingAdminModel.MODEL.getSettingAdmin()
    // let smtpTransport = mailer.createTransport({
    //     service: cfMailer.service,
    //     auth: {
    //         user: setting.username,
    //         pass: '*********************************************************************',
    //     }
    // });

    // let mail = {
    //     from: webName,
    //     to: to,
    //     subject: subject,
    //     text: 'Hello plain world!',
    //     html: content
    // };

    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey('*********************************************************************');
    const msg = {
        to: to,
        subject: subject,
        html: content,
    };
    //ES6
    sgMail
        .send(msg)
        .then(() => {}, error => {
            console.error(error);

            if (error.response) {
                console.error(error.response.body)
            }
        });

    // smtpTransport.sendMail(mail, function (error, response) {
    //     if (error) {
    //         if (callback == null || typeof callback == "undefined") {
    //         } else {
    //             callback({error: true, message: "send mail error!"});
    //         }
    //     } else {
    //         if (callback == null || typeof callback == "undefined") {
    //         } else {
    //             callback({error: false, message: "send mail success!"});
    //         }
    //     }
    //     smtpTransport.close();
    // });
};
