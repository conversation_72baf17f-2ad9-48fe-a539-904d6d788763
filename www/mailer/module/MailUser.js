"use strict";
let mailer = require('../mailer');
const host = require('../../config/CfApp').domain;
const domainRelease = require('../../config/CfApp').domainRelease;

exports.sendEmailConfirmAccount = async function (email, link) {
    let emailContent = `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
    <meta charset="UTF-8">
    <title>langnuoibienvandon.com</title>
  </head>
  
  <body>
    <div class="gmail_quote">
      <div style="margin:0px;background-color:#f4f3f4;font-family:Helvetica,Arial,sans-serif;font-size:12px"
        text="#444444" bgcolor="#F4F3F4" link="#21759B" alink="#21759B" vlink="#21759B" marginheight="0" marginwidth="0">
        <table border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#F4F3F4">
          <tbody>
            <tr>
              <td style="padding:15px">
                <center>
                  <table width="550" cellspacing="0" cellpadding="0" align="center" bgcolor="#ffffff">
                    <tbody>
                      <tr>
                        <td align="left">
                          <div style="border:solid 1px #d9d9d9">
                            <table
                              style="line-height:1.6;font-size:12px;font-family:Helvetica,Arial,sans-serif;border:solid 1px #ffffff;color:#444"
                              border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                              <tr>
                               <td align="center" bgcolor="#d2151b" style="padding:40px 0 30px 0;color:#153643;font-size:28px;font-weight:bold;font-family:Arial,sans-serif">
                                <img src="${domainRelease}/template/ui/img/mypet-logo-white.png" width="100" style="display:block; width: 100px;" class="CToWUd">
                                 </td>
                                  </tr>
                              
                                <tr>
                                  <td
                                    style="line-height:25px;padding: 10px 20px; text-align: center; background: #d2151b;">
                                    <h1 style="color: #fff; font-size: 30px; margin-bottom: 40px; text-align: center">
                                      Xác nhận email </h1>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="margin-top:15px;margin-right:30px;margin-left:30px;color:#444;line-height:1.6;font-size:12px; 
    font-family:Arial,sans-serif" border="0" width="490" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                                <tr>
                                  <td colspan="2">
                                    <div style="padding:15px 0">
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">maxQ nhận được
                                        yêu cầu đăng ký tài khoản từ email ${email}.
                                        Để xác nhận đây là email của bạn, hãy nhấn vào nút xác nhận bên dưới </p>
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">Nếu không, bạn có thể bỏ
                                        qua email này. </p>
                                      <br>
                                      <div style="text-align: center"><a href="${host + link}"
                                          style="text-decoration: none; color: #fff; background: #d2151b; padding: 8px 15px; font-size: 17px; border-radius: 5px;">Xác
                                          nhận</a></div>
                                      <br>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="line-height:1.5;font-size:12px;font-family:Arial, 
        sans-serif;margin-right:30px;margin-left:30px" border="0" width="490" cellspacing="0" cellpadding="0"
                              bgcolor="#ffffff">
                              <tbody>
                                <tr style="font-size:11px;color:#999999">
                                  <td style="border-top:solid 1px #d9d9d9" colspan="2">
                                    <div style="padding:15px 0"> This is automatic mailbox please do not Replied to this
                                      message! </div>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="color:#ffffff" colspan="2" height="15">.</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </center>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="yj6qo"></div>
        <div class="adL">
        </div>
      </div>
      <div class="adL"></div>
    </div>
  </body>
  
  </html>
`;
    mailer(email, 'Xác nhận email', emailContent, [], function (data) {
    })
};

exports.sendEmailResetPassword = async function (email, link) {
    let emailContent = `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
    <meta charset="UTF-8">
    <title>maxQ</title>
  </head>
  
  <body>
    <div class="gmail_quote">
      <div style="margin:0px;background-color:#f4f3f4;font-family:Helvetica,Arial,sans-serif;font-size:12px"
        text="#444444" bgcolor="#F4F3F4" link="#21759B" alink="#21759B" vlink="#21759B" marginheight="0" marginwidth="0">
        <table border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#F4F3F4">
          <tbody>
            <tr>
              <td style="padding:15px">
                <center>
                  <table width="550" cellspacing="0" cellpadding="0" align="center" bgcolor="#ffffff">
                    <tbody>
                      <tr>
                        <td align="left">
                          <div style="border:solid 1px #d9d9d9">
                            <table
                              style="line-height:1.6;font-size:12px;font-family:Helvetica,Arial,sans-serif;border:solid 1px #ffffff;color:#444"
                              border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                              <tr>
                               <td align="center" bgcolor="#d2151b" style="padding:40px 0 30px 0;color:#153643;font-size:28px;font-weight:bold;font-family:Arial,sans-serif">
                                <img src="${domainRelease}/template/ui/img/mypet-logo-white.png" width="100" style="display:block; width: 100px;" class="CToWUd">
                                 </td>
                                  </tr>
                                <tr>
                                  <td
                                    style="line-height:25px;padding: 10px 20px; text-align: center; background: #d2151b;">
                                    <h1 style="color: #fff; font-size: 30px; margin-bottom: 40px; text-align: center">
                                      Yêu cầu lấy lại mật khẩu </h1>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="margin-top:15px;margin-right:30px;margin-left:30px;color:#444;line-height:1.6;font-size:12px; 
    font-family:Arial,sans-serif" border="0" width="490" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                                <tr>
                                  <td colspan="2">
                                    <div style="padding:15px 0">
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">maxQ nhận được
                                        yêu cầu khôi phục mật khẩu từ email ${email}.
                                        Để xác nhận đây đây là yêu cầu của bạn, hãy nhấn vào nút xác nhận bên dưới </p>
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">Nếu không, bạn có thể bỏ
                                        qua email này. </p>
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">Email khôi phục mật khẩu
                                        này chỉ có hiệu lực trong 24 giờ kể từ khi nhận được email </p>
                                      <br>
                                      <div style="text-align: center"><a href="${host + link}"
                                          style="text-decoration: none; color: #fff; background: #d2151b; padding: 8px 15px; font-size: 17px; border-radius: 5px;">Xác
                                          nhận</a></div>
                                      <br>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="line-height:1.5;font-size:12px;font-family:Arial, 
        sans-serif;margin-right:30px;margin-left:30px" border="0" width="490" cellspacing="0" cellpadding="0"
                              bgcolor="#ffffff">
                              <tbody>
                                <tr style="font-size:11px;color:#999999">
                                  <td style="border-top:solid 1px #d9d9d9" colspan="2">
                                    <div style="padding:15px 0"> This is automatic mailbox please do not Replied to this
                                      message! </div>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="color:#ffffff" colspan="2" height="15">.</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </center>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="yj6qo"></div>
        <div class="adL">
        </div>
      </div>
      <div class="adL"></div>
    </div>
  </body>
  
  </html>
`;
    mailer(email, 'Lấy lại mật khẩu', emailContent, [], function (data) {
    })
};

exports.sendCodeResetPassword = async function (email, code) {
    let emailContent = `
  <!DOCTYPE html>
  <html lang="en">
  
  <head>
    <meta charset="UTF-8">
    <title>maxQ</title>
  </head>
  
  <body>
    <div class="gmail_quote">
      <div style="margin:0px;background-color:#f4f3f4;font-family:Helvetica,Arial,sans-serif;font-size:12px"
        text="#444444" bgcolor="#F4F3F4" link="#21759B" alink="#21759B" vlink="#21759B" marginheight="0" marginwidth="0">
        <table border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#F4F3F4">
          <tbody>
            <tr>
              <td style="padding:15px">
                <center>
                  <table width="550" cellspacing="0" cellpadding="0" align="center" bgcolor="#ffffff">
                    <tbody>
                      <tr>
                        <td align="left">
                          <div style="border:solid 1px #d9d9d9">
                            <table
                              style="line-height:1.6;font-size:12px;font-family:Helvetica,Arial,sans-serif;border:solid 1px #ffffff;color:#444"
                              border="0" width="100%" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                              <tr>
                               <td align="center" bgcolor="#d2151b" style="padding:40px 0 30px 0;color:#153643;font-size:28px;font-weight:bold;font-family:Arial,sans-serif">
                                <img src="${domainRelease}/template/ui/img/mypet-logo-white.png" width="100" style="display:block; width: 100px;" class="CToWUd">
                                 </td>
                                  </tr>
                                <tr>
                                  <td
                                    style="line-height:25px;padding: 10px 20px; text-align: center; background: #d2151b;">
                                    <h1 style="color: #fff; font-size: 30px; margin-bottom: 40px; text-align: center">
                                      Yêu cầu lấy lại mật khẩu </h1>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="margin-top:15px;margin-right:30px;margin-left:30px;color:#444;line-height:1.6;font-size:12px; 
    font-family:Arial,sans-serif" border="0" width="490" cellspacing="0" cellpadding="0" bgcolor="#ffffff">
                              <tbody>
                                <tr>
                                  <td colspan="2">
                                    <div style="padding:15px 0">
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">maxQ nhận được
                                        yêu cầu khôi phục mật khẩu từ email ${email}.
                                        Để xác nhận đây đây là yêu cầu của bạn, hãy sử dụng mã bên dưới để tạo mật khẩu mới </p>
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">Nếu không, bạn có thể bỏ
                                        qua email này. </p>
                                      <p class="title" style="line-height: 1.7; font-size: 19px;">Mã khôi phục mật khẩu
                                        này chỉ có hiệu lực trong 24 giờ kể từ khi nhận được email </p>
                                      <br>
                                      <div style="text-align: center"><a href="javascript:;"
                                          style="text-decoration: none; color: #fff; background: #d2151b; padding: 8px 15px; font-size: 17px; border-radius: 5px;">
                                          Mã: ${code}
</a></div>
                                      <br>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <table style="line-height:1.5;font-size:12px;font-family:Arial, 
        sans-serif;margin-right:30px;margin-left:30px" border="0" width="490" cellspacing="0" cellpadding="0"
                              bgcolor="#ffffff">
                              <tbody>
                                <tr style="font-size:11px;color:#999999">
                                  <td style="border-top:solid 1px #d9d9d9" colspan="2">
                                    <div style="padding:15px 0"> This is automatic mailbox please do not Replied to this
                                      message! </div>
                                  </td>
                                </tr>
                                <tr>
                                  <td style="color:#ffffff" colspan="2" height="15">.</td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </center>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="yj6qo"></div>
        <div class="adL">
        </div>
      </div>
      <div class="adL"></div>
    </div>
  </body>
  
  </html>
`;
    mailer(email, 'Lấy lại mật khẩu', emailContent, [], function (data) {
    })
};
