"use strict";

const utils = require('../../utils/utils');
const {getNumberPrice} = require("../../utils/StringUtils");

module.exports = function (app) {
    app.locals.priceFormat = function (num, fixLength = 0) {
        return utils.currencyFormat(num, fixLength);
    };

    app.locals.baseFormatNumber = function (number, fixLength) {
        return utils.currencyFormat(number, fixLength);
    };

    app.locals.numberFormat = function (number, fixLength) {
        return utils.currencyFormat(number, fixLength);
    };
    
    app.locals.getNumberPrice = function (value, replaceWith = '' ) {
        return getNumberPrice(value, replaceWith = '');
    };
};
