"use strict";

const timeUtils = require('../../utils/TimeUtils');

module.exports = function (app) {
    app.locals.convertDateShow1 = function (date) {
        return timeUtils.parseTimeFormat4(date);
    };

    app.locals.convertDateShow2 = function (date) {
        return timeUtils.parseTimeFormat5(date);
    };

    app.locals.convertDateShow3 = function (date, format) {
        return timeUtils.parseTimeFormatOption(date, format);
    }

    app.locals.convertDateShow4 = function (date) {
        return timeUtils.parseTimeFormat6(date);
    };

    app.locals.convertDateShow5 = function (date) {
        return timeUtils.parseTimeFormat5(date);
    };

    app.locals.moment = function (date, format) {
        return timeUtils.moment(date, format);
    };

    app.locals.currentDate = function () {
        let d = new Date();
        let month = d.getMonth() + 1;
        if (month < 10) month = `0${month}`;
        let date = d.getDate();
        if (date < 10) date = `0${date}`;

        return `${date}/${month}/${d.getFullYear()}`
    };

    app.locals.currentDateAdded = function (add) {
        let d = new Date();
        d.setDate(d.getDate() + add);
        let month = d.getMonth() + 1;
        if (month < 10) month = `0${month}`;
        let date = d.getDate();
        if (date < 10) date = `0${date}`;

        return `${date}/${month}/${d.getFullYear()}`
    };

    // Helper function cho datetimepicker format: 'H:i, d/m/Y'
    app.locals.formatForDateTimePicker = function (timestamp) {
        return timeUtils.parseTimeFormatOption(timestamp, 'H:mm, DD/MM/YYYY');
    };

    // Helper function để tạo đầu ngày hôm nay
    app.locals.getStartOfToday = function () {
        let today = new Date();
        let startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0, 0);
        return timeUtils.parseTimeFormatOption(startOfDay.getTime(), 'H:mm, DD/MM/YYYY');
    };

    // Helper function để tạo cuối ngày hôm nay
    app.locals.getEndOfToday = function () {
        let today = new Date();
        let endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
        return timeUtils.parseTimeFormatOption(endOfDay.getTime(), 'H:mm, DD/MM/YYYY');
    };
};
