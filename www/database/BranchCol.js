"use strict";
const {
    any
} = require('bluebird');
/**
 * <PERSON>ảng cửa hàng, phòng khám, spa, điểm gửi xe
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
var Schema = require('mongoose').Schema;
module.exports = BASE_COLL("branches", {
    userId: String,
    storeId: String, // ID cua cua hang , vi 1 cua hang co nhieu co so
    brandId:{ type: Schema.Types.ObjectId, ref: ('services') },
    code: Number,
    name: String,
    address: String,
    pictures: [String],
    timeOpen: String,
    timeClose: String,
    content: String,
    nameUTF: String,
    addressUTF: String,
    status: {
        type: Number,
        default: 1
    },
    betweenTime: String, // Thời gian phân biệt nửa ngày,
    lat: String,
    lng: String,
    phone: String,
    hotline: String, // hotline SOS
    location: {
        type: {
            type: String,
            enum: ['Point'],
            required: true
        },
        coordinates: {
            type: [Number],
            required: true
        }
    },
    province: String, // Tỉnh thành phố
    district: String, // Quận huyện
    ward: String, // Phường xã
});
