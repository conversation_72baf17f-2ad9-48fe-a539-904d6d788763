"use strict";
/**
 * <PERSON><PERSON> tiền online hệ thống quản lý
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("wallet_online", {
    total: {type: Number, default: 0}, // Tổng số dư user pay online
    storeValue: {type: Number, default: 0}, // Số dư cần trả cho store
    /**
     * Hiệu 2 số dư trên là cho phần đơn hàng đã thanh toán nhưng chưa giao hàng thanh công
     */
});