"use strict";
/**
 * Bảng cửa hàng, phòng khám, spa, điểm gửi xe
 */

var mongoose = require('mongoose');
var Schema = mongoose.Schema

const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("data_searchs", {
    userId: String,
    code: Number,
    name: String,
    address: String,
    thumbnail: String,
    province: String, // Tỉnh thành phố
    district: String, // Quận huyện
    districtUTF: String, // Quận huyện
    ward: String, // Phường xã
    street: {
        type: String,
        default: ''
    }, // Tên đường
    location: {
        type: String,
        default: ''
    },
    pictures: [String],
    isFeatured: {
        type: Boolean,
        default: false
    },
    timeOpen: String,
    timeClose: String,
    content: String,
    /**
     * 0: Cửa hàng
     * 1: Phòng Khám
     * 2: <PERSON>iể<PERSON> gửi xe
     * 3: Spa
     */
    type: {
        type: Number,
        default: 0
    },
    price: Number,
    priceText: String,
    luotDangKy: {
        type: Number,
        default: 0
    },
    nameUTF: String,
    addressUTF: String,
    provinceUTF: String,
    /**
     * 2- ẩn do chủ kinh doanh chọn ẩn
     * 1 hiển thị
     * 0: bị ận do user bị khoá hoặc chuyển sang trạng thái ẩn, hoặc tắt chức năng
     */
    status: {
        type: Number,
        default: 1
    },
    betweenTime: String, // Thời gian phân biệt nửa ngày
    totalRate: {
        type: Number,
        default: 0
    },
    branches: [{ type: Schema.Types.ObjectId, ref: 'branches' }],
    tags: [String],
    businessTypes: [String],
    saleServices: [String],
    saleName: String,
    saleValue: String,
    saleDate: Number,
    countService: Number,
    countRoom: Number,
    countClinic: Number,
    countParking: Number,
    countProduct: Number,
    dist: {
        calculated: Number,
    },
    serviceId: String,
    provinceInfo: String,
    usersFavorites: [String], // danh sách userId đã yêu thích
});
