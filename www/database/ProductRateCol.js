"use strict";
var mongoose = require('mongoose');
var Schema = mongoose.Schema;
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("product_rates", {
    userId: {type: Schema.Types.ObjectId, ref: ('users')},
    storeId: String,
    productId: String,
    content: String,
    image: [String],
    like: Number,
    commentId: String,
    rate: {type: Number, default: 0},
    parentId: {type: Schema.Types.ObjectId, ref: ('product_rates')},
});
