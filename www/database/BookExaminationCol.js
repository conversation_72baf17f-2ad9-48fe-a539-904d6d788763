"use strict";
/**
 * Lịch sử đặt phòng khám
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("book_examinations", {
    orderId: String,
    userId: String,
    storeId: String,
    storeManagerId: String,
    code: Number,
    timeCheckIn: Number,
    petSex: String, //  male ||  female
    weight: String,
    service: String,
    items: [],
    note: String, // replace key: symptom
    priceAddition: Number, // Phụ Thu
    branchAddress: String,
    branchName: String,
    branchPhone: String,
    /**
     * 0: chờ xác nhận
     * 1: Đã xác nhận
     * 2: Đã hủy
     * 3: thành công
     */
    status: {type: Number, default: 0},
    typePet: {type:Number, default:0},
    serviceDetail: String,
    phone: String,
    email: String,
    price:Number,
    coupon: String,
    /**
     * Thong tin Thanh Toan
     */
    bankCode: String, // TM = Tiền mặt ngược lại là mã ngân hàng
    paymentMethod: {type: Number, default: 0}, // 0 : Tiền mặt, 1: ATM
    isPayOnline: {type: Number, default: 0}, // 0: Thanh toán thất bại hoặc chưa thanh toán 1: Thanh Toan Thành công
    reasonCancel: String, // Lý do hủy
    noteCancel: String, // ghi chi tiết lý do hủy
    pickupAddress : String, // Điểm đón
});
