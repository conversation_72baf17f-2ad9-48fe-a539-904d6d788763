"use strict";

/**
 * lịch sử mua hàng của user
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("request_buy_products", {
    orderId: String,
    paymentId: String,
    code: Number,
    userId: String, // ID Người mua hàng
    storeId: String, // id chủ shop
    storeUserId: String, // ID tài khoản chủ shop
    userBuyFullName: String,
    phone: String,
    province: String, // Tỉnh thành phố
    district: String, // Quận huyện
    ward: String, // Phường xã
    street: String, // Tên đường
    location: String, // Chi tiết vị trí
    address: String,
    paymentMethod: {type: Number, default: 0}, // 0 : Tiền mặt / 1: Thanh Toán Online / 2 Dùng Điểm
    isPayOnline: {type: Number, default: 0}, // 0: Thanh toán thất bại hoặc chưa thanh toán 1: <PERSON><PERSON><PERSON> công
    bankCode: String, // TM = Tiền mặt ngược lại là mã ngân hàng
    shippingService: String,
    shippingCode: String,
    shippingStatus: {type: Number, default: 0}, // 0 Chưa gửi VC, 1 Đã gửi vận đơn
    transportFee: Number, // Bổ sung lưu phí vấn chuyển để hiện thị
    totalWeight: Number, // Tổng vận chuyển
    transportOrder: String, //String json của đơn hàng vẫn chuyển
    coupon: String,
    products: [{
        productId: String,
        productName: String,
        productPrice: Number,
        thumbnail: String,
        weight: String,
        count: Number,
        price: Number,
        noteProduct: String,
        classifyActive: [{
            id: String,
            name: String, // VD: Size
            value: String, // VD: M
            mass: String, // Khoi Luong: VD : 0.6
            price: String, //
        }],
    }],
    /**
     * 0 Chờ duyệt
     * 1 đồng ý bán, chờ giao hàng
     * 2 đang giao
     * 3 Hoàn thành
     * 4 Trả hàng
     * 5 Huỷ
     */
    status: {type: Number, default: 0},
    /**
     * Chỉ dùng khi status = 4
     * 0 chưa
     * 1 đã xác nhận
     */
    adminConfirm: {type: Number, default: 0},
    imageConfirm: {type: [String], default: []}, // mảng hình ảnh store upload
    reasonCancel: String, // Lý do hủy
    noteCancel: String, // ghi chi tiết lý do hủy
    timeSendRequest: Number,
    point: {type: Number, default: 0}, // Tích điểm
    totalPriceShop: {type: Number, default: 0},
});
