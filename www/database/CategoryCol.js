"use strict";
/**
 * Dòng xe
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("categories", {
    code:Number,
    name: String,
    picture:String,
    type: {type: String, default: 0}, // 0 Ô tô || 1 xe máy || 2: Ph<PERSON>ơng tiện khác
    order: {type: Number, default: 1}, // order
    status: {type: Number, default: 1}, // status = 1 for display, = 0 is hide
});
