"use strict";

const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("provinces", {
    name: String,
    nameUTF: String,
    location: {
        lng: String,
        lat: String,
    },
    parentId: { type: String, default: ''},
    parentName: { type: String, default: ''},
    parentNameUTF: { type: String, default: ''},
    level: { type: Number, default: 1},
});
