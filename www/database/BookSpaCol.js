"use strict";
/**
 * Lịch sử đặt spa
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("book_spas", {
    userId: String,
    storeId: String,
    orderId: String,
    storeManagerId: String, // idUser chủ service
    code: Number,
    timeCheckIn: Number,
    petSex: String, //  male ||  female
    weight: String,
    service: String,
    serviceDetail: String, // json of service detail
    items: [],
    /**
     * 0: chờ xác nhận
     * 1: Đã xác nhận
     * 2: Đã hủy
     * 3: Hoàn thành
     */
    status: {type: Number, default: 0},
    typePet: {type: Number, default: 0},
    phone: String,
    email: String,
    price: Number,
    priceAddition: Number, // Phụ Thu
    note: String,
    branchAddress: String,
    branchName: String,
    branchPhone: String,
    coupon: String,
    /**
     * Thong tin Thanh Toan
     */
    bankCode: String, // TM = Tiền mặt ngược lại là mã ngân hàng
    paymentMethod: {type: Number, default: 0}, // 0 : Tiền mặt, 1: ATM
    isPayOnline: {type: Number, default: 0}, // 0: Thanh toán thất bại hoặc chưa thanh toán 1: Thanh Toan Thành công
    reasonCancel: String, // Lý do hủy
    noteCancel: String, // ghi chi tiết lý do hủy
    pickupAddress : String, // Điểm đón
    point: {type: Number, default: 0} // Tích điểm
});
