"use strict";
/**
 * <PERSON>h mục được admin cài đặt
 */
var mongoose = require('mongoose');
var Schema = mongoose.Schema;
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("comments", {
    userId : { type: Schema.Types.ObjectId, ref: ('users') },
    storeId :String,
    content:String,
    image:[String],
    like:Number,
    comentId:String,
    rate:{type: Number, default: 0},
    parentId:{ type: Schema.Types.ObjectId, ref: ('comments') },
});
