"use strict";
/**
 * <PERSON>ịch sử đặt phòng
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("book_rooms", {
    userId: String,
    storeId: String,
    orderId: String,
    storeManagerId: String,
    code: Number,
    timeCheckIn: Number,
    timeCheckOut: Number,
    petSex: String, //  male ||  female
    weight: String,
    service: String,
    serviceDetail: String, // json of service detail
    items: [],
    note: String,
    /**
     * 0: chờ xác nhận
     * 1: Đã xác nhận
     * 2: Đã hủy
     * 3: Thành công
     */
    status: {type: Number, default: 0},
    typePet: {type: Number, default: 0},
    phone: String,
    email: String,
    price: Number,

    priceAddition: Number, // Phụ Thu
    branchAddress: String,
    branchName: String,
    branchPhone: String,

    datePrice: Number, // Giá mỗi ngày
    dateTime: Number, // Số ngày ở, timeCheckOut-timeCheckIn quy đổi ra ngày
    countRooms: Number, // Số phòng muốn ,
    coupon: String,
    /**
     * Thong tin Thanh Toan
     */
    bankCode: String, // TM = Tiền mặt ngược lại là mã ngân hàng
    paymentMethod: {type: Number, default: 0}, // 0 : Tiền mặt, 1: ATM
    isPayOnline: {type: Number, default: 0}, // 0: Thanh toán thất bại hoặc chưa thanh toán 1: Thanh Toan Thành công
    reasonCancel: String, // Lý do hủy
    noteCancel: String, // ghi chi tiết lý do hủy
    pickupAddress : String, // Điểm đón
});
