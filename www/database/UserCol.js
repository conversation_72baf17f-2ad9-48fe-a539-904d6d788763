"use strict";
/**
 * Ng<PERSON>ời dùng toàn bộ hệ thống
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("users", {
    fullName: {type: String, default: ''},
    userName: {type: String, default: ''},
    email: {type: String, default: ''},
    province: String, // Tỉnh thành phố
    district: String, // Quận huyện
    ward: String, // Phường xã
    street: {type: String, default: ''}, // Tên đường
    location: {type: String, default: ''},
    address: {type: String, default: ''},
    addressList:[{
        province: String, // Tỉnh thành phố
        district: String, // Quận huyện
        ward: String, // Phường xã
        street: {type: String, default: ''}, // Tên đường
        name: String,
        phone: {type: String, default: ''},
        address:String,
        default: {type: Boolean, default: false},
    }],
    password: String,
    phone: {type: String, default: ''},
    picture: String,
    background: String,
    code: Number,
    birthday: Number,
    sex: Number, // 0 : Nam || 1 : Nữ
    status: {type: Number, default: 1}, // 1 active || 2 block
    statusStore: {type: Number, default: 1}, // 0: Chờ xét duyệt || 1 : Thành công || 2 : Thất bại
    notifications: {type: Number, default: 0},
    type: Number, // 0: Admin || 1 : Chủ cửa hàng || 2 : Khách hàng
    googleId: String,
    facebookId: String,
    appleId: String,
    facebookUrl: String,
    namePet: String,
    typePet: String,
    description: String,
    countOnlineSession: {type: Number, default: 0, min: 0},
    countMessage: {type: Number, default: 0, min: 0},
    notificationsBillReturn: {type: Number, default: 0, min: 0},
    servicesStore: {type: Number, default: 1}, // 0: Mở || 1 : Đóng
    servicesExamination: {type: Number, default: 1},
    servicesHotel: {type: Number, default: 1},
    servicesSpa: {type: Number, default: 1},
    servicesParking: { type: Number, default: 1 },
    servicesShowroom: { type: Number, default: 1 },
    servicesGas: { type: Number, default: 1 },
    fee: {type: Number, default: 0}, // 0 chưa || 1 có
    funds: {type: Number, default: 0}, // tiền vốn của chủ để thanh toán phí dịch vụ
    wallet: {type: Number, default: 0}, // tiền bán hàng của chủ: user mua hàng thanh toán online
    confirmEmail: {type: Number, default: 0}, // 0 chưa - 1 đã confirm
    confirmPhone: {type: Number, default: 0}, // 0 chưa - 1 đã confirm
    point: {type: Number, default: 0} // Point
});
