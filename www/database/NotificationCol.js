"use strict";
/**
 * Thông báo
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("notifications", {
    userId: String,
    title: String,
    parentId: {type: String, default: '0'},
    /**
     * 0: user đặt sản phẩm
     * 1: user đặt dịch vụ
     * 2: Chủ shop chấp nhận bán sản phẩm
     * 3: Chủ shop từ chối bán sản phẩm lý do
     * 4: Chủ shop chấp nhận lịch dịch vụ
     * 5: Chủ shop từ chối lịch dịch vụ lý do
     * 6: Người dùng huỷ đơn hàng với lý do
     * 7: Người dùng huỷ lịc hẹn lý do
     * 8: Người dùng đã nhận hàng
     * 9: Admin xác nhận trả hàng
     * 10: Thông báo chủ shop sắp đến giờ trả phòng
     * 11: Thông báo user sắp đến giờ trả phòng
     * 12: Admin gửi thông báo
     */

    type: Number,
    storeId: String, // id cửa hàng, spa, ks, phòng khám
    userNotiId: String,
    watched: {type: Number, default: 0}, // 0 chưa || 1 đã xem
    message: String,
    /**
     * 0 đặt sp
     * 1 kham benh
     * 2 khach san
     * 3 spa
     */
    typeService: Number,
    requestId: String,
    bookId: String,
    typeUser: Number,
    /**
     * He dieu hanh
     * 0 - all
     * 1 - ios
     * 2 -android
     */
    typeOs: {type: Number, default: 0},
    version: String,
    userIds: [String],
    /**
     * Kiểu thông báo
     * 1 - Thông báo thường
     * 2 - Update App
     */
    typeNotify: {type: Number, default: 1},

    /**
     * Id tương ứng 1 lần mua hàng của user
     */
    orderId: String,
    fcmType: {type: Number, default: 0}, // tất cả
    extraData: String
});
