"use strict";
/**
 * Bảng sản phẩm
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("products", {
    userId: String,
    categoryId: String,
    storeId: String,
    branchId: [String],
    code: Number,
    name: String, // Tên hàng hóa
    thumbail: String, // Ảnh bìa
    pictures: [String], // Ảnh con
    trademark: String, // Thương hiệu
    description: String,  // Mo ta
    transport: String, // Vận chuyển
    price: Number,
    priceOld: Number, // giá cũ hiển thị khi khuyến mại
    weight: Number, // khối lượng vận chuyển
    size: [String],
    /**
     * 0 chờ admin duyệt
     * 1 hiển thị
     * 2 ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     *
     *
     *
     *
     * 2- ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     * 1 hiển thị
     * 0: chọn <PERSON>n sản phẩm
     */
    status: {type: Number, default: 0},
    watched: {type: Number, default: 0}, // Số lượt ghé xem
    revenue: {type: Number, default: 0}, // sl sp người dùng đặt mua
    typeProduct: {type: Number, default: 1}, //0: Hết hàng  || 1: còn hàng
    typeShip: {type: Number, default: 1}, //0: Miễn phí ship  || 1: Mất phí
    nameUTF: String,
    descriptionUTF: String,
    classify: [{
        name: String,
        data: [{
            name: String,
            price: String,
            priceOld: String,
            mass: String,
        }],
    }],
    rate: Number,
    isFeatured: { type: Boolean, default: false },
});
