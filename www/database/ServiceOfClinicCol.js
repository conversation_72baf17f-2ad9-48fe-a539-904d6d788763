"use strict";
/**
 * Bảng phòng của điểm gửi xe
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("service_of_clinic", {
    userId: String,
    storeId: String,
    categoryId: String,
    branchId: [String],
    code: Number,
    name: String, // Tên room
    thumbail: String, // Ảnh đại diện
    pictures: [String], // Ảnh con
    description: String,  // Mo ta
    shortDes: String,  // Mo ta ngan
    price: {type: Number, default: 0},
    isFeatured: {type: Boolean, default: false},
    /**
     * 0 chờ admin duyệt
     * 1 hiển thị
     * 2 ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     *
     *
     *
     *
     * 2- ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     * 1 hiển thị
     * 0: chọn ẩn sản phẩm
     */
    status: {type: Number, default: 0},
    watched: {type: Number, default: 0}, // Số lượt ghé xem
    revenue: {type: Number, default: 0}, // Số lượng đặt
    typeService: {type: Number, default: 1}, //0: Tạm dừng  || 1: Sẵn sàng!
    nameUTF: String,
    descriptionUTF: String,
    classify: [{
        name: String,
        data: [{
            name: String,
            price: String,
        }],
    }]
});
