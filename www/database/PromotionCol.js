"use strict";
/**
 * <PERSON> tức
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("promotion",{
    title: String,
    description: String,
    content: String,
    contentTerm: String,
    thumbnail: String,
    cover: String,
    stores: {type: [String],default: []}, // danh sách userId đã tham gia chương trình
    status: {type: Number,default: 0}, // 0: đang hiên | 1: đang ẩn
    type: {type: Number,default: 0},
    startTime: String,
    endTime: String
});
