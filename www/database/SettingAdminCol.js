"use strict";
/**
 * Phần cài đăt của admin
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("setting_admins", {
    percent: Number,
    fee: Number,
    email: String,
    username: String,
    password: String,
    timeMinute: Number,
    pointRate: {type: Number, default: 1000},
    vnpUrlProd: {type: String, default: 'https://pay.vnpay.vn/vpcpay.html'},
    vnpUrlSanbox: {type: String, default: 'http://sandbox.vnpayment.vn/paymentv2/vpcpay.html'},
    vnpTmnCode: {type: String, default: ''},
    vnpHashSecret: {type: String, default: ''},
    vnpDevMode: { type: Boolean, default: true }, // dev. prod
    bicToken: { type: String, default: '' }, // string token
});
