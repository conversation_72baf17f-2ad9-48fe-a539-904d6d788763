"use strict";
/**
 * Bảng dịch vụ
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("serviceofspas", {
    userId: String,
    categoryId: String,
    storeId: String,
    branchId: [String],
    code: Number,
    name: String, // Tên dịch vụ
    thumbail: String, // Ảnh bìa
    pictures: [String], // Ảnh con
    description: String,  // Mo ta
    shortDes: String,  // Mo ta ngan
    price: Number,
    isFeatured: {type: Boolean, default: false},
    /**
     * 0 chờ admin duyệt
     * 1 hiển thị
     * 2 ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     *
     *
     *
     *
     * 2- ẩn do chủ kinh doanh chọn ẩn hoặc xoá cửa hàng
     * 1 hiển thị
     * 0: chọn ẩn sản phẩm
     */
    status: {type: Number, default: 0},
    watched: {type: Number, default: 0}, // Số lượt ghé xem
    revenue: {type: Number, default: 0}, // sl sp người dùng đã sử dụng dịch vụ
    typeService: {type: Number, default: 1}, //0: Tạm dừng  || 1: Sẵn sàng!
    nameUTF: String,
    descriptionUTF: String,
    classify: [{
        name: String,
        data: [{
            name: String,
            price: String,
        }],
    }]
});
