"use strict";
/**
 * Danh sách coupon
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("coupons", {
    code: String,
    startTime: Number,
    endTime: Number,
    countBooking: Number,
    currentBooking: {type: Number, default: 0},
    status: {type: Number, default: 1}, // 1 active - 0 hết hạn
    type: Number, // 1 % - 2 tiền
    minBillValue: Number,
    value: Number,
    storeId: String,
    /**
     * 0: <PERSON><PERSON><PERSON> hàng
     * 1: Phòng Khám
     * 2: Điểm gửi xe
     * 3: Spa
     */
    typeService: {type: Number, default: 0},
    typeCode: {type: Number, default: 0}, // 0: là coupon admin tạo - 1: coupon store tạo
    // Thinh fix: add userId chủ store tạo coupon
    userId: String
});
