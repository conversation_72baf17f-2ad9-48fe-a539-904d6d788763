"use strict";
/**
 * <PERSON><PERSON><PERSON> sử trả phí của của hàng khi user đặt, mua
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("user_point_logs", {
    userId: String,
    orderId: String,
    point: Number,
    bookingType: Number,
    note: String,
    data: String,
    shippingData: String,
    status: {
        type: Number,
        default: 1
    },
    shippingStatus: {
        type: Number,
        default: 0 // (1 Hoàn thành, 2 Hủy bỏ, 3 <PERSON>iao hàng, 4 Hoàn trả)
    }
});
