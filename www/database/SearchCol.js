"use strict";
/**
 * Bang Search
 */
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("searchs", {
    userId: String,
    name: String,
    address: String,
    nameUTF: String,
    addressUTF: String,
    content: String,
    timeOpen: String,
    timeClose: String,
    code: Number,
    status: Number,
    luotDangKy: Number,
    type: Number,
    typePet: Number,
    pictures: [String],
    district: String,
    isFeatured: {
        type: Boolean,
        default: false
    },
    location: String,
    province: String,
    street: String,
    ward: String,
    totalRate: {
        type: Number,
        default: 0
    },
    provinceUTF: String,
    tags: [String],
    businessTypes: [String],
    serviceId: String,
    roomHotels: [String],
    serviceofspas: [String],
    clinics: [String],
    countService: Number,
    countRoom: Number,
    countClinic: String,
    countParking: String,
});
