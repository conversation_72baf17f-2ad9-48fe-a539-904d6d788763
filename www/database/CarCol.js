"use strict";
const BASE_COLL = require('../config/db/intalizeDb/BaseColl');
module.exports = BASE_COLL("cars", {
    userId: String,
    name: String, // tên xe
    carIdNo: String, // bsx
    description: String,
    photo: String,
    frameNo: String, // số khung
    engineNo: String, // số máy
    carBrandId: String, // thương hiệu ID
    categoryId: String, // dòng xe ID
    carTypeId: String, // loại xe ID
    color: String,
    dob: Number, // năm sản xuất
    datePickup: String,
    gpsId: String,
    status: {type: Number, default: 1}, // 1: đang hiên | 0: đang ẩn
});
