"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/CarCol'))
    }
    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }
    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }
    getOneByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE())
            return resolve(data)
        })
    }
    async getListPet(condition) {
        let data = await this.coll.aggregate([
            {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "pet_categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            {
                $match : condition
            },
            {
                $sort: {
                    createAt: -1
                }
            },
            {
                $project: {
                    categoryObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data : null)
    }
    async getPet(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "pet_categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            }, {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    categoryObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data[0] : null)
    }
}

exports.MODEL = new Model();
