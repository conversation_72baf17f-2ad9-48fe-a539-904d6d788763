"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/CategoryCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let count = await that.countDataWhere({});
            return resolve(count + 1);
        })
    }

    addCategory(obj) {
        const that = this;
        return new promise(async resolve => {
            const code = await that.findNextName()
            obj.code = code
            obj.order = code
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getCategoryById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getCategoryByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateCategory(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteCategory(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getCategoryByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {order: 1})
            return resolve(data)
        })
    }

    async getCategoryGroupType(condition) {
        let categoriesCho = await this.getDataWhere({type: 0, status: 1}, this.FIND_MANY(), {order: 1});
        let categoriesMeo = await this.getDataWhere({type: 1, status: 1}, this.FIND_MANY(), {order: 1});
        let categoriesThuCungKhac = await this.getDataWhere({type: 2, status: 1}, this.FIND_MANY(), {order: 1});

        return {categoriesCho, categoriesMeo, categoriesThuCungKhac};
    }

    async getCategoryByType(type) {
        return await this.getDataWhere({type: type}, this.FIND_MANY(), {name: 1});
    }

    getAllCategory() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_MANY(), {name: 1})
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
