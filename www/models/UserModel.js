"use strict";

const ObjectId = require('mongoose').Types.ObjectId;
const promise = require('bluebird');
const stringUtils = require('../utils/StringUtils');
const utils = require('../utils/utils');
const MailUser = require('../mailer/module/MailUser');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const UserSettingModel = require('./UserSettingModel');
const CodeEmailModel = require('./CodeEmailModel');
const NotificationModel = require("../models/NotificationModel");

class Model extends BaseModel {

    constructor() {
        super(require('../database/UserCol'))
    }
    addUser(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data) {
                if (data.code) code = data.code + 1;
                else code = 1;
            }

            return resolve(code);
        })
    }

    registerUser(obj) {
        const that = this;
        return new promise(async resolve => {
            if (obj.phone)
            {
                obj.phone = obj.phone.toString().trim();
            }
            obj.email = obj.email.toString().toLowerCase().trim();

            //hash password
            if (obj.password && obj.password != '') obj.password = stringUtils.md5(obj.password);

            obj.code = await that.findNextName();

            if (obj.email && obj.email != '') {
                obj.email = obj.email.trim();
                let countEmail = await that.countDataWhere({email: obj.email});
                if (countEmail > 0) {
                    return resolve({error: true, message: 'err_email_already_exist'})
                }

                let validateEmail = stringUtils.validEmail(obj.email);
                if (!validateEmail) return resolve({error: true, message: 'Email không đúng định dạng!'});
                let code = await CodeEmailModel.MODEL.randomCodeEmail(obj.email, 132);
                obj.confirmEmail = 1;
                MailUser.sendEmailConfirmAccount(obj.email, '/xac-nhan-tai-khoan-email.html?email=' + obj.email + '&code=' + code);
            }

            if (obj.phone && obj.phone != '') {
                obj.phone = obj.phone.trim();
                let countPhone = await that.countDataWhere({phone: obj.phone});
                if (countPhone > 0) {
                    return resolve({error: true, message: 'Số điện thoại đã được sử dụng!'})
                }
                let validatePhone = stringUtils.validPhone(obj.phone);
                obj.confirmPhone = 1;
                if (!validatePhone) return resolve({error: true, message: 'Số điện thoại không đúng định dạng!'});
            }



            if (!obj['birthday'])
            {
                obj['birthday'] = new Date().getTime()
            }

            let user = await that.insertData(obj);
            UserSettingModel.MODEL.addDefaultSetting({userId: user._id});
            if (obj.picture && obj.picture != '') {
            } else {
                user.picture = '/template/default-avatar.jpg';
            }
            return resolve({error: false, user});
        })
    }

    // registerUserByPhone(obj) {
    //     const that = this;
    //     return new promise(async resolve => {
    //         obj.phone = obj.phone.toString().trim();
    //         if (obj.password && obj.password != '') obj.password = stringUtils.md5(obj.password);
    //         obj.code = await that.findNextName();
    //         let confirmPhone = 0;
    //         if (obj.phone && obj.phone != '') {
    //             obj.phone = obj.phone.trim();
    //             let countPhone = await that.countDataWhere({phone: obj.phone});
    //             if (countPhone > 0) {
    //                 return resolve({error: true, message: 'Số điện thoại đã được sử dụng!'})
    //             }
    //             let validatePhone = stringUtils.validPhone(obj.phone);
    //             if (!validatePhone) return resolve({error: true, message: 'Số điện thoại không đúng định dạng!'});
    //         } else {
    //             confirmPhone = 1;
    //         }
    //         // obj.confirmEmail = confirmEmail;
    //         obj.confirmPhone = 1;
    //         if (!obj['birthday'])
    //         {
    //             obj['birthday'] = new Date().getTime()
    //         }
    //         let user = await that.insertData(obj);
    //         UserSettingModel.MODEL.addDefaultSetting({userId: user._id});
    //
    //         if (obj.picture && obj.picture != '') {
    //         } else {
    //             user.picture = '/template/default-avatar.jpg';
    //         }
    //         return resolve({error: false, user});
    //     })
    // }

    updateUser(userId, obj) {
        const that = this;
        return new promise(async resolve => {
            if (obj.password) {
                obj.password = stringUtils.md5(obj.password);
            }
            let hasConfirmEmail = 0;
            if (obj.email && obj.email != '') {
                obj.email = obj.email.trim();
                let user = await that.getUsersById(userId);
                if (user.email.trim() != obj.email.trim()) {
                    let countEmail = await that.countDataWhere({email: obj.email, _id: {$ne: ObjectId(userId)}});
                    if (countEmail > 0) {
                        return resolve({error: true, message: 'err_email_already_exist'})
                    }

                    let validateEmail = stringUtils.validEmail(obj.email);
                    if (!validateEmail) return resolve({error: true, message: 'Email không đúng định dạng!'});

                    // Xác thực email
                    let code = await CodeEmailModel.MODEL.randomCodeEmail(obj.email, 132);
                    MailUser.sendEmailConfirmAccount(obj.email, '/xac-nhan-tai-khoan-email.html?email=' + obj.email + '&code=' + code);
                    obj.confirmEmail = 0;
                    hasConfirmEmail = 1;
                }
            }
            await that.updateById(userId, obj);
            return resolve({error: false, resConfirmEmail: hasConfirmEmail});
        })
    }

    updateUserByEmail(email, obj) {
        const that = this;
        return new promise(async resolve => {

            if (obj.password) {
                obj.password = stringUtils.md5(obj.password);
            }

            if (obj.email) {
                let user = await that.getDataWhere({email}, that.FIND_ONE());
                let countEmail = await that.countDataWhere({email: obj.email, _id: {$ne: ObjectId(user._id)}});
                if (countEmail > 0) {
                    return resolve({error: true, message: 'err_email_already_exist'})
                }
            }
            await that.updateWhereClause({email}, obj);
            return resolve({error: false});
        })
    }

    updateUserByPhone(phone, obj) {
        const that = this;
        return new promise(async resolve => {

            if (obj.password) {
                obj.password = stringUtils.md5(obj.password);
            }

            if (obj.phone) {
                let user = await that.getDataWhere({phone}, that.FIND_ONE());
                let countPhone = await that.countDataWhere({phone: obj.phone, _id: {$ne: ObjectId(user._id)}});
                if (countPhone > 0) {
                    return resolve({error: true, message: 'Phone đã tồn tại!'})
                }
            }
            await that.updateWhereClause({phone}, obj);
            return resolve({error: false});
        })
    }

    signInAccount(email, password) {
        const that = this;
        return new Promise(async (resolve, reject) => {
            try {
                let user = await that.getDataWhere({
                    email: email,
                    password: stringUtils.md5(password)
                }, that.FIND_ONE());

                if (utils.isEmptyObject(user)) {
                    return resolve({error: true, message: "email hoặc mật khẩu không đúng"});
                }

                delete user.password;
                if (!user.picture) user.picture = '/template/default-avatar.jpg';
                return resolve({error: false, user});

            } catch (error) {
                // Bắt lỗi và trả về
                return reject({error: true, message: "Đã xảy ra lỗi khi đăng nhập", details: error.message});
            }
        });
    }


    signInAccountByPhone(phone, password) {
        const that = this;
        return new promise(async resolve => {
            let user = await that.getDataWhere({
                phone: phone,
                password: stringUtils.md5(password)
            }, that.FIND_ONE());
            if (utils.isEmptyObject(user)) return resolve({error: true, message: "Sai thông tin tài khoản hoặc mật khẩu không đúng."});
            delete user.password;
            if (!user.picture) user.picture = '/template/default-avatar.jpg';
            return resolve({error: false, user});
        })
    }

    getUsersById(id) {
        const that = this;
        return new promise(async resolve => {
            let user = await that.getDataById(id);
            if (user && !user.picture) user.picture = '/template/default-avatar.jpg';
            return resolve(user);
        })
    }

    getUsersInIds(ids) {
        const that = this;
        return new promise(async resolve => {
            if (!ids) ids = [];
            ids.forEach(id => {
                id = ObjectId(id);
            });
            let users = await that.getDataWhere({_id: {$in: ids}}, that.FIND_MANY());
            users.forEach(user => {
                if (!user.picture) user.picture = '/template/default-avatar.jpg';
            });
            return resolve(users);
        })
    }

    getAllUsersByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let users = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1});
            users.forEach(async user => {
                if (!user.picture) user.picture ='/template/default-avatar.jpg';
                if (!user.fee) {
                    await that.updateById(user._id, {fee: 0})
                }
            });
            return resolve(users);
        })
    }

    getOneUsersByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let user = await that.getDataWhere(condition, that.FIND_ONE());
            if (user && !user.picture) user.picture = '/template/default-avatar.jpg';
            return resolve(user);
        })
    }

    deleteUser(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere({_id: ObjectId(id), type: {$nin: [0, 2, 3, 5, 6]}});
            return resolve();
        })
    }

    chekAccountSystem() {
        const that = this;
        return new promise(async resolve => {
            let admin = await that.getDataWhere({type: 0}, that.FIND_ONE());
            if (!admin) {
                that.insertData({
                    fullName: 'Admin',
                    userName: 'admin',
                    email: '<EMAIL>',
                    password: stringUtils.md5('12345'),
                    type: 0
                })
            }
            return resolve();
        })
    }

    addNotification(userId, io) {
        const that = this;
        return new promise(async resolve => {
            try {
                let countUnread = await NotificationModel.MODEL.countDataWhere({userId: userId, watched: 0});
                await that.updateById(userId, {notifications: countUnread}); // cập nhật số thông báo chưa đọc
                let user = await that.getUsersById(userId);
                if (io && user && user.hasOwnProperty('notifications')) {
                    io.emit(userId, {type: 'notify-change-count', notifications: user.notifications});
                }
            } catch (e) {

                //TODO: log error not found user by id
                return resolve();
            }
            return resolve();
        });
    }

    addNotificationBillReturn(userId, io) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(userId, {$inc: {notificationsBillReturn: 1}});
            let user = await that.getUsersById(userId);
            io.emit(userId, {
                type: 'notify-bill-return-change-count',
                notificationsBillReturn: user.notificationsBillReturn
            });
            return resolve();
        });
    }

    resetNotification(userId) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(userId, {notifications: 0});
            return resolve();
        });
    }

    resetNotificationBillReturn(userId) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(userId, {notificationsBillReturn: 0});
            return resolve();
        });
    }
    getUsersByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), typeData)
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
