"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');

class Model extends BaseModel {

    constructor() {
        super(require('../database/CouponCol'))
    }

    async createCoupon(startTime, endTime, countBooking, countCoupon, type, value, minBillValue, storeId, typeCode, userId = '') {
        let codes = [];
        if (Number(countCoupon) == 0) return;
        for (let index = 0; index < countCoupon; index++) {
            codes.push(StringUtils.randomStringFixLengthOnlyAlphabetLowercase(7));
        }

        let mapFunc = codes.map(code => {
            return new promise(async resolve => {
                await this.insertData({
                    code,
                    startTime,
                    endTime,
                    countBooking,
                    minBillValue,
                    type,
                    value,
                    storeId,
                    typeCode,
                    userId,
                });
                return resolve();
            })
        });

        await promise.all(mapFunc);
    }


    async getCoupponWithStatus(status) {
        return await this.getDataWhere({status}, this.FIND_MANY(), {createAt: -1});
    }


    async addCurrentBookingCount(couponId) {
        await this.updateById(couponId, {$inc: {currentBooking: 1}});
        let cp = await this.getDataById(couponId);
        if (cp) {
            if (cp.currentBooking >= cp.countBooking) await this.updateById(couponId, {status: 0});
        }
    }

    async turnOffCoupponWithStatus(id) {
        return await this.updateById(id, {status: 0});
    }

    async deleteCoupponWithStatus(id) {
        return await this.removeDataById(id);
    }

    getCouponByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
