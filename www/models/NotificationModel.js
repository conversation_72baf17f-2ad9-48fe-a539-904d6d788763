"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const Notification = require('../utils/Notification');
const UserModel = require('./UserModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/NotificationCol'));
    }

    /**
     *
     * @param userId
     * @param userNotiId
     * @param title
     * @param type
     * @param io
     * @param data
     * @returns {Promise}
     */
    addNewNotification(userId, userNotiId, title, type, io, data) {
        const that = this;
        return new promise(async resolve => {
            let notify = await that.insertData({userId, userNotiId, title, type, ...data});
            if (notify && notify._id)
            {
                await Notification.sendNotification(notify._id.toString()); // push firebase
            }
            await UserModel.MODEL.addNotification(userId, io);
            return resolve(notify);
        });
    }


    getNotificationsById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    async getAllNotificationsByUserId(condition, typeData = 0, page = 1, itemsPerPage = 500) {
        return await this.coll.aggregate([
            {
                $addFields: {userIdObject: {$toObjectId: '$userNotiId'}}
            },
            // {
            //     $addFields: {bookIdObject: {$toObjectId: '$bookId'}}
            // },
            {
                $lookup: {
                    localField: 'userIdObject',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $lookup: {
                    from: "book_spas",
                    let: {orderId: "$orderId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$orderId', '$$orderId']
                                }
                            }
                        },
                        {$match: {orderId: {$exists: true}}},
                    ],
                    as: 'bookSpaInfo',
                }
            },
            {
                $lookup: {
                    from: "book_rooms",
                    let: {orderId: "$orderId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$orderId', '$$orderId']
                                }
                            }
                        },
                        {$match: {orderId: {$exists: true}}},
                    ],
                    as: 'bookRoomInfo'
                }
            },
            {
                $lookup: {
                    from: "book_examinations",
                    let: {orderId: "$orderId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$orderId', '$$orderId']
                                }
                            }
                        },
                        {$match: {orderId: {$exists: true}}},
                    ],
                    as: 'bookClinicInfo'
                }
            },
            {
                $lookup: {
                    from: "request_buy_products",
                    let: {orderId: "$orderId"},
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$orderId', '$$orderId']
                                }
                            }
                        },
                        {$match: {orderId: {$exists: true}}},
                    ],
                    as: 'orderProductInfo'
                }
            },
            {
                $match: condition
            },
            {$sort: typeData},
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
            {
                $project: {
                    userId: 1,
                    title: 1,
                    type: 1,
                    storeId: 1, // id cửa hàng, spa, ks, phòng khám
                    userNotiId: 1,
                    watched: 1, // 0 chưa || 1 đã xem
                    message: 1,
                    typeService: 1,
                    version: 1,
                    typeNotify: 1,
                    requestId: 1,
                    bookId: 1,
                    orderId: 1,
                    createAt: 1,
                    messageReject: 1,
                    extraData: 1, // json string
                    bookSpaInfo: {$arrayElemAt: ['$bookSpaInfo', 0]},
                    bookRoomInfo: {$arrayElemAt: ['$bookRoomInfo', 0]},
                    bookClinicInfo: {$arrayElemAt: ['$bookClinicInfo', 0]},
                    orderProductInfo: {$arrayElemAt: ['$orderProductInfo', 0]},
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    avatarUser: {$arrayElemAt: ['$userInfo.picture', 0]}
                }
            }
        ])
    }

    updateNotification(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteNotification(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    deleteNotificationByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere(condition)
            return resolve()
        })
    }

    updateNotificationByCondition(condition, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateWhereClause(condition, data)
            return resolve()
        })
    }

    getTotalPage(condition, limit = 10) {
        return new promise(async resolve => {
            let totalComment = await this.countDataWhere({userId: condition.userId});
            let totalPage = Math.ceil(totalComment / limit);
            return resolve(totalPage);
        })
    }

    getTotalPageAndUnread(condition, limit = 10) {
        return new promise(async resolve => {
            let totalNotification = await this.countDataWhere({userId: condition.userId});
            let totalPage = Math.ceil(totalNotification / limit);
            let countUnread = await this.countDataWhere({userId: condition.userId, watched: 0});
            return resolve({totalPage, countUnread});
        })
    }

    getOneByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let user = await that.getDataWhere(condition, that.FIND_ONE());
            return resolve(user);
        })
    }
}

exports.MODEL = new Model();
