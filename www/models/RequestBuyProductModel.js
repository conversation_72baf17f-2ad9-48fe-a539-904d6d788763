"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const GHTK = require('../utils/GiaoHang/ghtk');
const {priceFormat} = require('../utils/NumberUtils');
const ObjectId = require('mongoose').Types.ObjectId;
const util = require('util');
const ghtkStatus = require('../config/CfShippingStatus');
const ReportUtil = require("../utils/ReportUtil");

class Model extends BaseModel {

    constructor() {
        super(require('../database/RequestBuyProduct'))
    }

    completeData(p) {
        function handleDataObject(d) {
            switch (Number(d.status)) {
                case 0:
                    d.statusText = 'Chờ xác nhận';
                    d.class = 'wait'
                    break;
                case 1:
                    d.statusText = 'Chờ giao hàng';
                    d.class = 'wait_product';
                    break;
                case 2:
                    d.statusText = 'Đang giao';
                    d.class = 'delivery_product';
                    break;
                case 3:
                    d.statusText = 'Hoàn thành';
                    d.class = 'deliveried_product';
                    break;
                case 4:
                    d.statusText = 'Trả hàng';
                    d.class = 'cancle';
                    break;
                case 5:
                    d.statusText = 'Đã hủy';
                    d.class = 'cancle';
                    break;
            }
            return d;
        }

        if (util.isArray(p)) {
            return p.map((e) => {
                return handleDataObject(e);
            });
        } else {
            return handleDataObject(p);
        }
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }

    addRequest(obj) {

        const that = this;
        return new promise(async resolve => {
            const code = await that.findNextName()
            obj.code = code;
            let data = await that.insertData(obj);
            return resolve(data)

        });
    }

    async getRequestForPageArray(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {userObjectId: {$toObjectId: '$userId'}, codeString: {$toString: '$code'}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    shopId: 1,
                    products: 1,
                    createAt: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "storeId": {"$first": "$storeId"},
                    "shopId": {"$first": "$shopId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                }
            },
            {
                $match: condition
            },
        ]));
    }

    async getRequestForPage(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {userObjectId: {$toObjectId: '$userId'}, codeString: {$toString: '$code'}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    orderId: 1,
                    storeId: 1, // id shop
                    storeUserId: 1, // id tai khoan quan ly shop
                    products: 1,
                    createAt: 1,
                    paymentMethod: 1,
                    isPayOnline: 1,
                    status: 1,
                    codeString: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    productName: {$arrayElemAt: ['$productInfo.name', 0]},
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "userId": {"$first": "$userId"},
                    "orderId": {"$first": "$orderId"},
                    "storeId": {"$first": "$storeId"},
                    "products": {"$push": "$products"},
                    "code": {"$first": "$code"},
                    "storeUserId": {"$first": "$storeUserId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                    "paymentMethod": {"$first": "$paymentMethod"},
                    "isPayOnline": {"$first": "$isPayOnline"},
                }
            },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
        ]));
    }

    async getRequestForPageAdmin(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {
                    userObjectId: {$toObjectId: '$userId'},
                    codeString: {$toString: '$code'},
                    storeObjectId: {$toObjectId: '$storeId'}
                }
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    shopId: 1,
                    products: 1,
                    createAt: 1,
                    adminConfirm: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    productName: {$arrayElemAt: ['$productInfo.name', 0]},
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeProvince: {$arrayElemAt: ['$storeInfo.province', 0]},
                    storeDistrict: {$arrayElemAt: ['$storeInfo.district', 0]},
                    storeWard: {$arrayElemAt: ['$storeInfo.ward', 0]},
                    storeStreet: {$arrayElemAt: ['$storeInfo.street', 0]},
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "adminConfirm": {"$first": "$adminConfirm"},
                    "storeId": {"$first": "$storeId"},
                    "shopId": {"$first": "$shopId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                    "storeName": {"$first": "$storeName"},
                }
            },
            {
                $match: condition
            },
            {$sort: {timeSendRequest: 1}},
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
        ]));
    }

    async getBillForPageAdmin(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {
                    userObjectId: {$toObjectId: '$userId'},
                    codeString: {$toString: '$code'},
                    storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}
                }
            },
            {
                $lookup: {
                    from: "shipping_histories",
                    let: {orderId: "$orderId"},
                    as: 'shippingInfo',
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$partner_id', '$$orderId']
                                }
                            }
                        },
                        {
                            $sort: { createAt: -1}
                        }
                    ],
                }
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    orderId: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    storeUserId: 1,
                    userBuyFullName: 1,
                    products: 1,
                    createAt: 1,
                    phone: 1,
                    province: 1,
                    district: 1,
                    ward: 1,
                    street: 1,
                    location: 1,
                    paymentMethod: 1,
                    isPayOnline: 1,
                    bankCode: 1,
                    address: 1,
                    status: 1, // 0 Chờ duyệt,
                    codeString: 1,
                    payments: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    productName: {$arrayElemAt: ['$productInfo.name', 0]},
                    coupon: 1,
                    shippingService: 1,
                    transportFee: 1,
                    totalWeight: 1,
                    totalPriceShop: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeProvince: {$arrayElemAt: ['$storeInfo.province', 0]},
                    storeDistrict: {$arrayElemAt: ['$storeInfo.district', 0]},
                    storeWard: {$arrayElemAt: ['$storeInfo.ward', 0]},
                    storeStreet: {$arrayElemAt: ['$storeInfo.street', 0]},
                    shippingInfo: 1,
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "adminConfirm": {"$first": "$adminConfirm"},
                    "storeId": {"$first": "$storeId"},
                    "shopId": {"$first": "$shopId"},
                    "createAt": {"$first": "$createAt"},
                    "payments": {"$first": "$payments"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                    "storeName": {"$first": "$storeName"},
                }
            },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
        ]));
    }

    async getAllRequestForPageAdmin(condition) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {
                    userObjectId: {$toObjectId: '$userId'},
                    codeString: {$toString: '$code'},
                    storeObjectId: {$toObjectId: '$storeId'}
                }
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    shopId: 1,
                    products: 1,
                    createAt: 1,
                    adminConfirm: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    storeName: {$arrayElemAt: ['$storeInfo.fullName', 0]},
                    productName: {$arrayElemAt: ['$productInfo.name', 0]},
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "adminConfirm": {"$first": "$adminConfirm"},
                    "storeId": {"$first": "$storeId"},
                    "shopId": {"$first": "$shopId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                    "storeName": {"$first": "$storeName"},
                }
            },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
        ]));
    }

    async getCountRequestForPage(condition) {
        let data = await this.coll.aggregate([
            {
                $addFields: {userObjectId: {$toObjectId: '$userId'}, codeString: {$toString: '$code'}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.name": {$concat: [{$arrayElemAt: ['$productInfo.name', 0]}, ' - SL: ', {$toString: "$products.count"}]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    storeUserId: 1,
                    products: 1,
                    createAt: 1,
                    adminConfirm: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    productName: {$arrayElemAt: ['$productInfo.name', 0]},
                }
            },
            {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "adminConfirm": {"$first": "$adminConfirm"},
                    "storeId": {"$first": "$storeId"},
                    "storeUserId": {"$first": "$storeUserId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "userName": {"$first": "$userName"},
                }
            },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
            {
                $count: "totalCount"
            }
        ]);
        return (data.length > 0 ? data[0].totalCount : 0)
    }


    async getRequestForUser(condition, typeData = {createAt: -1}, page = 1, limit = 500) {
        const data = await this.coll.aggregate([
            {
                $match: condition
            },
            {
                $limit: limit
            },
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    from: "shipping_histories",
                    let: {orderId: "$orderId"},
                    as: 'shippingInfo',
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$partner_id', '$$orderId']
                                }
                            }
                        },
                        {
                            $sort: { createAt: -1}
                        }
                    ],
                }
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $project: {
                    code: 1,
                    orderId: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    storeUserId: 1,
                    userBuyFullName: 1,
                    products: 1,
                    createAt: 1,
                    phone: 1,
                    province: 1,
                    district: 1,
                    ward: 1,
                    street: 1,
                    location: 1,
                    paymentMethod: 1,
                    isPayOnline: 1,
                    bankCode: 1,
                    address: 1,
                    status: 1, // 0 Chờ duyệt
                    coupon: 1,
                    shippingService: 1,
                    transportFee: 1,
                    totalWeight: 1,
                    totalPriceShop: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeProvince: {$arrayElemAt: ['$storeInfo.province', 0]},
                    storeDistrict: {$arrayElemAt: ['$storeInfo.district', 0]},
                    storeWard: {$arrayElemAt: ['$storeInfo.ward', 0]},
                    storeStreet: {$arrayElemAt: ['$storeInfo.street', 0]},
                    shippingInfo: 1,
                }
            },
            {$sort: typeData},
        ])
        if (data) {

            data.map(e =>{
                return this.filterShippingData(e.shippingInfo);
            });
            return this.completeData(data)
        } else {
            return data
        }
    }

    getTotalPage(condition, limit) {
        return new promise(async resolve => {
            let total = await this.countDataWhere(condition);
            let totalPage = Math.ceil(total / limit);
            return resolve(totalPage);
        })
    }

    async getRequestById(id) {
        let data = await this.coll.aggregate([
            {
                $match: {_id: ObjectId(id)}
            },
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            // {$unwind: "$products"},
            // {
            //     $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            // },
            // {
            //     $lookup: {
            //         localField: 'productObjectId',
            //         from: "products",
            //         foreignField: '_id',
            //         as: 'productInfo'
            //     }
            // },
            // {
            //     $addFields: {
            //         "products.thumbail": {$arrayElemAt: ['$productInfo.thumbail', 0]},
            //         "products.count": '$products.count',
            //         "products.name": {$arrayElemAt: ['$productInfo.name', 0]},
            //         "products.price": {$arrayElemAt: ['$productInfo.price', 0]},
            //         "products.trademark": {$arrayElemAt: ['$productInfo.trademark', 0]},
            //         "products.size": {$arrayElemAt: ['$productInfo.size', 0]},
            //         "products.classifies": '$products.classifies',
            //     }
            // },
            {
                $project: {
                    code: 1,
                    userId: 1,
                    paymentId: 1,
                    orderId: 1,
                    storeId: 1, // id chủ shop
                    storeUserId: 1,
                    isPayOnline: 1,
                    paymentMethod: 1,
                    products: 1,
                    createAt: 1,
                    modifyAt: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    phone: 1,
                    province: 1,
                    district: 1,
                    ward: 1,
                    street: 1,
                    location: 1,
                    address: 1,
                    shippingService: 1,
                    shippingCode: 1,
                    totalWeight: 1,
                    transportFee: 1,
                    point: 1,
                    totalPriceShop: 1,
                    coupon: 1,
                    imageConfirm: 1,
                    reasonCancel: 1,
                    // userBuyFullName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    userBuyFullName: 1,
                    userBuyEmail: {$arrayElemAt: ['$userInfo.email', 0]},
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeProvince: {$arrayElemAt: ['$storeInfo.province', 0]},
                    storeDistrict: {$arrayElemAt: ['$storeInfo.district', 0]},
                    storeWard: {$arrayElemAt: ['$storeInfo.ward', 0]},
                    storeStreet: {$arrayElemAt: ['$storeInfo.street', 0]},
                    storeLocation: {$arrayElemAt: ['$storeInfo.location', 0]},

                }
            },
            // {
            //     $group: {
            //         "_id": "$_id",
            //         "products": {"$push": "$products"},
            //         "userId": {"$first": "$userId"},
            //         "code": {"$first": "$code"},
            //         "shippingService": {"$first": "$shippingService"},
            //         "shippingCode": {"$first": "$shippingCode"},
            //         "storeId": {"$first": "$storeId"},
            //         "userBuyFullName": {"$first": "$userBuyFullName"},
            //         "userBuyEmail": {"$first": "$userBuyEmail"},
            //         "province": {"$first": "$province"},
            //         "district": {"$first": "$district"},
            //         "transportFee": {"$first": "$transportFee"},
            //         "ward": {"$first": "$ward"},
            //         "street": {"$first": "$street"},
            //         "location": {"$first": "$location"},
            //         "address": {"$first": "$address"},
            //         "phone": {"$first": "$phone"},
            //         "storeUserId": {"$first": "$storeUserId"},
            //         "createAt": {"$first": "$createAt"},
            //         "status": {"$first": "$status"},
            //         "codeString": {"$first": "$codeString"},
            //         "storeName": {"$first": "$storeName"},
            //         "storeAddress": {"$first": "$storeAddress"},
            //         "storeProvince": {"$first": "$storeProvince"},
            //         "storeDistrict": {"$first": "$storeDistrict"},
            //         "storeWard": {"$first": "$storeWard"},
            //         "storeStreet": {"$first": "$storeStreet"},
            //         "storeLocation": {"$first": "$storeLocation"},
            //         "coupon": {"$first": "$coupon"},
            //         "paymentMethod": {"$first": "$paymentMethod"},
            //         "isPayOnline": {"$first": "$isPayOnline"},
            //     }
            // }
        ]);
        if (data.length > 0) data = data[0];
        return this.completeData(data);
    }

    async getRequestByIdForAdmin(id) {
        let data = await this.coll.aggregate([
            {
                $match: {_id: ObjectId(id)}
            },
            {
                $addFields: {storeObjectId: {$toObjectId: '$shopId'}, userStoreObjectId: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'userStoreObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userStoreInfo'
                }
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {$unwind: "$products"},
            {
                $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            },
            {
                $lookup: {
                    localField: 'productObjectId',
                    from: "products",
                    foreignField: '_id',
                    as: 'productInfo'
                }
            },
            {
                $addFields: {
                    "products.thumbail": {$arrayElemAt: ['$productInfo.thumbail', 0]},
                    "products.count": '$products.count',
                    "products.name": {$arrayElemAt: ['$productInfo.name', 0]},
                    "products.price": {$arrayElemAt: ['$productInfo.price', 0]},
                    "products.trademark": {$arrayElemAt: ['$productInfo.trademark', 0]},
                    "products.size": {$arrayElemAt: ['$productInfo.size', 0]},
                }
            }, {
                $project: {
                    code: 1,
                    userId: 1,
                    storeId: 1, // id chủ shop
                    shopId: 1,
                    products: 1,
                    couponId: 1,
                    createAt: 1,
                    status: 1, // 0 Chờ duyệt
                    codeString: 1,
                    phone: 1,
                    province: 1,
                    district: 1,
                    ward: 1,
                    street: 1,
                    location: 1,
                    transportSystem: 1,
                    address: 1,
                    dvVanChuyen: 1,
                    maVanChuyen: 1,
                    userBuyFullName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    userBuyEmail: {$arrayElemAt: ['$userInfo.email', 0]},
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeProvince: {$arrayElemAt: ['$storeInfo.province', 0]},
                    storeDistrict: {$arrayElemAt: ['$storeInfo.district', 0]},
                    storeWard: {$arrayElemAt: ['$storeInfo.ward', 0]},
                    storeStreet: {$arrayElemAt: ['$storeInfo.street', 0]},
                    storeLocation: {$arrayElemAt: ['$storeInfo.location', 0]},
                    userStoreName: {$arrayElemAt: ['$userStoreInfo.fullName', 0]},
                    userStoreAddress: {$arrayElemAt: ['$userStoreInfo.address', 0]},
                    userStoreEmail: {$arrayElemAt: ['$userStoreInfo.email', 0]},
                    imageConfirm: 1,
                    adminConfirm: 1,
                }
            }, {
                $group: {
                    "_id": "$_id",
                    "products": {"$push": "$products"},
                    "userId": {"$first": "$userId"},
                    "code": {"$first": "$code"},
                    "dvVanChuyen": {"$first": "$dvVanChuyen"},
                    "maVanChuyen": {"$first": "$maVanChuyen"},
                    "storeId": {"$first": "$storeId"},
                    "userBuyFullName": {"$first": "$userBuyFullName"},
                    "province": {"$first": "$province"},
                    "district": {"$first": "$district"},
                    "ward": {"$first": "$ward"},
                    "street": {"$first": "$street"},
                    "location": {"$first": "$location"},
                    "transportSystem": {"$first": "$transportSystem"},
                    "address": {"$first": "$address"},
                    "phone": {"$first": "$phone"},
                    "shopId": {"$first": "$shopId"},
                    "createAt": {"$first": "$createAt"},
                    "status": {"$first": "$status"},
                    "codeString": {"$first": "$codeString"},
                    "storeName": {"$first": "$storeName"},
                    "couponId": {"$first": "$couponId"},
                    "storeAddress": {"$first": "$storeAddress"},
                    "storeProvince": {"$first": "$storeProvince"},
                    "storeDistrict": {"$first": "$storeDistrict"},
                    "storeWard": {"$first": "$storeWard"},
                    "storeStreet": {"$first": "$storeStreet"},
                    "userStoreName": {"$first": "$userStoreName"},
                    "userStoreAddress": {"$first": "$userStoreAddress"},
                    "userStoreEmail": {"$first": "$userStoreEmail"},
                    "imageConfirm": {"$first": "$imageConfirm"},
                    "adminConfirm": {"$first": "$adminConfirm"},
                }
            }
        ]);
        if (data.length > 0) data = data[0];
        return this.completeData(data);
    }

    updateRequests(id, data) {
        const that = this;
        return new promise(async resolve => {
            const rs = await that.updateById(id, data);
            ReportUtil.updateReportByOrderId(id, 0);
            return resolve(rs)
        })
    }

    deleteRequests(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    async getRequestByCondition(condition) {
        return this.completeData(await this.coll.aggregate([
            // {
            //     $addFields: {productObjectId: {$toObjectId: '$products.productId'}}
            // },
            // {
            //     $lookup: {
            //         localField: 'productObjectId',
            //         from: "products",
            //         foreignField: '_id',
            //         as: 'productInfo'
            //     }
            // },
            // {
            //     $addFields: {
            //         "products.price": {$arrayElemAt: ['$productInfo.price', 0]}
            //     }
            // }, {
            //     $project: {
            //         code: 1,
            //         userId: 1,
            //         storeId: 1, // id chủ shop
            //         shopId: 1,
            //         products: 1,
            //         createAt: 1,
            //         modifyAt: 1,
            //         type: 1, // 0 Chờ duyệt
            //         codeString: 1,
            //     }
            // },
            // {
            //     $group: {
            //         "_id": "$_id",
            //         "products": {"$push": "$products"},
            //         "userId": {"$first": "$userId"},
            //         "code": {"$first": "$code"},
            //         "storeId": {"$first": "$storeId"},
            //         "shopId": {"$first": "$shopId"},
            //         "createAt": {"$first": "$createAt"},
            //         "type": {"$first": "$type"},
            //     }
            // },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
        ]));
    }

    async getRequestByIdNoData(id) {
        return await this.getDataById(id)
    }

    async getOneRequestByConditionNoData(condition) {
        return await this.getDataWhere(condition, this.FIND_ONE())
    }

    filterShippingData(p) {
        function handleDataObject(d) {
            d.statusText = ghtkStatus[d.status_id];
            return d;
        }

        if (util.isArray(p)) {
            return p.map((e) => {
                return handleDataObject(e);
            });
        } else {
            return handleDataObject(p);
        }
    }

    async getOrderDetail(condition) {
        let data = await this.coll.aggregate([
            {
                $lookup: {
                    from: "shipping_histories",
                    let: {orderId: "$orderId"},
                    as: 'shippingInfo',
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$partner_id', '$$orderId']
                                }
                            }
                        },
                        {
                            $sort: { createAt: -1}
                        }
                    ],
                }
            },
            {
                $match: condition
            },
            {$sort: {createAt: -1}},
        ])
        if (data) {
            if (Array.isArray(data) && data.length > 0 && data[0].shippingInfo) {
                data[0].shippingInfo = this.filterShippingData(data[0].shippingInfo);
                return this.completeData(data[0])
            }
            // error
            return this.completeData(data)
        } else {
            return data
        }
    }
}

exports.MODEL = new Model();
