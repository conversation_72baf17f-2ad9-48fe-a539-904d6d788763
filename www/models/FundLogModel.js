"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/FundLogCol'))
    }

    addFundLog(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getFundLogById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getFundLogByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateFundLog(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteFundLog(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id);
            return resolve()
        })
    }

    getFundLogByCondition(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1}, limit)
            return resolve(data)
        })
    }

    getFundLogByConditionNear(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {ceateAt: -1}, limit)
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();