"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/MessageCol'))
    }

    addMessage(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getMessageById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getMessageByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateMessage(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data);
            return resolve()
        })
    }

    updateMessageByConditon(condition, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateWhereClause(condition, data)
            return resolve()
        })
    }

    deleteMessage(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    async getMessageByCondition(condition, typeData = null, limit = null) {
        return await this.getDataWhere(condition, this.FIND_MANY(), typeData, limit)
    }

    async getOneMessageByCondition(condition) {
        return await this.getDataWhere(condition, this.FIND_ONE())
    }

}

exports.MODEL = new Model();