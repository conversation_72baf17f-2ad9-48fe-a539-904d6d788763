"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const ProducModel = require('./ProducModel');
const ServicesModel = require('./ServicesModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/TimKiemCol'))
    }

    addSearch(userId, searchId, type) {
        const that = this;
        return new promise(async resolve => {
            let count = await that.countDataWhere({
                userId,
                searchId,
                type,
            });

            if (count == 0) {
                await that.insertData({
                    userId,
                    searchId,
                    type,
                });
            } else {
                await that.updateWhereClause({
                    userId,
                    searchId,
                    type,
                }, {$inc: {count: 1}});
            }
            return resolve();
        })
    }

    findSearchUser(userId, type) {
        const that = this;
        return new promise(async resolve => {
            let search = await that.getDataWhere({
                userId,
                type,
            }, that.FIND_MANY(), {modifyAt: -1}, 10);

            let mapP = [];
            search.forEach((e, i) => {
                mapP.push(new promise(async resolve => {
                    if (type == 'sp') {
                        search[i].info = await ProducModel.MODEL.getProductsById(e.searchId);
                    } else {
                        search[i].info = await ServicesModel.MODEL.getServicesById(e.searchId);
                    }
                    return resolve();
                }))
            });

            await promise.all(mapP);
            return resolve(search);
        })
    }
}

exports.MODEL = new Model();