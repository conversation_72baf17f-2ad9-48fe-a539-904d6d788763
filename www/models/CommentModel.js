"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const ServicesModel = require('../models/ServicesModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/CommenCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let count = await that.countDataWhere({});
            return resolve(count + 1);
        })
    }

    addComment(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName()
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getCommentById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getCommentIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateComment(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteComment(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getCommentByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }

    getCommentByConditionWithPage(condition, page, sort = null, limit = null, populate = '') {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit, populate);
            return resolve(data)
        })
    }

    getCommentRateTotal(condition) {
        return new promise(async resolve => {
            let rateTotal1 = await this.countDataWhere({rate: 1, ...condition});
            let rateTotal2 = await this.countDataWhere({rate: 2, ...condition});
            let rateTotal3 = await this.countDataWhere({rate: 3, ...condition});
            let rateTotal4 = await this.countDataWhere({rate: 4, ...condition});
            let rateTotal5 = await this.countDataWhere({rate: 5, ...condition});
            let totalComment = await this.countDataWhere({storeId: condition.storeId});
            let so_sao_1 = Number(rateTotal1)
            let so_sao_2 = Number(rateTotal2) * 2;
            let so_sao_3 = Number(rateTotal3) * 3;
            let so_sao_4 = Number(rateTotal4) * 4;
            let so_sao_5 = Number(rateTotal5) * 5;
            let sao_all = so_sao_1 + so_sao_2 + so_sao_3 + so_sao_4 + so_sao_5;
            let so_ng_rate = Number(rateTotal1) + Number(rateTotal2) + Number(rateTotal3) + Number(rateTotal4) + Number(rateTotal5);
            let rateTotalValue = (sao_all / so_ng_rate).toFixed(2);
            return resolve({rateTotal1, rateTotal2, rateTotal3, rateTotal4, rateTotal5, rateTotalValue, totalComment});
        })
    }

    getTotalPage(condition) {
        return new promise(async resolve => {
            let totalComment = await this.countDataWhere({storeId: condition.storeId});

            let totalPage = Math.ceil(totalComment / 10);
            return resolve({totalPage});
        })
    }

}

exports.MODEL = new Model();
