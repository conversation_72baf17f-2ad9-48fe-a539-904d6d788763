"use strict";
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/WalletOnlineCol'))
    }

    async updateWallet(obj) {
        let count = await this.countDataWhere({});
        if (count == 0) {
            await this.insertData({total: 0, storeValue: 0});
        }

        await this.updateWhereClause({}, obj);
    }

    async getWallet() {
        let wallet = await this.getDataWhere({}, this.FIND_ONE());
        if (!wallet) {
            this.insertData({total: 0, storeValue: 0});
            return {total: 0, storeValue: 0};
        }
        return wallet;
    }
}

exports.MODEL = new Model();