"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/BannerAppCol'))
    }

    addBanner(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getBannerById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getBannerByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    getAllBanners() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({ status: 0}, that.FIND_MANY(), { order: 1});
            return resolve(data)
        })
    }

    updateBanner(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteBanner(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getBannerByCondition(condition, limit = null, sort = {modifyAt: -1}) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), sort, limit);
            return resolve(data)
        })
    }

}

exports.MODEL = new Model();
