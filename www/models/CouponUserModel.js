"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');

class Model extends BaseModel {

    constructor() {
        super(require('../database/CouponUserCol'))
    }
    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let count = await that.countDataWhere({});
            return resolve(count + 1);
        })
    }

    addCouponUser(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName()
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getCouponUserByCondition(condition, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), sort, limit);
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
