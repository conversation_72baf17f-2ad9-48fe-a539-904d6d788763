"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/FcmTokenCol'))
    }

    addToken(userId, token) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere({token});
            await that.insertData({userId, token});
            return resolve();
        })
    }

    removeToken(token) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere({token});
            return resolve();
        })
    }

    async getAllTokenForUser(userId) {
        let data = await this.getDataWhere({ userId }, this.FIND_MANY(), {createAt: -1}, 5);
        return data.map(d => d.token);
    }

    async getAllTokenForUserByTypeOs(userId, os) {
        let data = await this.getDataWhere({ userId: userId, os: os }, this.FIND_MANY(), {createAt: -1}, 5);
        return data.map(d => d.token);
    }

    async removeAllTokenForUser(userId) {
        const that = this;
        return new promise(async resolve => {
            const rs = await that.removeDataWhere({userId});
            return resolve(rs);
        })
    }
}

exports.MODEL = new Model();
