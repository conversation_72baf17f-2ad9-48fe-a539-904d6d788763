"use strict";
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/PayingWalletLogCol'))
    }

    async addLogs(obj) {
        this.insertData(obj);
    }

    async getLogsData(where1, where, page, limit) {
        let skip = (page - 1) * limit;

        if (where.userId) {
            return await this.coll.aggregate([
                {
                    $match: where1
                },
                {$sort: {createAt: -1}},
                {$skip: skip},
                {$limit: limit},
                {
                    $match: where
                },
            ])
        } else {
            return await this.coll.aggregate([
                {
                    $match: where1
                },
                {$sort: {createAt: -1}},
                {$skip: skip},
                {$limit: limit},
                {
                    $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
                },
                {
                    $lookup: {
                        localField: 'userObjectId',
                        from: "users",
                        foreignField: '_id',
                        as: 'userInfo'
                    }
                },
                {
                    $addFields: {
                        "email": {$arrayElemAt: ['$userInfo.email', 0]},
                        "phone": {$arrayElemAt: ['$userInfo.phone', 0]},
                        "fullName": {$arrayElemAt: ['$userInfo.fullName', 0]},
                        "address": {$arrayElemAt: ['$userInfo.address', 0]},
                    }
                }, {
                    $project: {
                        email: 1,
                        phone: 1,
                        fullName: 1,
                        address: 1,
                        _id: 1,
                        amount: 1,
                        createAt: 1,
                        userId: 1,
                    }
                },
                {
                    $match: where
                },
            ])
        }

    }

    async getLogsCount(where1, where) {
        let data = await this.coll.aggregate([
            {
                $match: where1
            },
            {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $addFields: {
                    "email": {$arrayElemAt: ['$userInfo.email', 0]},
                    "phone": {$arrayElemAt: ['$userInfo.phone', 0]},
                    "fullName": {$arrayElemAt: ['$userInfo.fullName', 0]},
                    "address": {$arrayElemAt: ['$userInfo.address', 0]},
                }
            }, {
                $project: {
                    email: 1,
                    phone: 1,
                    fullName: 1,
                    address: 1,
                    _id: 1,
                    amount: 1,
                    createAt: 1,
                    userId: 1,
                }
            },
            {
                $match: where
            },
        ]);
        return data.length;
    }

    async removeLogs(id) {
        this.removeDataById(id);
    }
}

exports.MODEL = new Model();
