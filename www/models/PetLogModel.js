"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const _ = require('lodash');
class Model extends BaseModel {

    constructor() {
        super(require('../database/PetLogCol'))
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }

    getOneByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE())
            return resolve(data)
        })
    }

    async getLogs(condition) {
        let data = await this.coll.aggregate([
            {
                $addFields: {petObjectId: {$toObjectId: '$petId'}}
            },
            {
                $lookup: {
                    localField: 'petObjectId',
                    from: "pets",
                    foreignField: '_id',
                    as: 'petInfo'
                }
            },
            {
                $match: condition
            },
            {
                $project: {
                    petObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data : null)
    }

    async getLogsGroupByDateYear(condition) {
        let data = await this.coll.aggregate([
            {
                $addFields: {petObjectId: {$toObjectId: '$petId'}}
            },
            {
                $lookup: {
                    localField: 'petObjectId',
                    from: "pets",
                    foreignField: '_id',
                    as: 'petInfo'
                }
            },
            {
                $match: condition
            },
            {
              $sort: {
                  dateTime: -1
              }
            },
            {
                $project: {
                    petObjectId: 0,
                }
            }
        ]);
        let result = null;
        if (data.length > 0) {
            result = _.chain(data)
                .groupBy("dateTimeString")
                .map((value, key) => ({dateYear: key, data: value}))
                .value()
        }
        return result;
    }

    async getLog(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {petObjectId: {$toObjectId: '$petId'}}
            },
            {
                $lookup: {
                    localField: 'petObjectId',
                    from: "pets",
                    foreignField: '_id',
                    as: 'petInfo'
                }
            },
            {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    categoryObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data[0] : null)
    }
}

exports.MODEL = new Model();
