"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');

class Model extends BaseModel {

    constructor() {
        super(require('../database/ReportCol'))
    }
    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data && data.code) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }
    addReport(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName();
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }
    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }
    getOneByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(data)
        })
    }
    async getReport(condition) {

        return await this.coll.aggregate([
            {
                $match: condition
            },
            {
                $group: {
                    _id: null,
                    totalRevenue: {$sum: "$totalRevenue"},
                    totalBookSpa: {$sum: "$totalBookSpa"},
                    totalBookRoom: {$sum: "$totalBookRoom"},
                    totalBookClinic: {$sum: "$totalBookClinic"},
                    totalSale: {$sum: "$totalSale"},
                }
            },
            {
                $project: {
                    totalRevenue: 1,
                    totalBookSpa: 1,
                    totalBookRoom: 1,
                    totalBookClinic: 1,
                    totalSale: 1,
                    _id: 0
                }
            },
            {$sort: {createAt: 1}},


        ])
    }
    async getReportWithDate(condition) {

        let data = await this.coll.aggregate([
            {
                $match: condition
            },
            {
                $group: {
                    _id: null,
                    totalRevenue: {$sum: "$totalRevenue"},
                    totalBookSpa: {$sum: "$totalBookSpa"},
                    totalBookRoom: {$sum: "$totalBookRoom"},
                    totalBookClinic: {$sum: "$totalBookClinic"},
                    totalSale: {$sum: "$totalSale"},
                    arrDate: {$push: {$dateToString: {format: "%d-%m-%Y", date: "$fullDate"}}},
                    arrTotalRevenue: {$push: "$totalRevenue"},
                    arrTotalBookSpa: {$push: "$totalBookSpa"},
                    arrTotalBookRoom: {$push: "$totalBookRoom"},
                    arrTotalBookClinic: {$push: "$totalBookClinic"},
                    arrTotalSale: {$push: "$totalSale"},
                }
            },
            {
                $project: {
                    totalRevenue: 1,
                    totalBookSpa: 1,
                    totalBookRoom: 1,
                    totalBookClinic: 1,
                    totalSale: 1,
                    arrDate: 1,
                    arrTotalRevenue: 1,
                    arrTotalBookSpa: 1,
                    arrTotalBookRoom: 1,
                    arrTotalBookClinic: 1,
                    arrTotalSale: 1,
                    _id: 0
                }
            },
            {$sort: {createAt: 1}},
        ])
        if (data.length > 0) data = data[0];
        return data
    }
}

exports.MODEL = new Model();
