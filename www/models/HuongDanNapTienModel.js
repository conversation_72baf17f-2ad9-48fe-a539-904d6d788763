"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/HuongDanNapTienCol'))
    }

    updateData(data) {
        const that = this;
        return new promise(async resolve => {
            let count = await that.countDataWhere({});
            if (count == 0) {
                await that.insertData(data);
            } else {
                await that.updateWhereClause({}, data);
            }
            return resolve();
        })
    }

    getData() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE());
            if (!data) return resolve('');
            return resolve(data.content)
        })
    }
}

exports.MODEL = new Model();