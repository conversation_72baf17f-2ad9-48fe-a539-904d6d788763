"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/UserPointLogCol'))
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateLog(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteLog(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id);
            return resolve()
        })
    }

    getByCondition(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1}, limit)
            return resolve(data)
        })
    }

    getAllByConditionWithPage(condition, sort = null, page, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit);
            return resolve(data)
        })
    }

    getTotalPage(condition, limit) {
        return new promise(async resolve => {
            let totalData = await this.countDataWhere(condition);

            let totalPage = Math.ceil(totalData / limit);
            return resolve({totalPage});
        })
    }

}

exports.MODEL = new Model();
