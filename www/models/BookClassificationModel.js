"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const moment = require('moment')
const ReportUtil = require("../utils/ReportUtil");

class Model extends BaseModel {

    constructor() {
        super(require('../database/BookClassificationCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }


    create(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName();
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    async getDataIdNoData(id) {
        let data = await this.getDataById(id);
        return data
    }

    async getOneById(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            }, {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    storeManagerId: 1,
                    name: 1,
                    service: 1,
                    status: 1,
                    phone: 1,
                    email: 1,
                    fullName: 1, // Thêm fullName từ booking
                    bookType: 1,
                    price: 1,
                    priceAddition: 1, // Phụ Thu
                    note: 1,
                    isPayOnline: 1,
                    orderId: 1,
                    branchName: 1,
                    branchAddress: 1,
                    coupon: 1,
                    bankCode: 1,
                    paymentMethod: 1,
                    items: 1, // For API V2
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    createAt: 1,
                    modifyAt: 1
                }
            }
        ]);
        return (data.length > 0 ? data[0] : null)
    }

    getBookSpaByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    getOneByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(data)
        })
    }

    update(id, data) {
        const that = this;
        return new promise(async resolve => {
            const rs = await that.updateById(id, data)
            ReportUtil.updateReportByOrderId(id, 3);
            return resolve(rs)
        })
    }

    delete(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), typeData)
            return resolve(data)
        })
    }

    async getByConditionDetail(condition, typeData = {createAt: -1}) {
        return await this.coll.aggregate([
            {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    orderId: 1,
                    storeId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    storeManagerId: 1,
                    service: 1,
                    status: 1,
                    phone: 1,
                    email: 1,
                    fullName: 1, // Thêm fullName từ booking
                    bookType: 1,
                    createAt: 1,
                    modifyAt: 1,
                    price: 1,
                    paymentMethod: 1,
                    priceAddition: 1,
                    coupon: 1,
                    isPayOnline: 1,
                    branchName: 1,
                    branchAddress: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    avatarUser: {$arrayElemAt: ['$userInfo.picture', 0]},
                    items: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                }
            }
        ])
    }

    getBookingHistoryByConditionWithPage(condition, page, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit);
            return resolve(data)
        })
    }

    getTotalPage(condition, limit) {
        return new promise(async resolve => {
            let total = await this.countDataWhere(condition);
            let totalPage = Math.ceil(total / limit);
            return resolve(totalPage);
        })
    }

    async getDataForUser(condition, typeData = {createAt: -1}, page = 1, limit = 500) {
        let skip = (page - 1) * limit;
        return await this.coll.aggregate([
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    bookType: 1,
                    service: 1,
                    status: 1,
                    phone: 1,
                    email: 1,
                    fullName: 1, // Thêm fullName từ booking
                    typePet: 1,
                    items: 1,
                    serviceDetail: 1,
                    note: 1,
                    price: 1,
                    branchAddress: 1,
                    branchName: 1,
                    branchPhone: 1,
                    coupon: 1,
                    bankCode: 1,
                    paymentMethod: 1,
                    isPayOnline: 1,
                    orderId: 1,
                    createAt: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeManagerId: {$arrayElemAt: ['$storeInfo.userId', 0]},
                }
            },
            {
                $skip: skip
            }, {
                $limit: limit
            }
        ])
    }

    async getReportLabelValueByDate(condition) {

        return await this.coll.aggregate([
            {
                $addFields: {
                    dateModify: {$dateToString: {format: "%d-%m-%Y", date: {$toDate: "$modifyAt"}}},
                    dateModify2: {$toDate: "$modifyAt"}
                }
            },
            {
                $match: condition
            },
            {
                $group: {
                    _id: "$dateModify",
                    // _id: {
                    //     $add: [
                    //         {
                    //             $dayOfYear: "$dateModify2"
                    //         },
                    //         {
                    //             $multiply: [400, {$year: "$dateModify2"}]
                    //         }
                    //     ]
                    // },
                    totalPrice: {$sum: "$price"},
                    dateModify: {"$first": "$dateModify"},
                    modifyAt: {"$first": "$modifyAt"},
                    count: {"$sum": 1},
                    // code: {"$push": "$code"},
                    // fullDate: {$min: "$dateModify2"}
                }
                // $group: {
                //     _id: "$dateModify",
                //     totalPrice: {$sum: "$price"},
                //     count: {$sum: 1}
                // }
            },
            {
                $project: {
                    date: "$dateModify",
                    totalPrice: 1,
                    modifyAt: 1,
                    count: 1,
                    _id: 0
                }
            },
            {$sort: {modifyAt: 1}},
            // {$sort: {"modifyAt": 1}},


        ])
    }

    async getRevenueValue(condition) {

        let data = await this.coll.aggregate([
            {
                $match: condition
            },
            {
                $group: {
                    _id: null,
                    totalPrice: {$sum: "$price"},
                }
            },
            {
                $project: {
                    totalPrice: 1,
                    _id: 0
                }
            },
            {$sort: {modifyAt: 1}},
        ]);
        return data ? data[0].totalPrice : 0;
    }
}

exports.MODEL = new Model();
