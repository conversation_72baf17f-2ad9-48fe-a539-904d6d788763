"use strict";
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/SettingAdminCol'))
    }

    async getSettingAdmin() {
        let data = await this.getDataWhere({}, this.FIND_ONE());
        if (!data) {
            data = await this.insertData({
                percent: 0,
                fee: 0,
                email: '',
                username: '',
                password: '',
                timeMinute: 30,
                point: 10000
            })
        }
        return data
    }

    async updateSettingAdmin(data) {
        await this.updateWhereClause({}, data);
        return data
    }
}

exports.MODEL = new Model();
