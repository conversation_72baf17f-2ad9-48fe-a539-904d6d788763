"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');
const ghtkStatus = require('../config/CfShippingStatus');
const util = require('util');
class Model extends BaseModel {

    constructor() {
        super(require('../database/ShippingCol'))
    }
    filterData(p) {
        function handleDataObject(d) {
            d.statusText = ghtkStatus[d.status_id];
            return d;
        }

        if (util.isArray(p)) {
            return p.map((e) => {
                return handleDataObject(e);
            });
        } else {
            return handleDataObject(p);
        }
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }
    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(this.filterData(data))
        })
    }
    getOneByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(this.filterData(data))
        })
    }
}

exports.MODEL = new Model();
