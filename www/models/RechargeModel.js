"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/RechargeCol'))
    }

    addRecharge(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }


    getRechargeByCondition(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1}, limit)
            return resolve(data)
        })
    }

    async getRechargeByConditionForSystem() {
        return await this.coll.aggregate([
            {
                $addFields: {userIdObject: {$toObjectId: '$userId'}}
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'userIdObject',
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    count: 1,
                    createAt: 1,
                    fullName: {$arrayElemAt: ['$userInfo.fullName', 0]}
                }
            }
        ]);

    }


}

exports.MODEL = new Model();