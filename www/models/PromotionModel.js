"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/PromotionCol'))
    }

    addPromotion(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getPromotionById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getPromotionByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}},that.FIND_MANY());
            return resolve(data)
        })
    }

    updatePromotion(id,data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id,data)
            return resolve()
        })
    }

    deletePromotion(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getPromotionByCondition(condition,limit = null) {
        const that = this;
        return new promise(async resolve => {
            let result = await that.getDataWhere(condition,that.FIND_MANY(),{createAt: -1},limit);

            resolve({
                result,
            });
        })
    }
    getOnePromotionByCondition(condition,limit = null) {
        const that = this;
        return new promise(async resolve => {
            let result = await that.getDataWhere(condition,that.FIND_ONE(),{createAt: -1},limit);
            return resolve(result)
        })
    }
}

exports.MODEL = new Model();
