"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/UserViewedCol'))
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    getDataViewedByIds(ids, field = 'productIds') {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateViewed(field, condition, fieldId) {
        const that = this;
        const data = {
            $addToSet: {[field]: fieldId}
        }
        return new promise(async resolve => {
            const rs = await this.coll.findOneAndUpdate(condition, data, {upsert: true, new: true})
            return resolve(rs)
        })
    }

    getViewByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(data)
        })
    }

    delete(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }
}

exports.MODEL = new Model();
