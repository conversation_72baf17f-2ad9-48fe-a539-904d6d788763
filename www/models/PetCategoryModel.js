"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/PetCategoryCol'))
    }
    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }
    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }
    getOneByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE())
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
