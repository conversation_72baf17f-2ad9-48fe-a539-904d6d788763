"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/UserSetting'));
    }

    addDefaultSetting(data) {
        const that = this;
        return new promise(async resolve => {
            await that.insertData(data);
            return resolve()
        })
    }

    async getSetting(userId) {
        let s = await this.getDataWhere({userId}, this.FIND_ONE(), {createAt: -1});
        if (!s) {
            s = {
                userId: userId,
                nhanTinNhanTuNguoiKhac: 1,
                nhanThongBaoMuaHangDatLich: 1,
                nhanThongBaoTinNhan: 1
            }
        }
        return s
    }

    async updateSetting(userId, dataUpdate) {
        let c = await this.countDataWhere({userId});
        if (c == 0) {
            await this.insertData({
                userId, ...dataUpdate
            })
        } else {
            await this.updateWhereClause({
                userId
            }, dataUpdate);
        }
    }
}

exports.MODEL = new Model();