"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/BranchCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data && data.code) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }

    addBranch(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName();
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getBranchById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    updateBranch(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    deleteBranchByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere(condition);
            return resolve()
        })
    }

    getBranchByCondition(condition, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), sort, limit);
            return resolve(data)
        })
    }

    getBranchByLocation(location, maxDistance = 5000, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({
                location: {
                    $near: {
                        $geometry: {
                            type: 'Point',
                            coordinates: [location.lng, location.lat]
                        },
                        $maxDistance: maxDistance,
                    }
                }
            }, that.FIND_MANY(), sort, limit);
            return resolve(data)
        })
    }

    getBranchByConditionWithPage(condition, page, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit);
            return resolve(data)
        })
    }

    async tangLuotDangKy(storeId) {
        await this.updateById(storeId, {$inc: {luotDangKy: 1}});
    }

    searchBranchByLocation(type, conditionBranch, location, maxDistance = 5000, sort = null, page = 1, limit = null) {

        const that = this;

        // let countType = {};

        // if (type == 1) {
        //     countType = {
        //         countClinic: {$gt: 0}
        //     }
        // }
        // if (type == 2) {
        //     countType = {
        //         countRoom: {$gt: 0}
        //     }
        // }
        // if (type == 3) {
        //     countType = {
        //         countService: {$gt: 0}
        //     }
        // }


        const pipeline = [
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            // {
            //     $lookup: {
            //         localField: 'storeObjectId',
            //         from: "services",
            //         foreignField: '_id',
            //         as: 'storeInfo'
            //     }
            // },
            {
                $lookup: {
                    localField: 'storeId',
                    from: "data_searchs",
                    foreignField: 'serviceId',
                    as: 'storeInfo'
                }
            },
            {
                $unwind: '$storeInfo'
            },
            {
                $group: {
                    _id: "$storeId",
                    name: {$first: "$name"},
                    dist: {$first: "$dist"},
                    storeInfo: {$first: "$storeInfo"},
                }
            },
            {$replaceRoot: {newRoot: {$mergeObjects: ["$storeInfo", {'dist': "$dist"}]}}}, // add more field dist for distance km
            {
                $sort: sort
            },
            // {
            //     $project: {
            //         content: 0
            //     }
            // },
        ]
        if (location) {
            const geoNear = {
                $geoNear: {
                    near: {
                        type: "Point",
                        coordinates: [location.lng, location.lat]
                    },
                    distanceField: "dist.calculated",
                    maxDistance: maxDistance,
                    query: conditionBranch,
                    includeLocs: "dist.location",
                    spherical: true,
                }
            };
            pipeline.unshift(geoNear);
        }
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline,
                // {
                //     $match: countType
                // },
                {
                    $group: {
                        _id: null,
                        count: {
                            $sum: 1
                        }
                    }
                }]);
            if (!countData.length) {
                resolve({
                    result: [],
                    total: 0,
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline,
                // {
                //     $match: countType
                // },
                {
                    $addFields: {_id: {$toString: '$serviceId'}}
                },
                {
                    $skip: (page - 1) * limit
                }, {
                    $limit: limit
                }]);
            const totalPage = Math.ceil(countData[0].count / limit);
            resolve({
                result,
                total: countData[0].count,
                totalPage
            });
        })
        // return new promise(async resolve => {
        //     const pipeline = '';
        //     let data = await that.coll.aggregate([
        //         {
        //             $geoNear: {
        //                 near: {
        //                     type: "Point",
        //                     coordinates: [location.lng, location.lat]
        //                 },
        //                 // key: 'location',
        //                 distanceField: "dist.calculated",
        //                 maxDistance: maxDistance,
        //                 query: conditionBranch,
        //                 includeLocs: "dist.location",
        //                 spherical: true
        //             }
        //         },
        //         {
        //             $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
        //         },
        //         {
        //             $lookup: {
        //                 localField: 'storeObjectId',
        //                 from: "services",
        //                 foreignField: '_id',
        //                 as: 'storeInfo'
        //             }
        //         },
        //         {
        //             $unwind: '$storeInfo'
        //         },
        //         {
        //             $group: {
        //                 _id: "$storeId",
        //                 name: {$first: "$name"},
        //                 dist: {$first: "$dist"},
        //                 storeInfo: {$first: "$storeInfo"},
        //             }
        //         },
        //         {$replaceRoot: {newRoot: {$mergeObjects: ["$storeInfo", {'dist': "$dist"}]}}},
        //         {
        //             $sort: {
        //                 "dist.calculated": 1
        //             }
        //         },
        //         // {
        //         //     $project: {
        //         //         name: 1,
        //         //         address: 1,
        //         //         dist: 1,
        //         //         storeInfo: 1,
        //         //     }
        //         // },
        //     ])
        //     console.log('Data Length', data.length)
        //     return resolve(data)
        // })

    }
}

exports.MODEL = new Model();
