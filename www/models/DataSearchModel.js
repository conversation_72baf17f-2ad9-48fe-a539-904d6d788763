"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/DataSearchCol'))
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    getById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    update(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    updateData(condition, data) {
        return new promise(async resolve => {
            const rs = await this.coll.findOneAndUpdate(condition, data, {upsert: true, new: true})
            return resolve(rs)
        })
    }

    delete(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getByCondition(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {modifyAt: -1}, limit);
            return resolve(data)
        })
    }
    getOneByCondition(condition, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), {modifyAt: -1}, limit);
            return resolve(data)
        })
    }

    getAllByConditionWithPage(condition, sort = null, page, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit);
            return resolve(data)
        })
    }

    getAllFavoritesByConditionWithPage(conditionBranch, location, sort = null, page = 1, limit = null) {

        const that = this;

        const pipeline = [
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            // {
            //     $lookup: {
            //         localField: 'storeObjectId',
            //         from: "services",
            //         foreignField: '_id',
            //         as: 'storeInfo'
            //     }
            // },
            {
                $lookup: {
                    localField: 'storeId',
                    from: "data_searchs",
                    foreignField: 'serviceId',
                    as: 'storeInfo'
                }
            },
            {
                $unwind: '$storeInfo'
            },
            {
                $group: {
                    _id: "$storeId",
                    name: {$first: "$name"},
                    dist: {$first: "$dist"},
                    storeInfo: {$first: "$storeInfo"},
                }
            },
            {$replaceRoot: {newRoot: {$mergeObjects: ["$storeInfo", {'dist': "$dist"}]}}}, // add more field dist for distance km
            {
                $sort: sort
            },
            // {
            //     $project: {
            //         content: 0
            //     }
            // },
        ]
        if (location) {
            const geoNear = {
                $geoNear: {
                    near: {
                        type: "Point",
                        coordinates: [location.lng, location.lat]
                    },
                    distanceField: "dist.calculated",
                    query: conditionBranch,
                    includeLocs: "dist.location",
                    spherical: true,
                }
            };
            pipeline.unshift(geoNear);
        }
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline,
                // {
                //     $match: countType
                // },
                {
                    $group: {
                        _id: null,
                        count: {
                            $sum: 1
                        }
                    }
                }]);
            if (!countData.length) {
                resolve({
                    result: [],
                    total: 0,
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline,
                // {
                //     $match: countType
                // },
                {
                    $addFields: {_id: {$toString: '$serviceId'}}
                },
                {
                    $skip: (page - 1) * limit
                }, {
                    $limit: limit
                }]);
            const totalPage = Math.ceil(countData[0].count / limit);
            resolve({
                result,
                total: countData[0].count,
                totalPage
            });
        })

    }

    getTotalPage(condition, limit) {
        return new promise(async resolve => {
            let totalData = await this.countDataWhere(condition);

            let totalPage = Math.ceil(totalData / limit);
            return resolve({totalPage});
        })
    }
}

exports.MODEL = new Model();
