"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const _ = require('lodash');
class Model extends BaseModel {

    constructor() {
        super(require('../database/PetGalleryCol'))
    }

    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }

    getOneByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE())
            return resolve(data)
        })
    }

    async getGalleries(condition) {
        let data = await this.coll.aggregate([
            {
                $addFields: {petObjectId: {$toObjectId: '$petId'}}
            },
            {
                $lookup: {
                    localField: 'petObjectId',
                    from: "pets",
                    foreignField: '_id',
                    as: 'petInfo'
                }
            },
            {
                $match: condition
            },
            {
                $project: {
                    petObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data : null)
    }

    async getOne(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {petObjectId: {$toObjectId: '$petId'}}
            },
            {
                $lookup: {
                    localField: 'petObjectId',
                    from: "pets",
                    foreignField: '_id',
                    as: 'petInfo'
                }
            },
            {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    categoryObjectId: 0,
                }
            }
        ]);
        return (data.length > 0 ? data[0] : null)
    }
}

exports.MODEL = new Model();
