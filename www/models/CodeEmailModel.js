"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtil = require('../utils/StringUtils');

class Model extends BaseModel {

    constructor() {
        super(require('../database/CodeEmailCol'))
    }

    randomCodeEmail(email, count) {
        const that = this;
        return new promise(async resolve => {
            let code = StringUtil.randomStringFixLengthOnlyAlphabet(count);
            await that.updateWhereClause({email}, {status: 1});
            await that.insertData({email, code});
            return resolve(code);
        })
    }

    getCode(email, code) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({email, code, status: 0}, that.FIND_ONE());
            await that.updateWhereClause({email}, {status: 1});
            return resolve(data);
        })
    }
}

exports.MODEL = new Model();