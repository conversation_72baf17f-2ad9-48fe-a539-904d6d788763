"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;
const ReportUtil = require("../utils/ReportUtil");

class Model extends BaseModel {

    constructor() {
        super(require('../database/BookExaminationCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }


    addBookExamination(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName();
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    async getBookExaminationById(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            }, {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    name: 1, // Tên hàng hóa
                    petSex: 1, // Ảnh bìa
                    weight: 1, // Ảnh con
                    symptom: 1, // Thương hiệu
                    status: 1,  // Mo ta
                    phone: 1,
                    typePet: 1,
                    email: 1,
                    storeManagerId: 1,
                    branchName:1,
                    branchAddress: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    timeCheckOut: 1,
                    note: 1,
                    datePrice: 1,
                    dateTime: 1,
                    countRooms: 1,
                    isPayOnline: 1,
                    orderId: 1,
                    paymentMethod: 1,
                    items: 1, // For API V2
                    createAt: 1,
                    modifyAt: 1,
                    service: 1,
                    price: 1,
                    priceAddition: 1, // Phụ Thu
                    coupon: 1,
                    bankCode: 1,
                }
            }
        ]);
        return (data.length > 0 ? data[0] : null)
    }

    async getBookExaminationByIdNoData(id) {
        let data = await this.getDataById(id);
        return data
    }

    getBookExaminationByIds(ids) {
        const that = this;
        return new promise(async resolve => {
            let nIds = ids.map(i => {
                return ObjectId(i);
            })
            let data = await that.getDataWhere({_id: {$in: nIds}}, that.FIND_MANY());
            return resolve(data)
        })
    }

    updateBookExamination(id, data) {
        const that = this;
        return new promise(async resolve => {
            const rs = await that.updateById(id, data);
            ReportUtil.updateReportByOrderId(id, 1);
            return resolve(rs)
        })
    }

    deleteBookExamination(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id)
            return resolve()
        })
    }

    getBookExaminationByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), typeData)
            return resolve(data)
        })
    }

    getOneBookExaminationByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(data)
        })
    }

    async getBookExaminationByConditionDetail(condition, typeData = {createAt: -1}) {
        return await this.coll.aggregate([
            {
                $addFields: {userObjectId:{$convert: {input: '$userId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'userObjectId',
                    from: "users",
                    foreignField: '_id',
                    as: 'userInfo'
                }
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    storeManagerId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    name: 1, // Tên hàng hóa
                    petSex: 1, // Ảnh bìa
                    weight: 1, // Ảnh con
                    symptom: 1, // Thương hiệu
                    status: 1,  // Mo ta
                    phone: 1,
                    typePet: 1,
                    email: 1,
                    createAt: 1,
                    modifyAt: 1,
                    price: 1,
                    branchName:1,
                    branchAddress: 1,
                    orderId: 1,
                    userName: {$arrayElemAt: ['$userInfo.fullName', 0]},
                    avatarUser: {$arrayElemAt: ['$userInfo.picture', 0]},
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    service: 1,
                    paymentMethod: 1,
                    priceAddition: 1,
                    coupon: 1,
                    isPayOnline: 1,
                    items: 1,
                }
            }
        ])
    }

    async getBookExaminationForUser(condition, typeData = {createAt: -1}) {
        return await this.coll.aggregate([
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    code: 1,
                    timeCheckIn: 1,
                    petSex: 1,
                    weight: 1,
                    symptom: 1,
                    status: 1,
                    phone: 1,
                    typePet: 1,
                    email: 1,
                    items: 1,
                    serviceDetail: 1,
                    note: 1,
                    price: 1,
                    branchAddress: 1,
                    branchName: 1,
                    branchPhone:1,
                    orderId: 1,
                    createAt: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeAddress: {$arrayElemAt: ['$storeInfo.address', 0]},
                    storeManagerId: {$arrayElemAt: ['$storeInfo.userId', 0]},
                    bankCode: 1,
                    paymentMethod: 1,
                    isPayOnline: 1,
                }
            }
        ])
    }
    async getReportLabelValueByDate(condition) {

        return await this.coll.aggregate([
            {
                $addFields: {
                    dateModify: {$dateToString: {format: "%d-%m-%Y", date: {$toDate: "$modifyAt"}}},
                    dateModify2: {$toDate: "$modifyAt"}
                }
            },
            {
                $match: condition
            },
            {
                $group: {
                    _id: "$dateModify",
                    // _id: {
                    //     $add: [
                    //         {
                    //             $dayOfYear: "$dateModify2"
                    //         },
                    //         {
                    //             $multiply: [400, {$year: "$dateModify2"}]
                    //         }
                    //     ]
                    // },
                    totalPrice: {$sum: "$price"},
                    dateModify: {"$first": "$dateModify"},
                    modifyAt: {"$first": "$modifyAt"},
                    count: {"$sum": 1},
                    // fullDate: {$min: "$dateModify2"}
                }
                // $group: {
                //     _id: "$dateModify",
                //     totalPrice: {$sum: "$price"},
                //     count: {$sum: 1}
                // }
            },
            {
                $project: {
                    date: "$dateModify",
                    totalPrice: 1,
                    modifyAt: 1,
                    count: 1,
                    _id: 0
                }
            },
            {$sort: {modifyAt: 1}},
            // {$sort: {"modifyAt": 1}},


        ])
    }
}

exports.MODEL = new Model();
