"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');
const ObjectId = require('mongoose').Types.ObjectId;
const util = require('util');

class Model extends BaseModel {

    constructor() {
        super(require('../database/ServiceOfSpaCol'))
    }

    completeData(p) {
        function handleDataObject(d) {
            d.typeProductText = d.typeProduct == 0 ? 'Dừng phục vụ!' : 'Sẵn sàng phục vụ!';
            return d;
        }

        if (util.isArray(p)) {
            return p.map((e) => {
                return handleDataObject(e);
            });
        } else {
            return handleDataObject(p);
        }
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1
            if (data) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }

    addService(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName()
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    async getServiceById(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    branchId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    shortDes: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    address: {$arrayElemAt: ['$storeInfo.address', 0]}
                }
            }
        ]);
        return (data.length > 0 ? this.completeData(data[0]) : null)
    }

    updateService(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    async getCountServiceByCondition(condition) {
        return await this.countDataWhere(condition)
    }

    async getServiceByConditionForStore(condition, typeData = 0, limit = 0) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]}
                }
            },
            {
                $match: {storeStatus: {$ne: 2}}
            }
        ]));
    }

    async getServiceMoiNhat() {
        return this.completeData(await this.coll.aggregate([
            {
                $match: {status: 1}
            },
            {$sort: {createAt: -1}},
            {$limit: 30},
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$service.status', 0]}
                }
            },
            {
                $match: {storeStatus: {$ne: 2}}
            }
        ]));
    }

    async getServiceConditionSell(condition, typeData = null, limit = null) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: condition
            },

            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]},
                }
            }, {
                $match: {storeStatus: 1}
            }, {
                $limit: limit
            },
        ]));
    }

    async getServiceForCategory(categoryId, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: {categoryId, status: 1}
            },
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]}
                }
            }
        ]));
    }

    async getServicePageByCondition(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            // {
            //     $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            // },
            // {
            //     $lookup: {
            //         localField: 'categoryObjectId',
            //         from: "categories",
            //         foreignField: '_id',
            //         as: 'categoryInfo'
            //     }
            // },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    shortDes: 1,  // Mo ta ngan
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    isFeatured: 1,
                    branchId: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
            {$sort: {createAt: -1, watched: -1}},
        ]));
    }

    async getCountServicePageByCondition(condition) {
        return (await this.coll.aggregate([
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            // {
            //     $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            // },
            // {
            //     $lookup: {
            //         localField: 'categoryObjectId',
            //         from: "categories",
            //         foreignField: '_id',
            //         as: 'categoryInfo'
            //     }
            // },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    // categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {$sort: {watched: -1, createAt: -1}},
        ])).length
    }

    async searchService(search, categoryId, page, limit) {
        let matchData = {
            $or: [
                {'nameUTF': {'$regex': StringUtils.removeUtf8(search)}},
                {'addressUTF': {'$regex': StringUtils.removeUtf8(search)}},
            ],
            status: 1
        };
        let skip = (page - 1) * limit;

        if (categoryId) matchData.categoryId = categoryId;

        return this.completeData(await this.coll.aggregate([
            {
                $match: matchData
            },
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]},
                }
            }, {
                $match: {storeStatus: 1}
            }, {
                $skip: skip
            }, {
                $limit: limit
            }
        ]));
    }

    async getServiceForStore(storeId) {
        return this.completeData(await this.coll.aggregate([
            {
                $match: {storeId, status: 1}
            },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    description: 1,  // Mo ta
                    price: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: đang phục vụ || 1 : dừng phục vụ
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeService: 1,//sẵn sàng phục vụ
                }
            }
        ]));
    }

    addCountRevenueService(productId, count) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(productId, {
                $inc: {revenue: count}
            });
            return resolve()
        })
    }

    removeServiceByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere(condition);
            return resolve()
        })
    }

    getServiceByConditionNoData(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY());
            return resolve(data)
        })
    }
    getAllService() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_MANY(), {name: 1})
            return resolve(data)
        })
    }
    getServiceTotal(condition) {
        return new promise(async resolve => {
            let totalService = await this.countDataWhere({storeId: condition.storeId,status:condition.status});
            return resolve({totalService});
        })
    }
}

exports.MODEL = new Model();
