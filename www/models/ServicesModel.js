"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const ObjectId = require('mongoose').Types.ObjectId;

class Model extends BaseModel {

    constructor() {
        super(require('../database/ServicesCol'))
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1;
            if (data && data.code) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }

    addServices(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName();
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getServicesById(id) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataById(id);
            return resolve(data)
        })
    }

    async getServiceWithBranchById(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {
                    serviceId: {
                        $toString: '$_id'
                    }
                }
            },
            {
                $lookup: {
                    from: "branches",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "branches"
                }
            },
            {
                $match: {_id: ObjectId(id)}
            },
        ]);
        return (data.length > 0 ? data[0] : null)
    }

    updateServices(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve(data)
        })
    }

    deleteServicesByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere(condition);
            return resolve()
        })
    }

    deleteServicesById(id) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataById(id);
            return resolve()
        })
    }

    getServicesByCondition(condition, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), sort, limit);
            return resolve(data)
        })
    }

    getServicesByConditionWithPage(condition, page, sort = null, limit = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataForPage(condition, that.FIND_MANY(), page, sort, limit);
            return resolve(data)
        })
    }


    async tangLuotDangKy(storeId) {
        await this.updateById(storeId, {$inc: {luotDangKy: 1}});
    }

    getAllService() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_MANY(), {name: 1})
            return resolve(data)
        })
    }

    getTotalPage(condition) {
        return new promise(async resolve => {
            let totalComment = await this.countDataWhere(condition);
            let totalPage = Math.ceil(totalComment / 10);
            return resolve({totalPage});
        })
    }

    /**
     * THANHNX
     * @param {*} condition
     */
    countPage(condition) {
        return new promise(async resolve => {
            let totalComment = await this.countDataWhere(condition);
            let totalPage = Math.ceil(totalComment / 10);
            return resolve(totalPage);
        })
    }

    getServicesByConditionWithPageLookup(condition, page, limit = 10, sort = null) {
        let countType = {};
        let type = condition.businessTypes ? condition.businessTypes['$regex'] : null;
        if (type == 1) {
            countType = {
                countClinic: {$gt: 0}
            }
        }
        if (type == 2) {
            countType = {
                countRoom: {$gt: 0}
            }
        }
        if (type == 3) {
            countType = {
                countService: {$gt: 0}
            }
        }

        const pipeline = [
            {
                $addFields: {
                    serviceId: {
                        $toString: '$_id'
                    }
                }
            },
            {
                $lookup: {
                    from: "room_hotels",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "roomHotels"
                }
            },
            {
                $lookup: {
                    from: "serviceofspas",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "serviceofspas"
                }
            },
            {
                $lookup: {
                    from: "service_of_clinics",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "clinics"
                }
            },
            {
                $addFields: {
                    countService: {$size: "$serviceofspas"},
                }
            },
            {
                $addFields: {
                    countRoom: {$size: "$roomHotels"},
                }
            },
            {
                $addFields: {
                    countClinic: {$size: "$clinics"},
                }
            },
            {
                $lookup: {
                    from: "branches",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "branches"
                }
            },
            {
                $match: condition
            },
            {
                $match: countType
            },
            {
                $sort: sort
            },
        ];
        const that = this;
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline, {
                $group: {
                    _id: null,
                    count: {
                        $sum: 1
                    }
                }
            }]);
            if (!countData.length) {
                resolve({
                    result: [],
                    total: 0,
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline, {
                $skip: (page - 1) * limit
            }, {
                $limit: limit
            }]);
            const totalPage = Math.ceil(countData[0].count / limit);
            resolve({
                result,
                total: countData[0].count,
                totalPage
            });
        })
    }

    async getAll() {
        return await this.coll.aggregate([
            {
                $match: {status: 1}
            },
            {$sort: {createAt: -1}},
            // {$limit: 500},
            {
                $project: {
                    storeId: 1,
                    name: 1,
                }
            }
        ]);
    }

    async getListOfPromotion(servicesIds, sort = {createAt: -1}, limit = 100) {
        let serviceOjbIds = [];
        servicesIds.map(serviceId => {
            serviceOjbIds.push(ObjectId(serviceId))
        })
        return await this.coll.aggregate([
            {
                $match: {
                    _id: {$in: serviceOjbIds}
                }
            },
            {
                $addFields: {
                    serviceId: {
                        $toString: '$_id'
                    }
                }
            },
            {
                $addFields: {
                    branches: '$branches'
                }
            },
            {
                $lookup: {
                    from: "branches",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "branches"
                }
            },
            {$sort: sort},
            {$limit: limit},
            // {
            //     $project: {
            //         storeId: 1,
            //         name: 1,
            //     }
            // }
        ]);
    }

    async getServicesWithProvince() {
        const condition = {
            status: 1,
            province: {$ne: null},
            provinceUTF: {$ne: null},
        }
        return await this.coll.aggregate([
            {
                $match: condition
            },
            {
                $lookup: {
                    from: "provinces",
                    localField: "provinceUTF",
                    foreignField: "nameUTF",
                    as: "provinceInfo"
                }
            },
            {
                $group: {
                    _id: "$province",
                    totalBrand: {$sum: 1},
                    province: {$first: "$province"},
                    provinceUTF: {$first: "$provinceUTF"},
                    provinceInfo: {$first: "$provinceInfo"},
                }
            },
            {
                $project: {
                    province: 1,
                    provinceUTF: 1,
                    totalBrand: 1,
                    provinceInfo: 1,
                    _id: 0
                }
            },
            {$sort: {province: 1}},
        ])
    }

    async getAllForDataSearch() {
        let condition = {
            status: 1
        };
        return await this.coll.aggregate([
            {
                $addFields: {
                    serviceId: {
                        $toString: '$_id'
                    }
                }
            },
            {
                $lookup: {
                    from: "room_hotels",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "roomHotels"
                }
            },
            {
                $lookup: {
                    from: "serviceofspas",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "serviceofspas"
                }
            },
            {
                $lookup: {
                    from: "service_of_clinics",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "clinics"
                }
            },
            {
                $lookup: {
                    from: "classification_of_brand",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "classifications"
                }
            },
            {
                $addFields: {
                    countService: {$size: "$serviceofspas"},
                }
            },
            {
                $addFields: {
                    countRoom: {$size: "$roomHotels"},
                }
            },
            {
                $addFields: {
                    countClinic: {$size: "$clinics"},
                }
            },
            {
                $addFields: {
                    countClassification: {$size: "$classifications"},
                }
            },
            {
                $match: condition
            },
            {
                $lookup: {
                    from: "branches",
                    localField: "serviceId",
                    foreignField: "storeId",
                    as: "branches"
                }
            },
            {
                $sort: {
                    createAt: -1
                }
            },
            {
                $limit: 5000
            },
            {
                $project: {
                    content: 0,
                }
            }
        ]);
    }
}

exports.MODEL = new Model();
