"use strict";

const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/HoTroCol'))
    }

    async updatePost(obj) {
        let c = await this.countDataWhere({});
        if (c == 0) {
            await this.insertData(obj);
        } else {
            await this.updateWhereClause({}, obj);
        }
    }

    async getPosts() {
        let c = await this.getDataWhere({}, this.FIND_ONE(), {createAt: -1});
        if (c) {
            return c
        } else {
            return {
                trungTamHoTro: '',
                chinhSachBaoMat: '',
                dieuKhoanDichVu: '',
                gioiThieu: '',
            }
        }
    }
}

exports.MODEL = new Model();