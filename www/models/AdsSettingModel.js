"use strict";

const BaseModel = require('../config/db/intalizeModel/BaseModel');
class Model extends BaseModel {

    constructor() {
        super(require('../database/AdsSettingCol'))
    }

    async getSettingAds() {
        let data = await this.getDataWhere({}, this.FIND_ONE());
        if (!data) {
            data = await this.insertData({
                homeCode: '',
                shopCode1: '',
                shopCode2: '',
                banner1: '',
                banner2: '',
                banner3: '',
                banner4: '',
            })
        }
        return data
    }

    async updateSettingAds(data) {
        await this.updateWhereClause({}, data);
        return data
    }
}

exports.MODEL = new Model();