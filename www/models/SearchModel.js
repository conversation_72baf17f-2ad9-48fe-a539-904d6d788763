"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
class Model extends BaseModel {

    constructor() {
        super(require('../database/SearchCol'))
    }
    getByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), {createAt: -1})
            return resolve(data)
        })
    }
    search(condition, page, limit = 10, sort = null) {
        let countType = {};
        let type = condition.businessTypes ? condition.businessTypes['$regex'] : null;
        if (type == 1) {
            countType = {
                countClinic: {$gt: 0}
            }
        }
        if (type == 2) {
            countType = {
                countRoom: {$gt: 0}
            }
        }
        if (type == 3) {
            countType = {
                countService: {$gt: 0}
            }
        }

        const pipeline = [
            // {
            //     $project: {
            //         locationSearch: "$branches.location",
            //     }
            // },
            // {
            //     $addFields: {
            //         locationSearch: "$branches.location",
            //     }
            // },
            {
                $match: condition
            },
            // {
            //     $match: countType
            // }

        ];

        if (sort){
            pipeline.push({ $sort: sort});
        }
        console.log({pipeline})

        const that = this;
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline, {
                $group: {
                    _id: null,
                    count: {
                        $sum: 1
                    }
                }
            }]);
            if (!countData.length) {
                resolve({
                    result: [],
                    total: 0,
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline, {
                $skip: (page - 1) * limit
            }, {
                $limit: limit
            }]);
            const totalPage = Math.ceil(countData[0].count / limit);
            resolve({
                result,
                total: countData[0].count,
                totalPage
            });
        })
    }
}

exports.MODEL = new Model();
