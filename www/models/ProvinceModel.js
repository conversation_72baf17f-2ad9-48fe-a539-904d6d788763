"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');

class Model extends BaseModel {

    constructor() {
        super(require('../database/ProvinceCol'))
    }
    add(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }
    getByCondition(condition = {}, sort ={createAt: -1}) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY(), sort)
            return resolve(data)
        })
    }
    getOneByCondition(condition, typeData = null) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_ONE(), typeData)
            return resolve(data)
        })
    }
}

exports.MODEL = new Model();
