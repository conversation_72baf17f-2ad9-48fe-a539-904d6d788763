"use strict";

const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const StringUtils = require('../utils/StringUtils');
const ObjectId = require('mongoose').Types.ObjectId;
const util = require('util');

class Model extends BaseModel {

    constructor() {
        super(require('../database/ProducCol'))
    }

    completeData(p) {
        function handleDataObject(d) {
            d.typeProductText = d.typeProduct == 0 ? 'Hết hàng' : 'Còn hàng';
            return d;
        }

        if (util.isArray(p)) {
            return p.map((e) => {
                return handleDataObject(e);
            });
        } else {
            return handleDataObject(p);
        }
    }

    findNextName() {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere({}, that.FIND_ONE(), {code: -1});
            let code = 1
            if (data) {
                code = data.code + 1
            }
            return resolve(code);
        })
    }

    addProducts(obj) {
        const that = this;
        return new promise(async resolve => {
            obj.code = await that.findNextName()
            let data = await that.insertData(obj)
            return resolve(data)
        })
    }

    async getProductsById(id) {
        let data = await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: {_id: ObjectId(id)}
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    weight: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    branchId: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    address: {$arrayElemAt: ['$storeInfo.address', 0]}
                }
            }
        ]);
        return (data.length > 0 ? this.completeData(data[0]) : null)
    }

    updateProducts(id, data) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(id, data)
            return resolve()
        })
    }

    async getCountProductByCondition(condition) {
        return await this.countDataWhere(condition)
    }

    async getProductByConditionForStore(condition, typeData = 0, limit = 0) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: condition
            },
            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    createAt: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$service.status', 0]}
                }
            },
            {
                $match: {storeStatus: {$ne: 2}}
            }
        ]));
    }

    async getProductMoiNhat() {
        return this.completeData(await this.coll.aggregate([
            {
                $match: {status: 1}
            },
            {$sort: {createAt: -1}},
            {$limit: 30},
            {
                $addFields: {storeIdObject: {$toObjectId: '$storeId'}}
            },
            {
                $lookup: {
                    localField: 'storeIdObject',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    classify: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$service.status', 0]}
                }
            },
            {
                $match: {storeStatus: {$ne: 2}}
            }
        ]));
    }

    async getProductByConditionSell(condition, typeData = null, limit = null) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: condition
            },

            {$sort: typeData},
            {
                $project: {
                    userId: 1,
                    storeId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]},
                }
            }, {
                $match: {storeStatus: 1}
            }, {
                $limit: limit
            },
        ]));
    }

    async getProductForCategory(categoryId, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $match: {categoryId, status: 1}
            },
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]}
                }
            }
        ]));
    }

    async getProductPageByCondition(condition, page, itemsPerPage) {
        return this.completeData(await this.coll.aggregate([
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    codeText: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    createAt: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {
                $skip: (page - 1) * itemsPerPage
            },
            {
                $limit: itemsPerPage
            },
            {$sort: {createAt: -1, watched: -1}},
        ]));
    }

    async getCountProductPageByCondition(condition) {
        // return 100;
        return (await this.coll.aggregate([
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            }, {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    codeText: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {$sort: {watched: -1, createAt: -1}},
        ])).length
    }

    async searchProduct(search, categoryId, page, limit) {
        let matchData = {
            $or: [
                {'nameUTF': {'$regex': StringUtils.removeUtf8(search)}},
                {'addressUTF': {'$regex': StringUtils.removeUtf8(search)}},
                {'descriptionUTF': {'$regex': StringUtils.removeUtf8(search)}},
            ],
            status: 1
        };
        let skip = (page - 1) * limit;

        if (categoryId) matchData.categoryId = categoryId;

        const countData = await that.coll.aggregate([matchData]).length;

        return this.completeData(await this.coll.aggregate([
            {
                $match: matchData
            },
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                },
            },
            // {
            //     $unwind:'$storeInfo'
            // },
            // {
            //     $match:{
            //         '$storeInfo.nameUTF':{$or:search}
            //     }
            // },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    storeId: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]},
                }
            }, {
                $match: {storeStatus: 1}
            }, {
                $skip: skip
            }, {
                $limit: limit
            }
        ]));
    }

    getProductByConditionWithPageLookup(condition, page, limit = 10, conditionOr = null, sort = null) {

        const pipeline = [
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo',
                },
            },
            {
                $match: condition
            },
            {$match: {"storeInfo.status": 1}},
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    storeId: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    storeStatus: {$arrayElemAt: ['$storeInfo.status', 0]},
                }
            },

        ];
        if (sort) {
            pipeline.push({
                $sort: sort
            })
        }

        const that = this;
        return new promise(async resolve => {
            // TODO: Cần thêm điều kiện trạng thái của store
            let mathStoreStatus = {
                storeStatus: 1
            }
            const countData = await that.coll.aggregate([...pipeline,
                // {$match: mathStoreStatus}
            ]);

            if (!countData.length) {
                resolve({
                    result: [],
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline, {
                $skip: (page - 1) * limit
            }, {
                $limit: limit
            },
                // {$match: mathStoreStatus}
            ]);
            const totalPage = Math.ceil(countData.length / limit);
            resolve({
                result,
                totalPage
            });
        })
    }

    async getProductForStore(storeId) {
        return this.completeData(await this.coll.aggregate([
            {
                $match: {storeId, status: 1}
            },
            {$sort: {watched: -1, createAt: -1}},
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    storeId: 1,
                    code: 1,
                    name: 1, // Tên hàng hóa
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    isFeatured: 1,
                }
            }
        ]));
    }

    addCountRevenueProduct(productId, count) {
        const that = this;
        return new promise(async resolve => {
            await that.updateById(productId, {
                $inc: {revenue: count}
            });
            return resolve()
        })
    }

    removeProductByCondition(condition) {
        const that = this;
        return new promise(async resolve => {
            await that.removeDataWhere(condition);
            return resolve()
        })
    }

    getProductByConditionNoData(condition) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(condition, that.FIND_MANY());
            return resolve(data)
        })
    }

    async getProductPageByCatIds(catIds, page, itemsPerPage) {
        const condition = {
            status: 1,
            categoryId: {$in: catIds}
        }
        const pipeline = [
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    codeText: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    createAt: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {
                $sort: {
                    createAt: -1
                }
            },
        ]
        const that = this;
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline]);

            if (!countData.length) {
                resolve({
                    result: [],
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline, {
                $skip: (page - 1) * itemsPerPage
            }, {
                $limit: itemsPerPage
            }
            ]);
            const totalPage = Math.ceil(countData.length / itemsPerPage);
            resolve({
                result,
                totalPage
            });
        })
    }

    async getProductPageByIds(productIds, page, itemsPerPage) {
        productIds = productIds.map(id => {
            return ObjectId(id)
        })
        const condition = {
            status: 1,
            _id: {$in: productIds}
        }
        const pipeline = [
            {
                $addFields: {codeText: {$toString: '$code'}}
            }, {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $addFields: {categoryObjectId: {$toObjectId: '$categoryId'}}
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    codeText: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    createAt: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            },
            {
                $sort: {
                    createAt: -1
                }
            },
        ]
        const that = this;
        return new promise(async resolve => {
            const countData = await that.coll.aggregate([...pipeline]);

            if (!countData.length) {
                resolve({
                    result: [],
                    totalPage: 0
                });
                return;
            }
            const result = await that.coll.aggregate([...pipeline, {
                $skip: (page - 1) * itemsPerPage
            }, {
                $limit: itemsPerPage
            }
            ]);
            const totalPage = Math.ceil(countData.length / itemsPerPage);
            resolve({
                result,
                totalPage
            });
        })
    }

    async adminGetProducts(condition, page, itemsPerPage) {
        let countProduct = 0;
        let pipeLine = [
            {
                $addFields: {codeText: {$toString: '$code'}}
            },
            {
                $addFields: {storeObjectId:{$convert: {input: '$storeId', to : 'objectId', onError: '',onNull: ''}}}
            },
            {
                $lookup: {
                    localField: 'storeObjectId',
                    from: "services",
                    foreignField: '_id',
                    as: 'storeInfo'
                }
            },
            {
                $addFields: {
                    categoryObjectId: {
                        $convert: {
                            input: '$categoryId',
                            to: 'objectId',
                            onError: '',
                            onNull: ''
                        }
                    },
                }
            },
            {
                $lookup: {
                    localField: 'categoryObjectId',
                    from: "categories",
                    foreignField: '_id',
                    as: 'categoryInfo'
                }
            },
            // {
            //     $lookup: {
            //         from: "categories",
            //         let: {categoryId:{$toObjectId: '$categoryId'}},
            //         pipeline: [
            //             {
            //                 $match: {
            //                     $expr: {
            //                         $eq: ['$_id', '$$categoryId']
            //                     }
            //                 }
            //             },
            //             // {$match: {orderId: {$exists: true}}},
            //         ],
            //         as: 'categoryInfo',
            //     }
            // },
            {
                $project: {
                    userId: 1,
                    categoryId: 1,
                    code: 1,
                    codeText: 1,
                    name: 1, // Tên hàng hóa
                    storeId: 1,
                    thumbail: 1, // Ảnh bìa
                    pictures: 1, // Ảnh con
                    trademark: 1, // Thương hiệu
                    description: 1,  // Mo ta
                    transport: 1,
                    price: 1,
                    priceOld: 1,
                    size: 1,
                    classify: 1,
                    status: 1, // 0: Còn hàng || 1 : Hết hàng
                    watched: 1, // Số lượt ghé xem
                    revenue: 1, // Doanh thu
                    typeProduct: 1,
                    typeShip: 1,
                    createAt: 1,
                    isFeatured: 1,
                    storeName: {$arrayElemAt: ['$storeInfo.name', 0]},
                    categoryName: {$arrayElemAt: ['$categoryInfo.name', 0]}
                }
            },
            {
                $match: condition
            }
        ];

        const that = this;
        return new promise(async resolve => {
            let products = that.completeData(await that.coll.aggregate([...pipeLine,
                {
                    $skip: (page - 1) * itemsPerPage
                },
                {
                    $limit: itemsPerPage
                },
                {
                    $sort: {
                        createAt: -1, watched: -1
                    }
                }
            ]));
            let countProductData = await that.coll.aggregate([...pipeLine]);
            if (countProductData) {
                countProduct = countProductData.length;
            }
            resolve({
                products,
                countProduct
            });
        })
    }
}

exports.MODEL = new Model();
