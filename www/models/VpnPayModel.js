"use strict";


const promise = require('bluebird');
const BaseModel = require('../config/db/intalizeModel/BaseModel');
const RequestBuyProductModel = require('./RequestBuyProductModel');
const BookSpaModel = require('./BookSpaModel');
const BookRoomModel = require('./BookRoomModel');
const BookExaminationModel = require('./BookExaminationModel');
const {VnPayStatus} = require("./enum/VnPayEnum");

class Model extends BaseModel {

    constructor() {
        super(require('../database/VpnPayCol'))
    }

    addVpnPay(obj) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.insertData(obj);
            return resolve(data)
        })
    }

    getVpnPayWhere(where) {
        const that = this;
        return new promise(async resolve => {
            let data = await that.getDataWhere(where, that.FIND_ONE(), {createAt: -1});
            return resolve(data)
        })
    }

    updateVpnPayByCondition(condition, data) {
        const that = this;
        return new promise(async resolve => {
            if (data.status && condition.orderId) {
                const orderId = condition.orderId
                if (data.status <= 2) {

                    const statusUpdate = data.status === 2 ? VnPayStatus.PAID : data.status

                    // Cập nhật đơn hàng
                    RequestBuyProductModel.MODEL.updateWhereClause({
                        paymentId: orderId
                    }, {isPayOnline: statusUpdate});

                    // Cập nhật đơn hàng spa
                    BookSpaModel.MODEL.updateWhereClause({
                        orderId
                    }, {isPayOnline: statusUpdate});

                    // Cập nhật đơn BookExaminationModel
                    BookExaminationModel.MODEL.updateWhereClause({
                        orderId
                    }, {isPayOnline: statusUpdate});

                    // Cập nhật đơn hàng BookRoomModel
                    BookRoomModel.MODEL.updateWhereClause({
                        orderId
                    }, {isPayOnline: statusUpdate});

                }
            }
            await that.updateWhereClause(condition, data);
            return resolve()
        })
    }
}

exports.MODEL = new Model();
