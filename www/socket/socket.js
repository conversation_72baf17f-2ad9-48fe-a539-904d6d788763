"use strict";
const UserSession = require('../session/UserSession');
const CfJws = require('../config/CfJws');
const MessageModel = require('../models/MessageModel');
const {sendHasMessageNotification} = require('../utils/Notification');
const UserModel = require('../models/UserModel');
const UserSettingModel = require('../models/UserSettingModel');
module.exports = function (io) {
    io.on('connection', function (socket) {
        let user = null;
        socket.on('disconnect', async () => {
            if (user != null) {
                await UserModel.MODEL.updateUser(user._id, {$inc: {countOnlineSession: -1}});
                pushChangeUserStatus(user._id);
            }
        });

        if (socket.handshake.session && UserSession.getUser(socket.handshake.session)) {
            user = UserSession.getUser(socket.handshake.session);
            startListeningSocket();
        } else if (socket.handshake.query['token'] && socket.handshake.query['token'] != '') {
            CfJws.extraToken(socket.handshake.query['token']).then(function (rslCheckToken) {
                if (rslCheckToken.error) {
                } else {
                    user = rslCheckToken.data;
                    startListeningSocket();
                }
            })
        }

        async function startListeningSocket() {
            await UserModel.MODEL.updateUser(user._id, {$inc: {countOnlineSession: 1}});
            pushChangeUserStatus(user._id);
            socket.on('realtime-chat', async (data) => {
                let userCheckStatus = await UserModel.MODEL.getUsersById(user._id);
                if (!userCheckStatus || userCheckStatus.status == 2) return;
                if (data.type == 'new-message') {
                    let userId = data.to;
                    // Kiểm tra trạng thái mở tin nhắn
                    let setting = await UserSettingModel.MODEL.getSetting(userId);
                    if (setting.nhanTinNhanTuNguoiKhac == 0) return;


                    let chat = await MessageModel.MODEL.addMessage({
                        userId,
                        userSendId: user._id,
                        content: data.content,
                        type: 0
                    });

                    setTimeout(async () => {
                        await UserModel.MODEL.updateUser(userId, {$inc: {countMessage: 1}});
                        let newInfo = await UserModel.MODEL.getUsersById(userId);
                        if (newInfo.type == 2) {
                            sendHasMessageNotification(userId, user._id);
                        }
                        io.emit(userId, {
                            type: 'change-count-message',
                            countMessage: newInfo.countMessage
                        });
                    }, 500);

                    chat.fullName = user.fullName;
                    chat.userPicture = user.picture;
                    data.chat = chat;
                    io.emit(data.to, data);
                    io.emit(user._id, data);
                } else {
                    io.emit(data.to, data)
                }
            });
        }

    });

    async function pushChangeUserStatus(userId) {
        let user = await UserModel.MODEL.getUsersById(userId);
        if (user && user.countOnlineSession == 0 || user.countOnlineSession == 1) {
            io.emit('change-online-status', {userId, countOnlineSession: user.countOnlineSession})
        }
    }
};