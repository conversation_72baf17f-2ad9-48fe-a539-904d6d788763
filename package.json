{"name": "BaseNodeproject", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"dev": "nodemon app.js", "start": "pm2 start ecosystem.config.js prod", "nodemon-dev": "brew services start redis && NODE_ENV=development nodemon", "start-dev": "sudo pm2 delete -s mypetapp || : && pm2 start ecosystem.config.js", "test": "mocha --timeout 10000", "start--prod": "export BUILD_ID=dontKillMe  && forever start app.js", "stop": "forever stopall", "debug": "NODE_ENV=dev node --nolazy --inspect-brk=9229 app.js", "fixenfile": "echo fs.inotify.max_user_watches=582222 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p", "fixdstore-mac": "find . -name '.DS_Store' -type f -delete"}, "repository": {"type": "git", "url": ""}, "keywords": ["BaseNodeproject"], "author": "nghianv", "license": "ISC", "homepage": "", "dependencies": {"@google-cloud/logging-winston": "^4.0.4", "@sendgrid/mail": "^7.2.1", "authenticator": "^1.1.2", "axios": "^0.20.0", "base64-img": "^1.0.4", "blob-to-base64": "^1.0.2", "bluebird": "^3.5.4", "body-parser": "^1.19.0", "bson-objectid": "^1.3.1", "canvas": "latest", "cb-http-client": "^0.2.3", "cluster": "^0.7.7", "connect-multiparty": "^2.0.0", "connect-redis": "^3.2.0", "cookie-parser": "^1.4.3", "cors": "^2.8.5", "cron": "^1.8.2", "crypto": "0.0.3", "cryptocompare": "^0.2.0", "dropbox": "^6.0.1", "dropbox-v2-api": "^2.4.28", "ejs": "^2.5.7", "excel4node": "^1.7.2", "express": "^4.15.2", "express-minify": "^1.0.0", "express-session": "^1.15.4", "farmhash": "^3.2.1", "file-exists": "^2.0.0", "firebase-admin": "^9.2.0", "form-data": "^4.0.0", "fs": "0.0.1-security", "geolib": "^3.3.1", "html-to-text": "^9.0.5", "htmlspecialchars": "^1.0.5", "http-proxy": "^1.16.2", "http-proxy-middleware": "^1.0.6", "httpify": "^3.0.0", "image-size": "^0.8.3", "image-to-base64": "^2.1.1", "isomorphic-fetch": "^3.0.0", "jsdom": "^13.2.0", "jsonwebtoken": "^8.2.1", "ksort": "0.0.6", "lodash": "^4.17.20", "md5": "^2.2.1", "mkdirp": "^0.5.1", "mocha": "^8.2.0", "moment": "^2.15.1", "moment-timezone": "^0.5.5", "mongoose": "^4.11.5", "mongoose-simple-random": "^0.4.1", "multer": "^1.4.1", "multer-cloud-storage": "^2.7.1", "node-cron": "^2.0.3", "node-fetch": "^1.7.3", "node-geocoder": "^3.27.0", "nodemailer": "^6.3.0", "nodemailer-sendgrid-transport": "^0.2.0", "nodemailer-ses-transport": "^1.5.1", "numeral": "^2.0.6", "passport": "^0.3.2", "passport-facebook": "^2.1.1", "passport-google-oauth20": "^2.0.0", "passport-local": "^1.0.0", "query-string": "^6.2.0", "read-excel-file": "^4.0.6", "redis": "^2.6.5", "request": "^2.75.0", "request-ip": "^3.3.0", "request-with-cookies": "0.0.5", "sha256": "^0.2.0", "socket.io": "^2.0.4", "socket.io-redis": "^5.2.0", "string_decoder": "^0.10.31", "twilio": "^3.49.1", "uniq": "^1.0.1", "util": "^0.10.3", "uuid": "^7.0.2", "validate.js": "^0.13.1", "winston": "^3.3.3", "ws": "^4.1.0", "xlsx": "^0.14.5", "xlsx-style": "^0.8.13", "xml2js": "^0.4.19", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "chai": "^4.2.0", "chai-http": "^4.3.0", "husky": "^4.3.0", "nodemon": "^2.0.19"}}