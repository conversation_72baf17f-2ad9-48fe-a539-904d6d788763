version: '3.8'

services:
  # Express Application
  express:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: lbvd-express
    restart: unless-stopped
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/lbvd_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis_password}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret-here}
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret-here}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - GOOGLE_CLOUD_PROJECT_ID=${GOOGLE_CLOUD_PROJECT_ID}
    volumes:
      - express_uploads:/app/uploads
      - express_files:/app/files
      - express_logs:/app/logs
      - ./template:/app/template:ro
      - ./www:/app/www:ro
    depends_on:
      mongo:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - express-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB Database
  mongo:
    image: mongo:6.0
    container_name: express-mongo
    restart: unless-stopped
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD:-admin_password}
      - MONGO_INITDB_DATABASE=lbvd_db
    volumes:
      - mongo_data:/data/db
      - mongo_config:/data/configdb
      - ./database/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "27017:27017"
    networks:
      - express-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: express-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - express-network
    healthcheck:
      test: ["CMD", "redis-cli", "auth", "${REDIS_PASSWORD:-redis_password}", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Nginx (Optional - for SSL and static files)
  nginx:
    image: nginx:alpine
    container_name: express-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - express_uploads:/var/www/uploads:ro
      - express_files:/var/www/files:ro
    depends_on:
      - express
    networks:
      - express-network
    profiles:
      - production

  # MongoDB Admin (Optional)
  mongo-express:
    image: mongo-express:latest
    container_name: express-mongo-admin
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=${MONGO_ROOT_USERNAME:-admin}
      - ME_CONFIG_MONGODB_ADMINPASSWORD=${MONGO_ROOT_PASSWORD:-admin_password}
      - ME_CONFIG_MONGODB_URL=******************************************/
      - ME_CONFIG_BASICAUTH_USERNAME=${MONGO_EXPRESS_USER:-admin}
      - ME_CONFIG_BASICAUTH_PASSWORD=${MONGO_EXPRESS_PASSWORD:-admin}
    depends_on:
      - mongo
    networks:
      - express-network
    profiles:
      - development

volumes:
  mongo_data:
    driver: local
  mongo_config:
    driver: local
  redis_data:
    driver: local
  express_uploads:
    driver: local
  express_files:
    driver: local
  express_logs:
    driver: local

networks:
  express-network:
    driver: bridge
