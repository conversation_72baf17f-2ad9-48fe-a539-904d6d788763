pipeline {
    agent any

    environment {
        NODE_ENV = 'production'
    }

    stages {
        stage('Copy Source') {
            steps {
                // Copy source code đến thư mục đích
                sh 'sudo rsync -av --exclude="node_modules" ./ /var/www/lbvd-backend-node/'
            }
        }

        stage('Install Dependencies in New Folder') {
            steps {
                // Điều hướng vào thư mục đích để cài đặt các phụ thuộc mới
                dir('/var/www/lbvd-backend-node') {
                    sh 'sudo yarn install --ignore-engines --max-concurrency=2'
                }
            }
        }

        stage('Restart PM2') {
            steps {
                dir('/var/www/lbvd-backend-node') {
                    // Khởi động lại PM2 với ecosystem.config.js
                    sh 'sudo pm2 reload ecosystem.config.js --env production'
                }
            }
        }
    }

    post {
        success {
            echo 'Deployment completed successfully! PM2 restarted.'
        }
        failure {
            echo 'Deployment failed!'
        }
    }
}
