'use strict'
let express = require('express'),
    cluster = require('cluster'),
    net = require('net'),
    sio = require('socket.io'),
    sio_redis = require('socket.io-redis'),
    https = require('https'),
    // farmhash = require('farmhash'),
    fs = require('fs')
let cors = require('cors')
const httpsConfig = require('./www/config/CfHttps')
let num_processes = require('os').cpus().length
let CfApp = require('./www/config/CfApp')
let stringUtils = require('./www/utils/StringUtils')
const {createProxyMiddleware} = require('http-proxy-middleware');
const CfNewsApi = require('./www/config/CfNewsApi')
const requestToCurl = require('./www/utils/requestToCurl')
const bodyParser = require('body-parser')
const socket = require("./www/socket/socket");
const config = require("./www/config/config");
const routing = require("./www/config/router/Routing");
const localsRouting = require("./www/locals/LocalsRouting");


exports.BASE_DIR = __dirname
exports.EXPRESS = express

console.log(`Running in ${process.env.NODE_ENV} mode.`);
console.log(`Config CfApp`, CfApp);
console.log(`Connecting to database ${CfApp.db.name} at ${CfApp.db.host}:${CfApp.db.port}`);

async function main() {
    let server
    let app = new express()
    app.use(cors())
    // app.use(bodyParser.json());

    // app.use(requestToCurl); // Logger

    app.use('/api', createProxyMiddleware(CfApp.apiUrlV2)); // api
    app.use('/uploads', createProxyMiddleware({ target: CfApp.apiUrlV2, changeOrigin: true })); // proxy image

    /**
     * Configure proxy middleware
     */
    // const wsProxy = createProxyMiddleware('/websocket', {
    //     target: 'ws://*************:3000/websocket',
    //     pathRewrite: {
    //         '^/websocket': '/websocket',        // rewrite path.
    //         // '^/removepath' : ''               // remove path.
    //     },
    //     changeOrigin: true, // for vhosted sites, changes host header to match to target's host
    //     ws: true, // enable websocket proxy
    //     logLevel: 'debug',
    // });

    //proxy rocketchat
    // app.use(wsProxy); // add the proxy to express

    server = app.listen(CfApp.port, function() {
        console.log("Express server listening on port http://" + CfApp.host + ':' + CfApp.port);
    });
    // server.on('upgrade', wsProxy.upgrade); // <-- subscribe to http 'upgrade'
    let io = sio(server, {
        pingInterval: 1000,
        pingTimeout: 1000,
        allowEIO3: true
    })

    io.adapter(sio_redis({host: 'localhost', port: 6379}))
    let socket = require('./www/socket/socket')
    socket(io)

    process.on('message', function (message, connection) {
        if (message !== 'sticky-session:connection') {
            return
        }

        server.emit('connection', connection)
        connection.resume()
    })

    process.setMaxListeners(0)

    config(app, io)

    let routing = require('./www/config/router/Routing')
    routing.mainHandel(app, io)

    let localsRouting = require('./www/locals/LocalsRouting')
    localsRouting(app)

    require('./www/system')(io)
    require('./www/models/ProducModel').MODEL.removeDataWhere({
        storeId: 'null',
    })

    module.exports = server
}

main();
