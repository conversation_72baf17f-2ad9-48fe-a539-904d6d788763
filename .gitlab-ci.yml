image: node

deploy:
  stage: deploy
  before_script:
  # - apt-get update -qq
  # - apt-get install -qq git
  - apt-get update -y && apt-get install openssh-client rsync -y
  - 'which ssh-agent || ( apt-get install -qq openssh-client )'
  - eval $(ssh-agent -s)
  - ssh-add <(echo "$SSH_PRIVATE_KEY")
  - mkdir -p ~/.ssh
  - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  # - 'which ssh-agent || ( apt-get install -qq openssh-client )'
  # - mkdir -p ~/.ssh
  # - chmod 700 ~/.ssh
  # - echo "***********" >> ~/.ssh/known_hosts
  # - chmod 644 ~/.ssh/known_hosts
  # - eval $(ssh-agent -s)
  # - echo "Host *ntStrictHostKeyChecking no" >> ~/.ssh/config
  script:
      # - ssh root@*********** && "cd ~/source/carcitybackendv2 && git checkout main && git pull origin main && exit"
      # - npm install --legacy-peer-deps npm trong ssh
      - rsync --update -raz --progress . root@***********:/root/source/carcity
  environment: production
  only:
    - prod