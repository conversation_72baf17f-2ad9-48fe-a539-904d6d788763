# Docker Setup cho LBVD Express Backend

Hướng dẫn triển khai Express backend sử dụng Docker và Docker Compose.

## Cấu trúc Services

### 1. Express Application
- **Port**: 4000
- **Database**: MongoDB 6.0
- **Cache**: Redis 7
- **Features**: Socket.IO, File Upload, Authentication

### 2. MongoDB Database
- **Port**: 27017
- **Admin UI**: Mongo Express (port 8081) - chỉ trong development
- **Volumes**: Persistent data storage

### 3. Redis Cache
- **Port**: 6379
- **Features**: Session storage, caching
- **Security**: Password protected

### 4. Nginx (Optional)
- **Ports**: 80, 443
- **Features**: SSL termination, static file serving

## Cài đặt và Chạy

### 1. <PERSON><PERSON><PERSON> bị Environment Variables

Tạo file `.env` trong thư mục gốc:

```bash
# Strapi Environment
STRAPI_JWT_SECRET=your-strapi-jwt-secret-here
STRAPI_ADMIN_JWT_SECRET=your-strapi-admin-jwt-secret-here
STRAPI_APP_KEYS=your-strapi-app-keys-here
STRAPI_API_TOKEN_SALT=your-strapi-api-token-salt-here

# Express Environment
EXPRESS_JWT_SECRET=your-express-jwt-secret-here
EXPRESS_SESSION_SECRET=your-express-session-secret-here

# Database Passwords
MYSQL_ROOT_PASSWORD=your-mysql-root-password
MYSQL_PASSWORD=your-mysql-password
MONGO_ROOT_PASSWORD=your-mongo-root-password
```

### 2. Build và Chạy Services

```bash
# Chuyển đến thư mục chứa docker-compose.yml
cd /Volumes/T9/Projects

# Build và chạy tất cả services
docker-compose up -d

# Hoặc build lại từ đầu
docker-compose up -d --build

# Xem logs
docker-compose logs -f

# Xem logs của service cụ thể
docker-compose logs -f strapi-backend
docker-compose logs -f express-backend
```

### 3. Kiểm tra Services

```bash
# Kiểm tra trạng thái containers
docker-compose ps

# Kiểm tra health
curl http://localhost/health

# Truy cập Strapi Admin
open http://localhost/admin

# Test Express API
curl http://localhost/user/api/test
```

## Quản lý Services

### Dừng Services
```bash
docker-compose down
```

### Dừng và xóa volumes
```bash
docker-compose down -v
```

### Restart service cụ thể
```bash
docker-compose restart strapi-backend
docker-compose restart express-backend
```

### Xem logs realtime
```bash
docker-compose logs -f --tail=100
```

### Truy cập container
```bash
# Truy cập Strapi container
docker-compose exec strapi-backend sh

# Truy cập Express container
docker-compose exec express-backend sh

# Truy cập database
docker-compose exec mysql mysql -u strapi_user -p strapi_db
docker-compose exec mongo mongosh
```

## Backup và Restore

### Backup Database
```bash
# Backup MySQL (Strapi)
docker-compose exec mysql mysqldump -u strapi_user -p strapi_db > backup_strapi.sql

# Backup MongoDB (Express)
docker-compose exec mongo mongodump --db lbvd_db --out /backup
```

### Restore Database
```bash
# Restore MySQL
docker-compose exec -T mysql mysql -u strapi_user -p strapi_db < backup_strapi.sql

# Restore MongoDB
docker-compose exec mongo mongorestore --db lbvd_db /backup/lbvd_db
```

## Monitoring

### Xem resource usage
```bash
docker stats
```

### Xem disk usage
```bash
docker system df
```

### Clean up
```bash
# Xóa unused images
docker image prune

# Xóa unused volumes
docker volume prune

# Xóa tất cả unused resources
docker system prune -a
```

## Troubleshooting

### 1. Port conflicts
Nếu port đã được sử dụng, thay đổi port mapping trong docker-compose.yml:
```yaml
ports:
  - "1338:1337"  # Thay vì 1337:1337
```

### 2. Permission issues
```bash
# Fix permission cho uploads
sudo chown -R 1000:1000 ./uploads
```

### 3. Database connection issues
```bash
# Kiểm tra network
docker network ls
docker network inspect projects_lbvd-network
```

### 4. Memory issues
Tăng memory limit cho Docker Desktop hoặc thêm vào docker-compose.yml:
```yaml
deploy:
  resources:
    limits:
      memory: 1G
```

## Production Deployment

### 1. SSL Configuration
Thêm SSL certificates vào `nginx/ssl/` và cập nhật nginx.conf

### 2. Environment Variables
Sử dụng Docker secrets hoặc external config management

### 3. Scaling
```bash
# Scale Express backend
docker-compose up -d --scale express-backend=3
```

### 4. Health Checks
Thêm health checks vào docker-compose.yml:
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:1337/admin"]
  interval: 30s
  timeout: 10s
  retries: 3
```
