<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="dev" type="NodeJSConfigurationType" path-to-node="/opt/homebrew/opt/node@18/bin/node" path-to-js-file="app.js" working-dir="$PROJECT_DIR$">
    <envs pass-parent-envs="false">
      <env name="NODE_ENV" value="development" />
      <env name="NODE_ENV.TZ" value="Asia/Ho_Chi_Minh" />
    </envs>
    <method v="2" />
  </configuration>
</component>