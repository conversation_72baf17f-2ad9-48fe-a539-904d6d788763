process.env.NODE_ENV = 'dev';

const user = require('./config');
const axios = require('axios');
let expect = require('chai').expect;
let assert = require('chai').assert;
let domain = require('../www/config/CfApp').domain
const {BookingStatus} = require('../www/models/enum/BookingStatus');
const BookSpaModel = require('../www/models/BookSpaModel');
const BookRoomModel = require('../www/models/BookRoomModel');
const BookExaminationModel = require('../www/models/BookExaminationModel');

domain = domain + '/user/api/';

const epBooking = 'booking-v2/'
const epHuyDon = 'huy-dich-vu.html/' //:id/:type

const bookSpaId = '5f85532b8c5c3a0a1ab829ad'
const bookRoomId = '5f86d78036ea96489217e456'
const bookClinicId = '5f8fb2d059f19e114bbb5e9b'

describe('API Cancel Booking Spa',() => {
    beforeEach(async () => {
        // Update Status Booking
        await BookSpaModel.MODEL.updateBookSpa(bookSpaId,{status: BookingStatus.WAITING_CONFIRM})
    });
    it('Cancel Booking Spa',async () => {
        const apiUrl = domain + epHuyDon + bookSpaId + '/3?output=json';
        console.log('API URL: ',apiUrl)
        let result = await axios.post(apiUrl,{message: 'Unit Test Cancel Booking Spa'},{headers: {'access-token': user.token}})

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error);
        // Re-check;
        let booking = await BookSpaModel.MODEL.getBookSpaById(bookSpaId) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking.status).to.equal(BookingStatus.CANCELED, 'ERR: Booking Spa Status');
    });

});

describe('API Cancel Booking Room',() => {
    beforeEach(async () => {
        // Update Status Booking
        await BookRoomModel.MODEL.updateBookRoom(bookRoomId,{status: BookingStatus.WAITING_CONFIRM})
    });
    it('Cancel Booking Room',async () => {
        const apiUrl = domain + epHuyDon + bookRoomId + '/2?output=json';
        console.log('API URL: ',apiUrl)
        let result = await axios.post(apiUrl,{message: 'Unit Test Cancel Booking Room'},{headers: {'access-token': user.token}})

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error);
        // Re-check;
        let booking = await BookRoomModel.MODEL.getBookRoomById(bookRoomId) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking.status).to.equal(BookingStatus.CANCELED, 'ERR: Booking Room Status');
    });

});

describe('API Cancel Booking Clinic',() => {
    beforeEach(async () => {
        // Update Status Booking
        await BookExaminationModel.MODEL.updateBookExamination(bookClinicId,{status: BookingStatus.WAITING_CONFIRM})
    });
    it('Cancel Booking Clinic',async () => {
        const apiUrl = domain + epHuyDon + bookClinicId + '/1?output=json';
        console.log('API URL: ',apiUrl)
        let result = await axios.post(apiUrl,{message: 'Unit Test Cancel Booking Clinic'},{headers: {'access-token': user.token}})

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error);
        // Re-check;
        let booking = await BookExaminationModel.MODEL.getBookExaminationById(bookClinicId) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking.status).to.equal(BookingStatus.CANCELED, 'ERR: Booking Clinic Status');
    });

});

describe('API Add Booking Spa',() => {

    beforeEach((done) => {
        done()
    });
    it('Add Booking Spa',async () => {

        const apiUrl = domain + epBooking + '?output=json';
        console.log('API URL: ',apiUrl)
        const orderId = new Date().getTime();
        // Test data items
        let items = [
            {
                "image" : "/files/images/118770340_2750853531826348_4754084026308104086_o.jpg",
                "timeCheckOut" : 0,
                // "timeCheckIn" : 1603791000000.0, // TIME FAILED
                "timeCheckIn" : new Date().getTime() + 222, // TIME PASS
                "price" : 50000,
                "branchPhone" : "0366832222",
                "typePet" : "cat",
                "serviceName" : "Cạo bàn 1",
                "weight" : "Giá chung",
                "time" : 1603791000000.0,
                "storeId" : "5f69bc7b1ded5e0e6ca132db",
                "_id" : "122"
            },
            {
                "image" : "/files/images/118770340_2750853531826348_4754084026308104086_o.jpg",
                "timeCheckOut" : 0,
                // "timeCheckIn" : 1603791000000.0, // TIME FAILED
                "timeCheckIn" : new Date().getTime() + 222, // TIME PASS
                "price" : 50000,
                "branchPhone" : "0366832222",
                "typePet" : "cat",
                "serviceName" : "Cạo bàn 2",
                "weight" : "Giá chung",
                "time" : 1603791000000.0,
                "storeId" : "5f69bc7b1ded5e0e6ca132db",
                "_id" : "123"
            },
            {
                "image" : "/files/images/118770340_2750853531826348_4754084026308104086_o.jpg",
                "timeCheckOut" : 0,
                // "timeCheckIn" : 1603791000000.0, // TIME FAILED
                "timeCheckIn" : new Date().getTime() + 222, // TIME PASS
                "price" : 50000,
                "branchPhone" : "0366832222",
                "typePet" : "cat",
                "serviceName" : "Cạo bàn 3",
                "weight" : "Giá chung",
                "time" : 1603791000000.0,
                "storeId" : "5f69bc7b1ded5e0e6ca132db",
                "_id" : "124"
            },
        ]
        const testData = {
            userId: user.id,
            storeId: '5f7fcdeae0595609bca3363c',
            phone: user.phone,
            typePet: 1,
            note: 'Test Note', // nếu là đặt lịch khám: tương ứng triệu chứng cũ.
            branchName: 'Test branchName',
            branchAddress: 'Test branchAddress',
            items,
            price: 99000,
            branchPhone: '**********',
            bankCode: 'TM',
            orderId,
            coupon: '',
            valueCoupon: '',
            bookingType: 3,
            pickupAddress: 'Test pickup Address', //điểm đón
        }
        const result = await axios.post(apiUrl,testData,{headers: {'access-token': user.token}})
        // console.log("result book spa", result)

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error);
        // Re-check;
        let booking = await BookSpaModel.MODEL.getOneBookSpaByCondition({orderId: orderId}) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking).to.have.deep.property('_id');
    });

});


describe('API Add Booking Room',() => {
    beforeEach((done) => {
        done()
    });
    it('Add Booking Room',async () => {
        const apiUrl = domain + epBooking + '?output=json';
        console.log('API URL: ',apiUrl)
        const orderId = new Date().getTime();
        const testData = {
            userId: user.id,
            storeId: '5f7fcdeae0595609bca3363c',
            time: new Date().getTime() + 5 * 86400000, // timeCheckIn
            timeCheckOut: new Date().getTime() + 8 * 86400000,
            weight: 'Demo Can',
            service: 'Test Room Name 111111',
            phone: user.phone,
            typePet: 1,
            note: 'Test Note', // nếu là đặt lịch khám: tương ứng triệu chứng cũ.
            branchName: 'Test branchName',
            branchAddress: 'Test branchAddress',
            serviceDetail: {
                "_id": "5f6bfa171cf1450c172e18f2",
                "name": "Combo Tắm",
                "description": "Vệ sinh tai, cắt m&oacute;ng , vắt tuyến h&ocirc;i, tắm",
                "image": "/files/images/f70a6db9f43f0b61522e.jpg",
                "classify": [{
                    "name": "CÂN NẶNG",
                    "_id": "5f6bfa171cf1450c172e18f3",
                    "data": [{
                        "name": "DƯỚI 6KG",
                        "price": "100,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f7"
                    },{
                        "name": "6KG- 10KG",
                        "price": "150,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f6"
                    },{
                        "name": "10KG- 15KG",
                        "price": "200,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f5"
                    },{"name": "15KG- 20KG","price": "250,000 VND","_id": "5f6bfa171cf1450c172e18f4"}]
                }],
                "shortDes": "Vệ sinh tai, cắt móng , vắt tuyến hôi, tắm",
                "price": 100000
            },
            price: 99000,
            branchPhone: '**********',
            bankCode: 'TM',
            orderId,
            coupon: '',
            valueCoupon: '',
            bookingType: 2,
            timeValue: 2, // số ngày hoặc số giờ
            timeType: 'day', // tính tiền theo ngày hoặc giờ: day, hour
            pickupAddress: 'Test pickup Address', //điểm đón
        }
        const result = await axios.post(apiUrl,testData,{headers: {'access-token': user.token}})

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error);
        // Re-check;
        let booking = await BookRoomModel.MODEL.getOneBookRoomByCondition({orderId: orderId}) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking).to.have.deep.property('_id');
    });

});


describe('API Add Booking Clinic',() => {
    beforeEach((done) => {
        done()
    });
    it('Add Booking Clinic',async () => {
        const apiUrl = domain + epBooking + '?output=json';
        console.log('API URL: ',apiUrl)
        const orderId = new Date().getTime();
        const testData = {
            userId: user.id,
            storeId: '5f7fcdeae0595609bca3363c',
            time: new Date().getTime() + 22222, // timeCheckIn
            weight: 'Demo Can',
            service: 'Test dich vu kham',
            phone: user.phone,
            typePet: 1,
            note: 'Test triệu chứng', // nếu là đặt lịch khám: tương ứng triệu chứng cũ.
            branchName: 'Test branchName',
            branchAddress: 'Test branchAddress',
            serviceDetail: {
                "_id": "5f6bfa171cf1450c172e18f2",
                "name": "Combo Tắm",
                "description": "Vệ sinh tai, cắt m&oacute;ng , vắt tuyến h&ocirc;i, tắm",
                "image": "/files/images/f70a6db9f43f0b61522e.jpg",
                "classify": [{
                    "name": "CÂN NẶNG",
                    "_id": "5f6bfa171cf1450c172e18f3",
                    "data": [{
                        "name": "DƯỚI 6KG",
                        "price": "100,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f7"
                    },{
                        "name": "6KG- 10KG",
                        "price": "150,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f6"
                    },{
                        "name": "10KG- 15KG",
                        "price": "200,000 VND",
                        "_id": "5f6bfa171cf1450c172e18f5"
                    },{"name": "15KG- 20KG","price": "250,000 VND","_id": "5f6bfa171cf1450c172e18f4"}]
                }],
                "shortDes": "Vệ sinh tai, cắt móng , vắt tuyến hôi, tắm",
                "price": 100000
            },
            price: 99000,
            branchPhone: '**********',
            bankCode: 'TM',
            orderId,
            coupon: '',
            valueCoupon: '',
            bookingType: 1,
            pickupAddress: 'Test pickup Address', //điểm đón
        }
        const result = await axios.post(apiUrl,testData,{headers: {'access-token': user.token}})

        expect(typeof result).to.equal('object')
        assert.isNotTrue(result.data.error, 'API Return Error');
        // Re-check;
        let booking = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({orderId: orderId}) // Lấy dữ liệu book vừa gọi API
        assert.isNotNull(booking,'Book Data Return null');
        expect(booking).to.have.deep.property('_id');
    });

});
