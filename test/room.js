// process.env.NODE_ENV = 'test';
//
// let mongoose = require("mongoose");
// let BookRoomModel = require('../www/models/BookRoomModel');
//
// let expect  = require('chai').expect;
//
// let domain = require('../www/config/CfApp').domain
// domain = domain + '/user/api/';
// const userToken = '';
// const axios = require('axios');
//
// const epGetRooms = 'get-room-hotel-by-storeid.html'
// const epHuyDon = 'huy-dich-vu.html/:id/:type'
//
// describe('Test API Room Hotel',() => {
//     beforeEach((done) => {
//         // Book.remove({}, (err) => {
//         //     done();
//         // });
//         done()
//     });
//     it('Get Room By StoreId', (done) => {
//         const id = '5f7fcdeae0595609bca3363c'
//         axios.get(domain + epGetRooms + id)
//             .then(({data}) => {
//                 console.log(JSON.stringify(data))
//                 expect(typeof data).to.equal('object');
//                 expect(data.error).to.equal(false)
//                 done()
//             }).catch(err => {
//                 console.log(err)
//                 done()
//             })
//     });
//
//     // describe('/GET Room Hotel By StoreId',() => {
//     //     it('resolves',(done) => {
//     //
//     //        chai.request(server)
//     //             .get(uriAppDev)
//     //             .set('access-token',userToken)
//     //             .end((err,res) => {
//     //                 console.log(res);
//     //                 // res.should.have.status(200);
//     //                 res.body.should.have.property('access-token')
//     //                 done();
//     //             });
//     //     });
//     // });
//     /*
//     * Test the /POST route
//     */
//     // describe('/POST book',() => {
//     //     it('it should not POST a book without pages field',(done) => {
//     //         let book = {
//     //             title: "The Lord of the Rings",
//     //             author: "J.R.R. Tolkien",
//     //             year: 1954
//     //         }
//     //         chai.request(server)
//     //             .post('/book')
//     //             .send(book)
//     //             .end((err,res) => {
//     //                 res.should.have.status(200);
//     //                 res.body.should.be.a('object');
//     //                 res.body.should.have.property('errors');
//     //                 res.body.errors.should.have.property('pages');
//     //                 res.body.errors.pages.should.have.property('kind').eql('required');
//     //                 done();
//     //             });
//     //     });
//     //
//     // });
// });
