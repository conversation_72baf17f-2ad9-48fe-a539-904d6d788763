// process.env.NODE_ENV = 'production';
process.env.NODE_ENV = 'dev';

const testConfig = require('./config');
// const axios = require('axios');
// let expect = require('chai').expect;
let assert = require('chai').assert;
// let domain = require('../www/config/CfApp').domain

const NotificationModel = require('../www/models/NotificationModel');
const {TypeServices} = require("../www/models/enum/TypeServices");

const userIdOwnerStore = '5fa25caddd2a6d44377cc717' // người gửi
const idNguoiNhan = testConfig.user.id; // Nguoi <PERSON>han
const idDonHang = '5fa262502c8c93a68c887f18';
const orderId = '1604477519750';
const toUserId = '5fa25caddd2a6d44377cc717'; // người nhận
const storeId = '5f68128a4a439506adb900c9';
const storeUserId = '5f6c3b1d1cf1450c172e1927';
const idLichHen = '5f7e0cfb272dda0e4caae857';
const io = null

describe('Notifications', function () {
    describe('addNewNotification', function () {
        console.log(process.env.NODE_ENV)

        it('USER: Báo cho người dùng đã được chấp nhận lịch hẹn', async function () {

            const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, storeUserId, "Lịch hẹn của bạn đã được chấp nhận", 4, io, {
                bookId: idLichHen, // copyID record trong database paste vào để test
                typeService: TypeServices.SPA,
                storeId
            });
            console.log(rs)
            assert.isNotNull(rs)
        });

        it('USER: Báo cho người dùng khi huỷ lịch hẹn', async function () {
            const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, storeUserId, "Lịch hẹn của bạn đã bị huỷ", 5, io, {
                bookId: idLichHen,
                typeService: TypeServices.HOTEL,
                storeId
            });
            console.log(rs)
            assert.isNotNull(rs)
        });

        it('USER: Báo cho người dùng khi đơn hàng được chấp nhận', async function () {
            const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, storeUserId, "Đơn hàng của bạn đã được chấp nhận", 2, io, {
                requestId: idDonHang
            });
            console.log(rs)
            assert.isNotNull(rs)
        });

        it('USER: Báo cho người dùng khi đơn hàng bị huỷ', async function () {
            const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, storeUserId, "Đơn hàng của bạn đã bị huỷ", 3, io, {
                requestId: idDonHang
            });
            console.log(rs)
            assert.isNotNull(rs)
        });

        // it('STORE: Báo cho Store khi có đơn hàng', async function () {
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, testConfig.store.id, "Người dùng đặt sản phẩm", 0, io, {
        //         requestId: idDonHang,
        //         storeId,
        //         orderId
        //     });
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('STORE: Báo cho Store khi huỷ đơn hàng', async function () {
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, testConfig.store.id, 'huỷ đơn hàng', 6, io, {
        //         message: "Ly Do huy Don Hang ABC",
        //         typeService: 0,
        //         requestId: idDonHang,
        //         storeId: storeId,
        //     });
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('STORE: Báo cho Store khi huỷ lịch hẹn', async function () {
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, testConfig.store.id, 'huỷ lịch hẹn', 7, io, {
        //         message: "Ly Do Huy Dat Lich Dich Vu",
        //         typeService: TypeServices.SPA,
        //         bookId: idLichHen,
        //         storeId,
        //     });
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('STORE: Báo cho Store khi nguoi dung Da Nhan Hang', async function () {
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, testConfig.store.id, 'Xác nhận đã nhận hàng', 8, io, {
        //         typeService: 0,
        //         requestId: idDonHang,
        //         storeId: storeId,
        //     });
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('STORE: Báo cho Store Admin chấp nhận đơn trả Hang', async function () {
        //     // Admin dong y tra hang
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, testConfig.store.id, "Chấp nhận đơn hàng trả hàng", 9, io, {
        //         requestId: idDonHang
        //     })
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('STORE: Admin chấp nhận đơn trả Hang', async function () {
        //     // Admin dong y tra hang
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, null, "Chấp nhận đơn hàng trả hàng", 9, io, {
        //         requestId: idDonHang
        //     })
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('Cron Thông báo chủ shop sắp đến giờ trả phòng', async function () {
        //     // Admin dong y tra hang
        //     const rs = await NotificationModel.MODEL.addNewNotification(storeUserId, null, 'Thông báo chủ shop sắp đến giờ trả phòng', 10, io, {
        //         typeService: TypeServices.HOTEL,
        //         bookId: idLichHen
        //     })
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('Cron Thông báo user sắp đến giờ trả phòng', async function () {
        //     const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, null, 'Thông báo user sắp đến giờ trả phòng', 11, io, {
        //         typeService: TypeServices.HOTEL,
        //         bookId: idLichHen
        //     })
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });
        // it('Admin Gui Thong Bao', async function () {
        //     // Type user 0: All user. Test User shop cho đỡ nhieu ban ghi.
        //     const rs = await NotificationModel.MODEL.addNewNotification(idNguoiNhan, idNguoiNhan, "Admin thong bao", 12, io, {
        //         message: "Noi Dung Thong Bao",
        //         typeUser: 1,
        //         watched: 1
        //     });
        //     console.log(rs)
        //     assert.isNotNull(rs)
        // });

    });
});
