db.searchs2.drop()
db.createView(
    "searchs2",
    "services",
    [{
        $addFields: {
            serviceId: {$toString: '$_id'}
        }
    },
        {
            $lookup: {
                from: "room_hotels",
                localField: "serviceId",
                foreignField: "storeId",
                as: "roomHotels"
            }
        },
        {
            $lookup: {
                from: "serviceofspas",
                localField: "serviceId",
                foreignField: "storeId",
                as: "serviceofspas"
            }
        },
        {
            $lookup: {
                from: "service_of_clinics",
                localField: "serviceId",
                foreignField: "storeId",
                as: "clinics"
            }
        },
        {
            $addFields: {
                countService: {$size: "$serviceofspas"},
            }
        },
        {
            $addFields: {
                countRoom: {$size: "$roomHotels"},
            }
        },
        {
            $addFields: {
                countClinic: {$size: "$clinics"},
            }
        },
        {
            $project: {
                "clinics": 0,
                "serviceofspas": 0,
                "roomHotels": 0,
                "content": 0
            }
        }
    ]
)
