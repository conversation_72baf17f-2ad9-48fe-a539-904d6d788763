<<<<<<< HEAD
# **Tên dự án**: maxQServer
=======
# NHHS

>>>>>>> Incoming

<<<<<<< HEAD
**Mô tả**:
Source code bao gồm landing page, trang đăng nhập, web admin, và web người kinh doanh.
=======
This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 18.0.7.

>>>>>>> Incoming

<<<<<<< HEAD
**Ngôn ngữ**: Nodejs, web site sử dụng server render, có sử dụng cluster để tận dụng sức mạnh máy chủ, và
có kết nối đến redis-server (port 6379).
=======
## Development server

>>>>>>> Incoming

<<<<<<< HEAD
**Cơ sở dữ liệu**: Mongodb
=======
Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

>>>>>>> Incoming

<<<<<<< HEAD
#Thanhnx: <PERSON><PERSON> chạy đượ<PERSON> dự án cần cài https://www.notion.so/thanhnx/H-ng-d-n-c-i-mongodb-tr-n-mac-feaefa4693d9470a82cf390c0fded376
=======
## Code scaffolding

>>>>>>> Incoming

<<<<<<< HEAD
**Thư viện hỗ trợ các chức năng mở rộng**:
=======
Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

>>>>>>> Incoming

<<<<<<< HEAD
- Gửi thông báo đến mobile app: thư viện fcm-push, cầu hình ở `5.1.6`
- Gửi email: thư viện nodemailer
- Thanh toán online: vnpay, cầu hình ở `5.1.5`
=======
## Build

>>>>>>> Incoming

<<<<<<< HEAD
\*\*Install redis
=======
Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

>>>>>>> Incoming

<<<<<<< HEAD
docker-compose -f redis.yml up -d
=======
## Running unit tests

>>>>>>> Incoming

<<<<<<< HEAD
# **Cấu trức dự án**
=======
Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

>>>>>>> Incoming

<<<<<<< HEAD
1 File khởi chạy:
=======
## Running end-to-end tests

>>>>>>> Incoming

<<<<<<< HEAD
    ├── app.js
=======
Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

>>>>>>> Incoming

<<<<<<< HEAD
`có tích hợp cluster để xử lý nodejs chạy đa nhân`
=======
## Further help

>>>>>>> Incoming

<<<<<<< HEAD
`chạy local dev với lệnh` node app.js

2 Thư mục giao diện web và các file js tải, xử lý dự liệu hiể thị

    ├── template

`các file gồm js, ảnh, css, được phân chia theo thư mục phân quền hoặc thư viện sử dụng`

3 Thư mục chứa file upload

    ├── files

4 Thư mục chứa file mẫu xuất excel cho các tác vụ cần xuất excel

    ├── files-mau

5 Thư mục chứu toàn bộ cài đặt và source chính

    ├── www

5.1 Cấu hình thông số cơ bản để khởi chạy web

    ├── www/config

`Chứa tất cả các file cấu hình cơ bản cho hệ thông web`

5.1.1 Cấu hình ip, port và database

    ├── www/config/CfApp.js


        protocol: 'https',
        domainRelease: 'appmypet.com', // tên miền
        webName: 'MyPet', // tên website
        hostProduct: '**************', // ip product
        postProduct: '4000', // port product

        hostDev: 'localhost', // ip môi trường dev
        postDev: '5000', // port môi trường dev


        dbProduct: { // thông số kết nối db product
            name: 'mypetweb',
            user: 'mypetwebproduct',
            password: 'mypetwebproduct',
            host: '**************',
            port: '27017'

        },

        dbDev: { // thông số kết nối db dev
            name: 'mypetweb',
            user: 'mypetwebproduct',
            password: 'mypetwebproduct',
            host: '**************',
            port: '27017'
        },


5.1.2 Cấu hình tham số version của cache

    ├── www/config/CfCache.js

5.1.3 Cấu hình phân quền

    ├── www/config/CfRole.js

5.1.4 Cấu hình môi trường phát triển

    ├── www/config/CfMode.js

    hostProduct: true,
    databaseProduct: true,

`chuyển đổi giữa ip dev và product, db dev và product`

5.1.5 Cấu hình thông số thanh toán vnpay

    ├── www/config/CfVpnPay.js



5.2 Các chức năng hỗ trợ cho local

    ├── www/locals

`Bao gồm các hàm hỗ trợ server render xử lý số, ký tự, thời gian chi tiết trong www/locals/module`

5.3 Chứ năng gửi mail

    ├── www/mailer

`Bao gồm các hàm gửi mail đến người dùng hệ thống, chi tiết trong www/mailer/module`

5.4 Class, phương thức lưu trữ session cho các đối tượng

    ├── www/session

5.5 Xử lý kết nối socket và các chức năng liên quan đến kết nối socket

    ├── www/socket

5.6 Các hàm tự khởi chạy cho hệ thống giám sát

    ├── www/system

5.6.1 Kiểm tra mã coupon đến hạn thời gian kết thúc

    ├── www/system/module/kiemTraCoupon.js

5.6.2 Kiểm tra thời gian đến hạn trả phòng

    ├── www/system/module/kiemTraTraPhong.js


5.7 Files giao diện hiển thị

    ├── www/views


5.7.1 Các files giao diện độc lập

    ├── www/views/pages


`gồm các file đăng nhập, đăng ký, landingpage, quên mật khẩu, ....`

5.7.2 Các files giao diện cho tài khoản sau đăng nhập:

    ├── www/views/public/layout

`Chứa header, footer`

    ├── www/views/public/inc

`Các trang của admin, người kinh doanh được phân chia theo thư mục`

5.8 Files cấu trúc cơ sơ dữ liệu cho các đối tượng

    ├── www/database

`Chức năng các file được mô tả rõ ở đầu mỗi file`

5.9 Files model xử lý chung tương ứng cho mỗi đối tượng csdl ở trên

    ├── www/models

5.10 Routing và phân quền

    ├── www/routing

`Chứa tất cả các files đăng ký router, và phân quền theo ý nghĩa từng file`

    ├── www/routing/public.js  Sử dụng chung có các router không cần phân quền
    ├── www/routing/admin.js  Các router dành riêng cho admin
    ├── www/routing/store.js  Các router dành riêng cho người kinh doanh
    ├── www/routing/user-api.js  Các router dành riêng user (api)

{
"admin": 0,
"store": 1,
"user": 2,
"all": -1,
"noAccount": -2,
"account": -3
}

# Phần BUILD đang tự động CICD gitlab, cần pm2 restart app_name trên hosting để nhận code mới

NODE_ENV=development node app.js
NODE_ENV=production node app.js
NODE_ENV=local node app.js
=======
To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.

>>>>>>> Incoming
