{"info": {"name": "LBVD User API Collection", "description": "API endpoints for user authentication and management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register by Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"0987654321\",\n    \"password\": \"********\",\n    \"fullName\": \"<PERSON><PERSON><PERSON>\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/register-by-phone.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "register-by-phone.html"]}, "description": "<PERSON><PERSON><PERSON> ký tài khoản bằng số điện thoại"}}, {"name": "Login by Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"0987654321\",\n    \"password\": \"********\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/login.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "login.html"]}, "description": "<PERSON><PERSON><PERSON> nhập bằng số điện thoại và mật khẩu"}}, {"name": "Login by <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"********\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/dang-nhap.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "dang-nhap.html"]}, "description": "<PERSON><PERSON><PERSON> nhập bằng email và mật khẩu"}}, {"name": "Login with Social Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fullName\": \"<PERSON><PERSON><PERSON>\",\n    \"phone\": \"\",\n    \"picture\": \"https://example.com/profile.jpg\",\n    \"facebookId\": \"********9\"\n    // Or alternatively use googleId or appleId\n}"}, "url": {"raw": "{{baseUrl}}/user/api/dang-nhap-social.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "dang-nhap-social.html"]}, "description": "<PERSON><PERSON><PERSON> nhập bằng tài khoản xã hội (Facebook, Google hoặc Apple)"}}, {"name": "Register with Social Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fullName\": \"<PERSON><PERSON><PERSON>\",\n    \"phone\": \"\",\n    \"picture\": \"https://example.com/profile.jpg\",\n    \"facebookId\": \"********9\"\n    // Or alternatively use googleId or appleId\n}"}, "url": {"raw": "{{baseUrl}}/user/api/register-social.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "register-social.html"]}, "description": "<PERSON><PERSON><PERSON> ký tài khoản bằng tài khoản xã hội (Facebook, Google hoặc Apple)"}}, {"name": "Register Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"fullName\": \"<PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"********\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/dang-ky.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "dang-ky.html"]}, "description": "<PERSON><PERSON><PERSON> ký tài k<PERSON>n bằng email"}}, {"name": "Forgot Password by Phone", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"0987654321\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/forgot-password.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "forgot-password.html"]}, "description": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u - <PERSON><PERSON><PERSON> thực số điện thoại"}}, {"name": "Recover Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"0987654321\",\n    \"password\": \"87654321\",\n    \"confirmPassword\": \"87654321\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/recover-password.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "recover-password.html"]}, "description": "Đặt lại mật khẩu bằng số điện thoại"}}, {"name": "Forgot Password by <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/user/api/quen-mat-khau.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "quen-mat-khau.html"]}, "description": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u - <PERSON><PERSON><PERSON> mã xác thực qua email"}}], "description": "Các API xác thực người dùng"}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}