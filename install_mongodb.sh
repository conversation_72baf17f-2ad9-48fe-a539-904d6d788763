#!/bin/bash

set -e

# ==============================
# 🛠️ CẤU HÌNH CƠ BẢN
MONGO_ADMIN_USER="admin"
MONGO_APP_DB="myappdb"
MONGO_APP_USER="appuser"

# 🔐 Sinh pass ngẫu nhiên (chỉ khi tạo mới)
MONGO_ADMIN_PASS=Thanhnx1Q2SS
MONGO_APP_PASS=Thanhnx1Q2SS

ENV_FILE=".env.mongodb"
# ==============================

echo "🟡 Cập nhật hệ thống..."
sudo apt update -y

echo "🟡 Gỡ bỏ MongoDB cũ (nếu có)..."
sudo systemctl stop mongod || true
sudo apt purge -y mongodb-org* || true
sudo rm -rf /var/log/mongodb /var/lib/mongodb || true

echo "🟡 Cập nhật hệ thống..."
sudo apt update -y

echo "📦 Thêm key và repo MongoDB 4.4 từ focal (dùng cho Ubuntu 22.04)..."
wget -qO - https://www.mongodb.org/static/pgp/server-4.4.asc | sudo apt-key add -
echo "deb [ arch=amd64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/4.4 multiverse" | \
    sudo tee /etc/apt/sources.list.d/mongodb-org-4.4.list
sudo apt update

# ✅ Cài MongoDB 4.4.25
MONGO_VERSION="4.4.25"
echo "🛠️ Cài MongoDB version $MONGO_VERSION..."
sudo apt install -y \
  mongodb-org=$MONGO_VERSION \
  mongodb-org-server=$MONGO_VERSION \
  mongodb-org-shell=$MONGO_VERSION \
  mongodb-org-mongos=$MONGO_VERSION \
  mongodb-org-tools=$MONGO_VERSION

# ✅ Ghim version để tránh bị update ngoài ý muốn
echo "📌 Ghim version MongoDB..."
echo "mongodb-org hold" | sudo dpkg --set-selections
echo "mongodb-org-server hold" | sudo dpkg --set-selections
echo "mongodb-org-shell hold" | sudo dpkg --set-selections
echo "mongodb-org-mongos hold" | sudo dpkg --set-selections
echo "mongodb-org-tools hold" | sudo dpkg --set-selections
echo "✅ Bật và khởi động MongoDB..."
sudo systemctl enable mongod
sudo systemctl start mongod

# ✅ Đợi MongoDB mở port 27017
echo "⏳ Kiểm tra MongoDB có lắng nghe trên cổng 27017..."
for i in {1..10}; do
    if nc -z localhost 27017 >/dev/null 2>&1; then
        echo "✅ MongoDB đã sẵn sàng!"
        break
    else
        echo "⏳ Đang chờ MongoDB khởi động ($i/10)..."
        sleep 2
    fi
    if [ $i -eq 10 ]; then
        echo "❌ MongoDB không sẵn sàng sau 20s. Dừng script!"
        exit 1
    fi
done

# ✅ Tạo admin nếu chưa tồn tại
if ! mongosh admin --quiet --eval "db.getUser('$MONGO_ADMIN_USER')" | grep -q "$MONGO_ADMIN_USER"; then
    echo "🔐 Tạo user admin..."
    mongosh admin --eval "
    db.createUser({
      user: '$MONGO_ADMIN_USER',
      pwd: '$MONGO_ADMIN_PASS',
      roles: [ { role: 'root', db: 'admin' } ]
    });
    "
    ADMIN_CREATED=true
else
    echo "ℹ️ User admin đã tồn tại. Bỏ qua."
    ADMIN_CREATED=false
fi

# ✅ Tạo app user nếu chưa có
if $ADMIN_CREATED; then
    AUTH_URI="*******************************************************************"
else
    echo "🔐 Nhập mật khẩu của user admin để tạo app user:"
    read -s ADMIN_PASS_INPUT
    AUTH_URI="*******************************************************************"
fi

if ! mongosh "$AUTH_URI" --quiet --eval "db.getSiblingDB('$MONGO_APP_DB').getUser('$MONGO_APP_USER')" | grep -q "$MONGO_APP_USER"; then
    echo "🔐 Tạo user ứng dụng '$MONGO_APP_USER' trên DB '$MONGO_APP_DB'..."
    mongosh "$AUTH_URI" --eval "
    db = db.getSiblingDB('$MONGO_APP_DB');
    db.createUser({
      user: '$MONGO_APP_USER',
      pwd: '$MONGO_APP_PASS',
      roles: [ { role: 'readWrite', db: '$MONGO_APP_DB' } ]
    });
    "
    APP_USER_CREATED=true
else
    echo "ℹ️ User ứng dụng đã tồn tại. Bỏ qua."
    APP_USER_CREATED=false
fi

# ✅ Bật xác thực và remote access nếu chưa bật
if ! grep -q 'authorization: "enabled"' /etc/mongod.conf; then
    echo "🔒 Bật xác thực MongoDB..."
    sudo sed -i '/#security:/a\  authorization: "enabled"' /etc/mongod.conf
fi

if grep -q 'bindIp: 127.0.0.1' /etc/mongod.conf; then
    echo "🌐 Mở remote access..."
    sudo sed -i 's/bindIp: 127.0.0.1/bindIp: 0.0.0.0/' /etc/mongod.conf
fi

echo "🔁 Khởi động lại MongoDB để áp dụng config..."
sudo systemctl restart mongod

echo "🛡️ Mở port 27017 trên UFW..."
sudo ufw allow 27017 || true
sudo ufw reload || true

# ▶️ Lấy IP LAN hoặc fallback sang IP public
IP=$(hostname -I | awk '{print $1}')
if [ -z "$IP" ]; then
    IP=$(curl -s ifconfig.me)
fi

# ===========================================
# ✅ IN THÔNG TIN SAU CÀI ĐẶT
echo ""
echo "🎉 MongoDB đã được cấu hình hoàn tất!"
echo "=============================================="

if $ADMIN_CREATED; then
    echo "🔑 Tài khoản ADMIN (vừa tạo):"
    echo "  Username: $MONGO_ADMIN_USER"
    echo "  Password: $MONGO_ADMIN_PASS"
fi

if $APP_USER_CREATED; then
    echo ""
    echo "🧾 Tài khoản ỨNG DỤNG (vừa tạo):"
    echo "  Database: $MONGO_APP_DB"
    echo "  Username: $MONGO_APP_USER"
    echo "  Password: $MONGO_APP_PASS"
fi

echo ""
echo "🌐 Kết nối MongoDB từ xa:"
echo "  mongodb://$MONGO_APP_USER:$MONGO_APP_PASS@$IP:27017/$MONGO_APP_DB?authSource=admin"
echo "=============================================="

# ✅ Ghi file .env nếu user được tạo
if $APP_USER_CREATED || $ADMIN_CREATED; then
    echo "💾 Ghi file $ENV_FILE"
    cat <<EOF > $ENV_FILE
# MongoDB .env file
MONGO_ADMIN_USER=$MONGO_ADMIN_USER
MONGO_ADMIN_PASS=$MONGO_ADMIN_PASS
MONGO_APP_USER=$MONGO_APP_USER
MONGO_APP_PASS=$MONGO_APP_PASS
MONGO_APP_DB=$MONGO_APP_DB
MONGO_URI=mongodb://$MONGO_APP_USER:$MONGO_APP_PASS@$IP:27017/$MONGO_APP_DB?authSource=admin
EOF
    echo "✅ Thông tin đã ghi vào $ENV_FILE"
fi
