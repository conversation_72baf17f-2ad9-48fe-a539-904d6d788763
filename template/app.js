function get(url, data, success, error, showLoading = false) {
    if (showLoading) {
        $('#preloader').css({opacity: 1, display: 'table'});
    }
    $.ajax({
        url: url,
        data: data,
        type: 'get',
        success: function (response) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            success(response);

        },
        error: function (err) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            if (error) error(err);

        }
    })
}

function objectifyForm(formArray) {
    var returnArray = {};
    for (var i = 0; i < formArray.length; i++) {
        let name = formArray[i]['name'];
        if (name.toLowerCase().includes('date') || name.toLowerCase().includes('birthday')
            || name.toLowerCase().includes('sendtime')) {
            if (formArray[i]['value'].includes(',')) {
                let valArr = formArray[i]['value'].split(',');
                let date = valArr[1].trim();
                let time = valArr[0].trim();
                let dateArr = null
                if (date.includes('/')) {
                    dateArr = date.split("/");
                } else if (date.includes('-')) {
                    dateArr = date.split("-");
                }
                formArray[i]['value'] = `${dateArr[1]}/${dateArr[0]}/${dateArr[2]}, ${time}`;
            } else {
                let valArr = formArray[i]['value']
                let val = null
                if (valArr.includes('/')) {
                    val = valArr.split("/");
                } else if (valArr.includes('-')) {
                    val = valArr.split("-");
                }
                formArray[i]['value'] = `${val[1]}/${val[2]}/${val[0]}`;
            }
        }

        returnArray[formArray[i]['name']] = formArray[i]['value'];
    }
    return returnArray;
}

function getDateValue(input) {
    if (input && input.trim() != '') {
        if (input.includes('/')) {
            let val = input.split("/");
            return `${val[1]}/${val[0]}/${val[2]}`;
        } else if (input.includes('.')) {
            let val = input.split(".");
            return `${val[1]}/${val[0]}/${val[2]}`;
        }
    } else {
        return input;
    }
}

function formatDateInput(input) {
    if (input && input.trim() != '') {
        if (input.includes('/')) {
            let val = input.split("/");
            return `${val[2]}${val[1]}${val[0]}`;
        } else if (input.includes('.')) {
            let val = input.split(".");
            return `${val[2]}${val[1]}${val[0]}`;
        } else if (input.includes('-')) {
            let val = input.split(".");
            return `${val[2]}${val[1]}${val[0]}`;
        } else {
            return input
        }
    } else {
        return input;
    }
}

function post(url, data, success, error, showLoading = false) {
    if (showLoading) {
        $('#preloader').css({opacity: 1, display: 'table'});
    }
    $.ajax({
        url: url,
        data: data,
        type: 'post',
        success: function (response) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            success(response);

        },
        error: function (err) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            if (error) error(err);

        }
    })

}

function displayError(text) {
    // M.toast({html: text, classes: 'error-toast'});
    Swal.fire({
        position: 'top-end',
        type: 'error',
        title: text,
        showConfirmButton: false,
        timer: 2500
    })
}

function displayWarning(text) {
    M.toast({html: text, classes: 'warning-toast'});

}

function displaySuccess(text) {
    // M.toast({html: text, classes: 'success-toast'});
    Swal.fire({
        position: 'top-end',
        type: 'success',
        title: text,
        showConfirmButton: false,
        timer: 1500
    })
}


function ajaxFile(url, data, success, error, showLoading = false) {
    if (showLoading) {
        $('#preloader').css({opacity: 1, display: 'table'});
    }
    $.ajax({
        url: url,
        data: data,
        type: 'post',
        processData: false,
        contentType: false,
        success: function (response) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            success(response);

        },
        error: function (err) {
            if (showLoading) {
                $('#preloader').css('opacity', 0);
                setTimeout(function () {
                    $('#preloader').hide();
                }, 1000);
            }
            error(err);

        }
    })
}

function numberFormat(number, fixLength = 0) {
    if (fixLength == null) {
        let stringNum = number + '';
        let arrInc = stringNum.split('.');
        let fixNum = 0;
        if (arrInc.length == 2) {
            fixNum = arrInc[1].length;
        }

        fixNum = fixNum > 3 ? 3 : fixNum;

        return (Number(number)).toLocaleString('en-US', {minimumFractionDigits: fixNum});
    } else {
        return (Number(number)).toLocaleString('en-US', {minimumFractionDigits: fixLength});
    }
}

$(document).ajaxStart(function () {
    showLoading();
});


$(document).ajaxComplete(function () {
    hideLoading();
});

$(document).ajaxStop(function () {
    hideLoading();
});

function showLoading() {
    $('#loadingView').show()
}

function hideLoading() {
    $('#loadingView').hide()
}

function randomStringFixLengthOnlyAlphabet(count) {
    let text = "";
    let possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    for (let i = 0; i < count; i++)
        text += possible.charAt(Math.floor(Math.random() * possible.length));

    return text;
};

function copyToClipboard(elementId) {
    // Create a "hidden" input
    var aux = document.createElement("input");

    aux.setAttribute("value", $(elementId).val());
    // Append it to the body
    document.body.appendChild(aux);
    // Highlight its content
    aux.select();
    // Copy the highlighted text
    document.execCommand("copy");
    // Remove it from the body
    document.body.removeChild(aux);

}

function removeUtf8(str) {
    str = str.toString()
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    // str = str.replace(/\W+/g, ' ');
    // str = str.replace(/\s/g, '-');
    // str = str.replace(/[^a-zA-Z0-9]/g, '_');

    // let max = 10;
    // for (let index = max; index >= 0; index--) {
    //     let inc_ = "";
    //     for (let index2 = 0; index2 <= index; index2++) {
    //         inc_ += "_";
    //     }
    //     str = str.replace(inc_, '_');
    // }
    return str;
};


function removeUtf8ReplaceAll(str) {
    str = str.toLowerCase();
    str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a');
    str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e');
    str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i');
    str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o');
    str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u');
    str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y');
    str = str.replace(/đ/g, 'd');
    // str = str.replace(/\W+/g, ' ');
    str = str.replace(/\s/g, '-');
    str = str.replace(/[^a-zA-Z0-9]/g, '_');

    let max = 10;
    for (let index = max; index >= 0; index--) {
        let inc_ = "";
        for (let index2 = 0; index2 <= index; index2++) {
            inc_ += "_";
        }
        str = str.replace(inc_, '_');
    }
    return str;
};

$(function () {
    $('.refresh').on('click', function () {
        location.reload();
    })
})

function newMessageMp3() {
    document.getElementById("newMessage").play();
    setTimeout(() => {
        document.getElementById("newMessage").pause();
    }, 3000)
}

function createMaDanhMuc(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'DM' + index.substring(leftLength, index.length);
}

function createMaSanPham(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'MSP' + index.substring(leftLength, index.length);
}

function createMaDonHang(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'DH' + index.substring(leftLength, index.length);
}

function createCuaHang(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'CH' + index.substring(leftLength, index.length);
}

function createKhachSan(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'KS' + index.substring(leftLength, index.length);
}

function createPhongKham(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'PK' + index.substring(leftLength, index.length);
}

function createSpa(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'SPA' + index.substring(leftLength, index.length);
}

function createBranch(index) {
    index = '0000000000000000000000' + index;
    let maxLength = 5;
    let leftLength = index.length - maxLength;
    return 'CS' + index.substring(leftLength, index.length);
}



function hienThiSoTienBangChu(price) {
    let first = Math.round(Number(price)) + "";

    let firstFormat = numberFormat(first);
    let firstArr = firstFormat.split(",");
    let config3Name = {
        0: 'đồng',
        1: 'nghìn',
        2: 'triệu',
        3: 'tỉ',
    };

    let configText = {
        0: 'không',
        1: 'một',
        2: 'hai',
        3: 'ba',
        4: 'bốn',
        5: 'năm',
        6: 'sáu',
        7: 'bảy',
        8: 'tám',
        9: 'chín',
    };

    let nameTextArr = [];

    let indexName = 0;
    for (let i = firstArr.length - 1; i >= 0; i--) {
        let nameText = '';
        let boSoThu3 = false;
        let boSoThu2 = false;
        let indexCount = 0;
        for (let i2 = firstArr[i].length - 1; i2 >= 0; i2--) {
            nameText = nameText.trim();
            switch (indexCount) {
                case 0:
                    if (firstArr[i][i2] == '0') {
                        if (!firstArr[i][i2 - 1] || firstArr[i][i2 - 1] == '0' || firstArr[i][i2 - 1] == '1') {

                        } else {

                        }
                        boSoThu3 = true;
                    } else if (firstArr[i][i2] == '5') {
                        if (!firstArr[i][i2 - 1] || firstArr[i][i2 - 1] == '0') {
                            nameText = 'năm' + nameText;
                        } else {
                            nameText = 'lăm' + nameText;
                        }

                        boSoThu3 = false;
                    } else if (firstArr[i][i2] == '1') {
                        if (!firstArr[i][i2 - 1] || firstArr[i][i2 - 1] == '1' || firstArr[i][i2 - 1] == '0') {
                            nameText = 'một' + nameText;
                        } else {
                            nameText = 'mốt' + nameText;
                        }

                        boSoThu3 = false;
                    } else {
                        nameText = configText[firstArr[i][i2]] + nameText;
                        boSoThu3 = false;
                    }
                    break;
                case 1:
                    if (firstArr[i][i2] == '0') {
                        if (boSoThu3) {

                        } else {
                            nameText = 'lẻ ' + nameText;
                        }
                        boSoThu2 = true;
                    } else if (firstArr[i][i2] == '1') {
                        if (boSoThu3) {
                            nameText = 'mười ' + nameText
                        } else {
                            if (firstArr[i][i2 - 1]) {
                                nameText = 'mười ' + nameText
                            } else {
                                nameText = 'mười ' + nameText
                            }
                        }
                    } else {
                        nameText = configText[firstArr[i][i2]] + ' mươi ' + nameText
                    }

                    break;
                case 2:
                    nameText = configText[firstArr[i][i2]] + ' trăm ' + nameText;
                    break;
            }
            indexCount++;
            nameText = nameText.trim();
        }

        nameText = nameText + ' ' + config3Name[indexName];
        nameText = nameText.trim();
        nameTextArr.push(nameText);
        indexName++;
    }

    let newArr = [];
    for (let i = nameTextArr.length - 1; i >= 0; i--) {
        newArr.push(nameTextArr[i]);
    }
    let x = newArr.join(", ");
    return x.charAt(0).toUpperCase() + x.slice(1);
}

function formatCurrency(input, blur) {
    // appends $ to value, validates decimal side
    // and puts cursor back in right position.

    // get input value
    var input_val = input.val();
    let first = '';
    if (input_val.includes('-')) {
        first = '-';
    }

    // don't validate empty input
    if (input_val === "") {
        return;
    }

    // original length
    var original_len = input_val.length;

    // initial caret position
    var caret_pos = input.prop("selectionStart");

    // check for decimal
    if (input_val.indexOf(".") >= 0) {

        // get position of first decimal
        // this prevents multiple decimals from
        // being entered
        var decimal_pos = input_val.indexOf(".");

        // split number by decimal point
        var left_side = input_val.substring(0, decimal_pos);
        var right_side = input_val.substring(decimal_pos);

        // add commas to left side of number
        left_side = formatNumber(left_side);

        // validate right side
        right_side = formatNumber(right_side);

        // On blur make sure 2 numbers after decimal
        if (blur === "blur") {
            right_side += "00";
        }

        // Limit decimal to only 2 digits
        right_side = right_side.substring(0, 2);

        // join number by .
        input_val = left_side + "." + right_side + ' VND';

    } else {
        // no decimal entered
        // add commas to number
        // remove all non-digits
        input_val = formatNumber(input_val);
        input_val = input_val + " VND";

        // final formatting
        if (blur === "blur") {
            input_val += "";
        }
    }

    // send updated string to input
    input.val(first + input_val);

    // put caret back in the right position
    var updated_len = input_val.length;
    caret_pos = updated_len - original_len + caret_pos;
    input[0].setSelectionRange(caret_pos, caret_pos);
}


function reloadDataTypeCurrency() {
    $("input[data-type='currency']").on({
        keyup: function () {
            formatCurrency($(this));
        },
        blur: function () {
            formatCurrency($(this), "blur");
        }
    });

    $("input[data-type='currency']").keyup();
}


function formatNumber(n) {
    // format number 1000000 to 1,234,567
    return n.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",")
}

$(function () {
    $('.inputBoxCustom input').on('change paste keyup', function () {
        $(this).parent().find('p').fadeIn();
    });



    $(document).on('keyup', "input[data-type='currency']", function(){
        formatCurrency($(this));
    });

    $(document).on('blur', "input[data-type='currency']", function(){
        formatCurrency($(this));
    });
    $("input[data-type='currency']").keyup();


    $("input[data-type='number']").on({
        keyup: function () {
            formatNumberCustom($(this));
        },
        blur: function () {
            formatNumberCustom($(this), "blur");
        }
    });
    $("input[data-type='number']").keyup();

    function formatNumberCustom(input, blur) {
        // appends $ to value, validates decimal side
        // and puts cursor back in right position.

        // get input value
        var input_val = input.val();
        let first = '';
        if (input_val.includes('-')) {
            first = '-';
        }
        // don't validate empty input
        if (input_val === "") {
            return;
        }

        // original length
        var original_len = input_val.length;

        // initial caret position
        var caret_pos = input.prop("selectionStart");

        // check for decimal
        if (input_val.indexOf(".") >= 0) {

            // get position of first decimal
            // this prevents multiple decimals from
            // being entered
            var decimal_pos = input_val.indexOf(".");

            // split number by decimal point
            var left_side = input_val.substring(0, decimal_pos);
            var right_side = input_val.substring(decimal_pos);

            // add commas to left side of number
            left_side = formatNumber(left_side);

            // validate right side
            right_side = formatNumber(right_side);

            // On blur make sure 2 numbers after decimal
            if (blur === "blur") {
                right_side += "00";
            }

            // Limit decimal to only 2 digits
            right_side = right_side.substring(0, 2);

            // join number by .
            input_val = left_side + "." + right_side + '';

        } else {
            // no decimal entered
            // add commas to number
            // remove all non-digits
            input_val = formatNumber(input_val);
            input_val = input_val + "";

            // final formatting
            if (blur === "blur") {
                input_val += "";
            }
        }

        // send updated string to input
        input.val(first + input_val);

        // put caret back in the right position
        var updated_len = input_val.length;
        caret_pos = updated_len - original_len + caret_pos;
        input[0].setSelectionRange(caret_pos, caret_pos);
    }
});

//get param from url

function getUrlVars() {
    var vars = {};
    var parts = window.location.href.replace(/[?&]+([^=&]+)=([^&]*)/gi, function(m,key,value) {
        vars[key] = value;
    });
    return vars;
}

function getUrlParam(parameter, defaultvalue){
    var urlparameter = defaultvalue;
    if(window.location.href.indexOf(parameter) > -1){
        urlparameter = getUrlVars()[parameter];
    }
    return urlparameter;
}
