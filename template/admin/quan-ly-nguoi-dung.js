var userShows = users;
let currentPage = 1;
let itemsPerPage = 15;
let statusTable = 'all'
let totalPages = Math.ceil(userShows.length / itemsPerPage)

function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage - 1
    if (end > userShows.length - 1) {
        end = userShows.length
    }
    for (let i = start; i <= end; i++) {
        if (userShows[i]) {
            let user = userShows[i]
            let buttonHtml = ''
            if (user.status == 1) {
                buttonHtml = `
                <a href="javascript:;" class="cancel-user"title="Xác nhận khóa người dùng">
                    <img src="/template/ui/img/close.svg" alt="">
                </a> `
            } else {
                buttonHtml = `
                <a href="javascript:;" class="success-user" title="Xác nhận mở khóa người dùng">
                    <img src="/template/ui/img/check.svg" alt="">
                </a> `
            }

            html += `
            <tr id="${user._id}">
                    <td>${i + 1}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${user.phone}</td>
                    <td>${user.address}</td>
                    <td>${moment(Number(user.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td class="status">
                        <span class="${user.status == 1 ? 'deliveried_product' : 'cancle'}">
                        ${user.status == 1 ? 'Hoạt động' : 'Bị khóa'}
                        </span>
                    </td>
                    <td>
                        ${buttonHtml}
                        <a href="/admin/chinh-sua-nguoi-dung/${user._id}.html" title="Chỉnh sửa">
                            <img src="/template/ui/img/edit.svg" alt="">
                        </a>
                    </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${userShows.length} kết quả`)
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    currentPage = 1;
    userShows = []
    statusTable = $('.portfolio_select').val()
    if (statusTable == 'all') {
        userShows = users
    } else {
        users.forEach(user => {
            if (user.status == statusTable) {
                userShows.push(user)
            }
        })
    }
    let key = removeUtf8($('#search-goods').val().trim())
    let dataUsers = []
    userShows.forEach(item => {
        if (removeUtf8(item.fullName).includes(key)
            || removeUtf8(item.email.toString()).includes(key)
            || removeUtf8((item.phone).toString()).includes(key)
            || removeUtf8(item.address.toString()).includes(key)) {
            dataUsers.push(item)
        }
    })
    let timeFilter = $('#time-filter').val()
    let dataUsers2 = []
    if (!isNaN(new Date(timeFilter).getTime())) {
        dataUsers.forEach(item => {
            if (item.createAt >= new Date(timeFilter).getTime()) {
                dataUsers2.push(item)
            }
        })
    } else {
        dataUsers2 = dataUsers
    }
    userShows = dataUsers2
    totalPages = Math.ceil(userShows.length / itemsPerPage)
    renderTable()
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            renderTable();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        renderTable();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        currentPage = totalPages;
        renderTable();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            renderTable();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let page = e.currentTarget.value;
        currentPage = page;
        renderTable();
    });
    $(document).on('click', '.success-user', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái hoạt động?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-status-user/${id}.html?status=1`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    $(document).on('click', '.cancel-user', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái không hoạt động?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-status-user/${id}.html?status=2`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    $(document).on('click', '.delete-user', function (e) {
        let conf = confirm('Bạn có chắc muốn xóa yêu cầu này?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/delete-user/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    renderTable()
});

