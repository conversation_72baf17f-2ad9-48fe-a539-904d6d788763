jQuery.datetimepicker.setLocale('vi');
jQuery('.dateTimePicker').datetimepicker({
    format: 'H:i d/m/Y',
});


$(function () {
    $('.createCoupon').on('submit', function () {
        let data = objectifyForm($(this).serializeArray());

        let startTime = data.startTime;
        let endTime = data.endTime;

        let startTimeArr = startTime.split(" ");
        startTime = startTimeArr[0] + " " + startTimeArr[1].split('/')[1] + "/" + startTimeArr[1].split('/')[0] + "/" + startTimeArr[1].split('/')[2]

        let endTimeArr = endTime.split(" ");
        endTime = endTimeArr[0] + " " + endTimeArr[1].split('/')[1] + "/" + endTimeArr[1].split('/')[0] + "/" + endTimeArr[1].split('/')[2]

        data.startTime = new Date(startTime).getTime();
        data.endTime = new Date(endTime).getTime();

        data.value = data.value.split(",").join("");
        data.minBillValue = data.minBillValue.split(",").join("");
        data.minBillValue = data.minBillValue.replace("VND", '');
        data.minBillValue = Number(data.minBillValue.trim());
        if (data.startTime > data.endTime) {
            alert("Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc");
            return false;
        }

        if (new Date().getTime() >= data.endTime) {
            alert("Thời gian kết thúc phải lớn hơn thời gian hiện tại");
            return false;
        }

        post($(this).attr('action'), data, res => {
            displaySuccess(res.message);
            setTimeout(() => {
                location.href = '/admin/quan-ly-coupon.html';
            }, 1000);
        }, () => {
            displayError("Lỗi kết nối. Vui lòng kiểm tra lại");
        })
        return false;
    })
})