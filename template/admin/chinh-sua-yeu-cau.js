function successDoneScheduleViewBox(element) {
    $('#successDoneScheduleViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

function xacNhanHoanThanh() {
    let gia = $('#gia').val().trim().replace("VND", '').split(",").join("").trim();
    if (gia == '' || isNaN(Number(gia))) {
        alert("Vui lòng nhập chi phí");
    } else {
        if (!confirm("Bạn chắc chắn nạp tiền cho tài khoản này?")) return;
        post(`/admin/nap-tien-cho-nguoi-kinh-doanh/${dataUser._id}.html`, {price: Number(gia)}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}


function changeStatus(status) {
    let message = "Bạn chắc chắn muốn KHOÁ tài khoản này?";
    if (status == 1) message = "Bạn chắc chắn muốn MỞ khoá tài khoản này?";
    if (!confirm(message)) return;
    get(`/admin/change-status-user/${dataUser._id}.html?status=${status}`, {}, (response) => {
        if (!response.error) {
            location.reload()
        } else {
            displayError(response.message);
        }
    })
}

function changeStatusEmail() {
    let message = "Bạn chắc chắn muốn XÁC NHẬN EMAIL tài khoản này?";
    if (!confirm(message)) return;
    get(`/admin/change-status-email/${dataUser._id}.html`, {}, (response) => {
        if (!response.error) {
            location.reload()
        } else {
            displayError(response.message);
        }
    })
}


function lichSuTraTienChoCuaHang() {
    let timeStart = $('#time-start').val().trim();
    let timeEnd = $('#time-end').val().trim();

    try {
        let timeStartArr = timeStart.split(',');
        timeStart = `${timeStartArr[0].trim()} ${timeStartArr[1].trim().split("/")[1]}/${timeStartArr[1].trim().split("/")[0]}/${timeStartArr[1].trim().split("/")[2]}`
        let timeEndArr = timeEnd.split(',');
        timeEnd = `${timeEndArr[0].trim()} ${timeEndArr[1].trim().split("/")[1]}/${timeEndArr[1].trim().split("/")[0]}/${timeEndArr[1].trim().split("/")[2]}`
    } catch (e) {
        timeStart = '';
        timeEnd = '';
    }

    get(`/admin/lich-su-thanh-toan-online.html?search=&page=1&itemsPerPage=1000&timeStart=${timeStart}&timeEnd=${timeEnd}&userId=${dataUser._id}`, {}, res => {
        if (!res.error) {
            histories = res.data.histories;
            renderTable2(histories)
        }
    }, () => {
        displayError('Lỗi kết nối');
    })
}


function renderTable2(histories) {
    let html = '';
    histories.forEach((history, i) => {
        html += `
      <tr id="${history._id}">
          <td>${i + 1}</td>
          <td>${numberFormat(history.amount)} VNĐ</td>
          <td>${moment(history.createAt).format('HH:mm DD/MM/YYYY')}</td>`;

        html += `<td>
            <a href="javascript:;" class="xoa-lich-su" historyId="${history._id}" title="Xoá lịch sử">
              <img src="/template/ui/img/delete.svg" alt="">
            </a>
          </td>
      </tr>`
    });
    $(`.pagination-0 p`).text(`Hiển thị ${histories.length} kết quả`);
    $(`.table-0 .tbody-table`).html(html);
}

async function thanhToanSoDu() {
    const {value: amountPay} = await Swal.fire({
        title: `Nhập số tiền muốn thanh toán`,
        html: `<span style="color: red;">Tối đa: ${numberFormat(dataUser.wallet)} VNĐ</span>`,
        input: 'number',
        inputPlaceholder: 'Nhập số tiền muốn thanh toán',
        confirmButtonText: 'Thanh toán'
    });

    if (amountPay) {
        if (Number(amountPay) > 0) {
            if (!confirm(`Bạn chắc chắn muốn thanh toán ${numberFormat(amountPay)} VNĐ cho tài khoản này`)) return;
            post(`/admin/thanh-toan-wallet-online.html`, {userId: dataUser._id, amount: amountPay}, res => {
                if (res.error) {
                    displayError(res.message);
                } else {
                    displaySuccess('Thanh toán thành công');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            }, () => {
                displayError('Lỗi kết nối');
            })
        }
    }
}

$(function () {
    $('.dateTimeHandle').datetimepicker({
        i18n: {
            de: {
                months: [
                    'Tha.1', 'Tha.2', 'Tha.3', 'Tha.4',
                    'Tha.5', 'Tha.6', 'Tha.7', 'Tha.8',
                    'Tha.9', 'Tha.10', 'Tha.11', 'Tha.12',
                ],
                dayOfWeek: [
                    "CN", "Thu.2", "Thu.3", "Thu.4",
                    "Thu.5", "Thu.6", "Thu.7",
                ]
            }
        },
        format: 'H:i, d/m/Y',
        scrollMonth: false,
        scrollInput: false,
        scrollTime: false

    });

    lichSuTraTienChoCuaHang();

    get(`/admin/lay-thong-tin-phi-chu-shop.html/${dataUser._id}`, {}, (response) => {
        if (!response.error) {
            let {fundHistories, fundDay, funYesterday, funWeek, funMonth, funAll} = response.data
            $('.phi-thanh-toan .date-now').html(`${numberFormat(fundDay)} VNĐ`)
            $('.phi-thanh-toan .date-week').html(`${numberFormat(funWeek)} VNĐ`)
            $('.phi-thanh-toan .date-all').html(`${numberFormat(funAll)} VNĐ`)
            $('.phi-thanh-toan .date-yesterday').html(`${numberFormat(funYesterday)} VNĐ`)
            $('.phi-thanh-toan .date-month').html(`${numberFormat(funMonth)} VNĐ`)
            let html = ``
            fundHistories.forEach((item, index) => {
                let codeFund = ''
                let typeText = ''
                let link = ''
                switch (item.typeService) {
                    case 0:
                        typeText = 'Cửa hàng'
                        codeFund = `CH${item.code}`
                        link = `/admin/thi-tiet-don-hang/${item.requestId}.html`
                        break;
                    case 1:
                        typeText = 'Xưởng dịch vụ'
                        codeFund = `PK${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=1`
                        break;
                    case 2:
                        typeText = 'Điểm gửi xe'
                        codeFund = `KS${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=2`
                        break;
                    case 3:
                        typeText = 'Spa'
                        codeFund = `SPA${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=3`
                        break;
                }
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td><a href="${link}" style="color:blue">${codeFund}</a>
                    <td>${numberFormat(item.fee)} VNĐ</td>
                    <td>${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${typeText}</td>
                </tr>
                `
            })
            if (html == '') {
                $('.table-box-2 .will_pay').show()
                $('.table-box-2 .payed_container').hide();
                $('.table-2').remove();
            } else {
                $('.table-box-2 .will_pay').hide()
                $('.table-box-2 .payed_container').show()
            }
            $('.table-2 tbody').html(html)
        } else {
            displayError(response.message);
        }
    })

    get(`/admin/lay-thong-thu-nhap-chu-shop.html/${dataUser._id}`, {}, (response) => {
        if (!response.error) {
            let {thuNhaps, fundDay, funYesterday, funWeek, funMonth, funAll} = response.data
            $('.thu-nhap .date-now').html(`${numberFormat(fundDay)} VNĐ`)
            $('.thu-nhap .date-week').html(`${numberFormat(funWeek)} VNĐ`)
            $('.thu-nhap .date-all').html(`${numberFormat(funAll)} VNĐ`)
            $('.thu-nhap .date-yesterday').html(`${numberFormat(funYesterday)} VNĐ`)
            $('.thu-nhap .date-month').html(`${numberFormat(funMonth)} VNĐ`)
            let html = ``
            thuNhaps.forEach((item, index) => {
                let codeFund = ''
                let typeText = ''
                let link = ''
                switch (item.type) {
                    case 0:
                        typeText = 'Cửa hàng'
                        codeFund = `CH${item.code}`
                        link = `/admin/chi-tiet-don-hang/${item.id}.html`
                        break;
                    case 1:
                        typeText = 'Xưởng dịch vụ'
                        codeFund = `PK${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.id}.html?index=1`
                        break;
                    case 2:
                        typeText = 'Điểm gửi xe'
                        codeFund = `KS${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.id}.html?index=2`
                        break;
                    case 3:
                        typeText = 'Spa'
                        codeFund = `SPA${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.id}.html?index=3`
                        break;
                }
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td><a href="${link}" style="color:blue">${codeFund}</a>
                    <td>${numberFormat(item.price)} VNĐ</td>
                    <td>${moment(Number(item.timeCheckIn)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${typeText}</td>
                </tr>
                `
            })
            if (html == '') {
                $('.table-box-1 .will_pay').show()
                $('.table-box-1 .payed_container').hide();
                $('.table-1').remove();
            } else {
                $('.table-box-1 .will_pay').hide()
                $('.table-box-1 .payed_container').show()
            }
            $('.table-1 tbody').html(html)
        } else {
            displayError(response.message);
        }
    })

    get(`/admin/lay-thong-tin-nap-tien-chu-shop.html/${dataUser._id}`, {}, (response) => {
        if (!response.error) {
            let {recharges} = response.data
            let html = ``
            recharges.forEach((item, index) => {
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${numberFormat(item.count)} VNĐ</td>
                </tr>
                `
            })
            if (html == '') {
                $('.table-box-3 .will_pay').show()
                $('.table-box-3 .payed_container').hide();
                $('.table-3').remove();
            } else {
                $('.table-box-3 .will_pay').hide()
                $('.table-box-3 .payed_container').show()
            }
            $('.table-3 tbody').html(html)
        } else {
            displayError(response.message);
        }
    });

    $(document).on('click', '.xoa-lich-su', async function (e) {
        e.preventDefault();
        let historyId = $(this).attr('historyId');

        if (!confirm(`Bạn chắc chắn muốn lịch sử thanh toán này? Lưu ý: chỉ xoá lịch sử, chứ không hoàn lại tiền`)) return;
        post(`/admin/xoa-lich-su-thanh-toan-online.html`, {historyId}, res => {
            if (res.error) {
                displayError(res.message);
            } else {
                $('#' + historyId).remove();
            }
        }, () => {
            displayError('Lỗi kết nối');
        })
    });

});
