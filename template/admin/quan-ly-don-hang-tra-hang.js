var bills, billShows
let currentPage = 1;
let itemsPerPage = 10;
let categoryId = 'all'
let totalPages = 1
let statusTable = 'all'
let countBill = 0
function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage
    if (end > countBill) {
        end = countBill
    }
    for (let i = 0; i <= billShows.length-1; i++) {
        if (billShows[i]) {
            let bill = billShows[i];
            let status = '';
            if (!bill.adminConfirm || bill.adminConfirm == 0) {
                status = `<p style="background: orange; text-align: center; color: #fff; padding: 5px; border-radius: 15px;">Chờ xử lý</p>`
            } else {
                status = `<p style="background: green; text-align: center; color: #fff; padding: 5px; border-radius: 15px;">Đã xác nhận</p>`
            }

            html += `
            <tr id="${bill._id}">
                <td>${(currentPage - 1) * 10 + i + 1}</td>
                <td><a href="#">${bill.code}</a></td>
                <td>${bill.listProduct}</td>
                <td>${bill.storeName}</td>
                <td>${bill.userName}</td>
                <td>${moment(Number(bill.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                <td>${numberFormat(bill.totalMoney)} VNĐ</td>
                <td>${status}</td>
                <td>
                    <a href="/admin/chi-tiet-don-hang-tra-hang/${bill._id}.html" class="edit-bill" title="Chi tiết">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${countBill} kết quả`)
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    let search = $(`#search-goods`).val().trim()
    let statusTable = $(`#adminConfirm`).val().trim()
    get(`/admin/lay-danh-sach-don-hang-tra-hang.html?page=${currentPage}&search=${search}&adminConfirm=${statusTable}`, {}, (res) => {
        if (!res.error) {
            bills = res.data.bills
            billShows = bills
            countBill = res.data.countBill
            totalPages = Math.ceil(res.data.countBill / itemsPerPage)
            renderTable()
        } else {
            displayError(res.message);
        }
    })
}

function resetCountNotificationsBillReturn() {
    get('/admin/reset-count-notification-bill-return.html', {}, (res) => {
    });
    $('#number-notification-bill-return').hide()
    $('#number-notification-bill-return').text(0)
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            filterList();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        filterList();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        currentPage = totalPages;
        filterList();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            filterList();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let page = e.currentTarget.value;
        currentPage = page;
        filterList();
    });
    filterList()
    resetCountNotificationsBillReturn();

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-don-hang-tra-hang.html`, {
            bills: billShows,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

})


