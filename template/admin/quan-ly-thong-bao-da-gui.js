var notifications;
var editor;
function renderNotification() {
    let html = ''
    notifications.forEach(item => {
        let title = '';
        switch (item.type) {
            case 12:
                item.avatarUser = '/template/ui/img/logo.png';
                title = `<b class="name">${item.title}</b><br>${item.message}`;
                break;
        }
        html += `
            <li class="customer_medical" id="${item._id}">
                <input class='select-delete' type="checkbox" notificationId="${item._id}" style="margin-right: 10px;">
                <div class="image_customer">
                    <img style="border-radius: 50%;" src="${item.avatarUser}" alt="">
                </div>
                <div class="info_customer">
                    ${title}
                    <p class="time">${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}
<!--                        <a href="javascript:;" class="edit-notification" title="Chỉnh sửa" style="padding-left:5px">-->
<!--                            <img src="/template/ui/img/edit.svg" alt="">-->
<!--                        </a>-->
                        <a href="javascript:;" class="delete-notification" title="Xoá" style="padding-left:5px">
                            <img src="/template/ui/img/delete.svg" alt="">
                        </a>
                    </p>
                </div>
            </li>
        `
    })
    if (notifications.length > 0) {
        $('.list_medical').show()
        $('.no-notification').hide()
    } else {
        $('.list_medical').hide()
        $('.no-notification').show()
    }
    $('.medical_schedule .list_medical').html(html)
}

function guiThongBao() {
    $('#guiThongBao').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
    return false;
}

function deleteThongBao() {
    let listIds = []
    $(".select-delete:checked").each(function (index) {
        listIds.push($(this).attr('notificationId'))
    });
    if (listIds.length == 0) return false
    let conf = confirm('Bạn có chắc muốn xóa những thông báo này?');
    if (conf) {
        post(`/admin/delete-notifications.html`, {listIds}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

get('/admin/lay-du-lieu-thong-bao-da-gui.html', {}, (res) => {
    if (!res.error) {
        notifications = res.data.notifications
        renderNotification()
    }
})

$(document).on('click', '.edit-notification', function (e) {
    let id = $(this).parents('.customer_medical').attr('id')
    $('#suaThongBao').addClass('open');
    $('#suaThongBao form').attr('action', `/admin/sua-thong-bao-da-gui/${id}.html`)
    notifications.forEach(item => {
        if (item._id == id) {
            $('#suaThongBao form input[name="title"]').val(item.title)
            $('#suaThongBao form textarea[name="content"]').text(item.message)
        }
    })
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
});

$(document).on('click', '.delete-notification', function (e) {
    let conf = confirm('Bạn có chắc muốn xóa thông báo này?');
    let id = $(this).parents('.customer_medical').attr('id')
    if (conf) {
        get(`/admin/delete-notification/${id}.html`, {}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
});
$(() => {
    $('#form-send-notification').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-send-notification').serialize().replace(/&?content=[^&]*/g, '');
        data += `&content=${encodeURIComponent(editor.getData())}`
        post('/admin/quan-ly-thong-bao-da-gui.html', data, res => {
            if (res.error) {
                displayError(res.message);
            } else {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }, error => {
            displayError('Lỗi kết nối');
        }, true)
    })


})

$(() => {

    console.log('init')
    loadUsers();

    $('select[name="typeUser"]').on('change', function (event) {
        const typeUser = event.target.value;
        toggleUserIdsField(typeUser)
        // if (typeUser == '9') {
        //     loadUsers()
        // }
    })

    function toggleUserIdsField(id) {
        if (id == '9') {
            $('#user_ids_row').show();
        } else {
            $('#user_ids_row').hide();
        }

    }

    function uiLoading() {
        $.blockUI({
            css: {
                backgroundColor: '#0fe218',
                color: '#fff',
                padding: '20px',
                'border-radius': '8px',
                border: 'none',
                'z-index': 9999,
            }, message: 'Vui lòng chờ...'
        });
    }

    function loadUsers() {
        uiLoading();
        $.ajax({
            url: `/rest/v1/get-users`,
            data: {},
            type: 'get',
            success: function (response) {
                $('select#userIds').select2({
                    multiple: true,
                    allowClear: true,
                    theme: "bootstrap4",
                    width: 'resolve',
                    placeholder: '[[--------- Chọn người dùng --------]]',
                    data: response.users.map(user => ({id: user._id, text: user.fullName})),
                }).trigger('change')
                $.unblockUI();
            },
            error: function (err) {
                $.unblockUI();
            }
        })
    }
})

$(() => {

    editor = CKEDITOR.replace('content', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 200
    });
})
