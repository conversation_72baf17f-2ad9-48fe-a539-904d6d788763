function removeUser() {
    if (!confirm("Bạn chắc chắn muốn xoá người dùng này? Lưu ý: hành động này không thể hoàn tác, và tất cả dữ li<PERSON>u đặt lịch, mua <PERSON>ả<PERSON>h<PERSON>, tin nhắn,... của user này cũng sẽ bị xoá")) return;

    get(`/admin/xoa-nguoi-dung.html/${userId}`, {}, res => {
        if (res.error) {
            displayError(res.message);
        } else {
            displaySuccess(res.message);
            setTimeout(() => {
                location.href = '/admin/quan-ly-nguoi-dung.html';
            }, 1000);
        }
    }, error => {
        displayError("Lỗi kết nối");
    })
}

function changeStatusEmail() {
    let message = "Bạn chắc chắn muốn XÁC NHẬN EMAIL tài khoản này?";
    if (!confirm(message)) return;
    get(`/admin/change-status-email/${dataUser._id}.html`, {}, (response) => {
        if (!response.error) {
            location.reload()
        } else {
            displayError(response.message);
        }
    })
}
