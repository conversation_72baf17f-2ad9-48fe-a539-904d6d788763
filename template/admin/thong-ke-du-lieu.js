$(function () {
    get(`/admin/lay-thong-ke-du-lieu.html`, {}, (response) => {
        if (!response.error) {
            let {objThongKe} = response.data;
            for (let key in objThongKe){
                let text = ''
                if (key=='nguoi-dung' || key=='nguoi-kd'){
                    text = 'Người dùng'
                } else if (key=='cua-hang') {
                    text= 'Cửa hàng'
                } else {
                    text= 'Phòng'
                }
                $(`.${key} .date-now`).html(`${(objThongKe[key].fundDay)} ${text}`)
                $(`.${key} .date-week`).html(`${(objThongKe[key].funWeek)} ${text}`)
                $(`.${key} .date-all`).html(`${(objThongKe[key].funAll)} ${text}`)
                $(`.${key} .date-yesterday`).html(`${(objThong<PERSON>e[key].funYesterday)} ${text}`)
                $(`.${key} .date-month`).html(`${(objT<PERSON><PERSON><PERSON>[key].funMonth)} ${text}`)
            }
        } else {
            displayError(response.message);
        }
    })
})
