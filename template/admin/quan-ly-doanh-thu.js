function chinhSuaHuongDanView() {
    $('#huongDanNapTien').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

let editor;

function luuHuongDan() {
    post('/admin/chinh-sua-huong-dan-nap-tien.html', {content: editor.getData()}, (res) => {
        displaySuccess("Cập nhật thành công")
    }, (err) => {
        displayError(err);
    })
}

$(() => {
    editor = CKEDITOR.replace('content', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 100
    });
});

$(function () {
    get('/admin/lich-su-thu-phi-he-thong.html', {}, (response) => {
        if (!response.error) {
            let {fundHistories, fundDay, funYesterday, funWeek, funMonth, funAll, walletOnline} = response.data
            $('.tong-wallet').html(`${numberFormat(walletOnline.total)} VNĐ`);

            $('.phi-thanh-toan .date-now').html(`${numberFormat(fundDay)} VNĐ`);
            $('.phi-thanh-toan .date-week').html(`${numberFormat(funWeek)} VNĐ`);
            $('.tong-phi-thu').html(`${numberFormat(funAll)} VNĐ`)
            $('.phi-thanh-toan .date-yesterday').html(`${numberFormat(funYesterday)} VNĐ`)
            $('.phi-thanh-toan .date-month').html(`${numberFormat(funMonth)} VNĐ`)
            let html = ``
            fundHistories.forEach((item, index) => {
                let codeFund = ''
                let typeText = ''
                let link = ''
                switch (item.typeService) {
                    case 0:
                        typeText = 'Cửa hàng'
                        codeFund = `CH${item.code}`
                        link = `/admin/chi-tiet-don-hang/${item.requestId}.html`
                        break;
                    case 1:
                        typeText = 'Xưởng dịch vụ'
                        codeFund = `PK${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=1`
                        break;
                    case 2:
                        typeText = 'Điểm gửi xe'
                        codeFund = `KS${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=2`
                        break;
                    case 3:
                        typeText = 'Spa'
                        codeFund = `SPA${item.code}`
                        link = `/admin/chi-tiet-lich-hen/${item.bookId}.html?index=3`
                        break;
                }
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td><a href="/admin/chinh-sua-yeu-cau/${item.storeId}.html" style="color:blue">${item.storeFullName}</a>
                    <td><a href="${link}" style="color:blue">${codeFund}</a>
                    <td>${numberFormat(item.fee)} VNĐ</td>
                    <td>${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${typeText}</td>
                </tr>
                `
            })
            if (html == '') {
                $('.table-box-2 .will_pay').show()
                $('.table-box-2 .payed_container').hide();
                $('.table-2').remove();
            } else {
                $('.table-box-2 .will_pay').hide()
                $('.table-box-2 .payed_container').show()
            }
            $('.table-2 tbody').html(html)
        } else {
            displayError(response.message);
        }
    }, () => {
    })

    get(`/admin/lich-su-nap-tien.html`, {}, (response) => {
        if (!response.error) {
            let {recharges, fundDay, funYesterday, funWeek, funMonth, funAll} = response.data;
            $('.quy-chu-kd .date-now').html(`${numberFormat(fundDay)} VNĐ`)
            $('.quy-chu-kd .date-week').html(`${numberFormat(funWeek)} VNĐ`)
            $('.quy-chu-kd .date-all').html(`${numberFormat(funAll)} VNĐ`)
            $('.quy-chu-kd .date-yesterday').html(`${numberFormat(funYesterday)} VNĐ`)
            $('.quy-chu-kd .date-month').html(`${numberFormat(funMonth)} VNĐ`)

            let html = ``
            recharges.forEach((item, index) => {
                html += `
                <tr>
                    <td>${index + 1}</td>
                    <td><a href="/admin/chinh-sua-yeu-cau/${item.userId}.html" style="color:blue">${item.fullName}</a></td>
                    <td>${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    <td>${numberFormat(item.count)} VNĐ</td>
                </tr>
                `
            });

            if (html == '') {
                $('.table-box-3 .will_pay').show()
                $('.table-box-3 .payed_container').hide();
                $('.table-3').remove();
            } else {
                $('.table-box-3 .will_pay').hide()
                $('.table-box-3 .payed_container').show()
            }
            $('.table-3 tbody').html(html)
        } else {
            displayError(response.message);
        }
    })
})
