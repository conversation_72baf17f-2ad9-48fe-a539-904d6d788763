$(() => {
    // Initialize CKEditor for all textareas
    var editorTrungTam = CKEDITOR.replace('trungTamHoTro', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 500
    });

    var editorChinhSach = CKEDITOR.replace('chinhSachBaoMat', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 500
    });

    var editorDieuKhoan = CKEDITOR.replace('dieuKhoanDichVu', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 500
    });

    var editorGioiThieu = CKEDITOR.replace('gioiThieu', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 500
    });
});

function savePost() {
    post('/admin/ho-tro.html', {
        trungTamHoTro: CKEDITOR.instances.trungTamHoTro.getData(),
        chinhSachBaoMat: CKEDITOR.instances.chinhSachBaoMat.getData(),
        dieuKhoanDichVu: CKEDITOR.instances.dieuKhoanDichVu.getData(),
        gioiThieu: CKEDITOR.instances.gioiThieu.getData(),
    }, function (response) {
        if (response.error) {
            displayError(response.message);
        } else {
            displaySuccess("Cập nhật thành công");
            setTimeout(() => {
                location.reload()
            }, 500)
        }
    }, function () {

    });
}
