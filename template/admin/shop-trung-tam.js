let limit = 300;
let pageCH = 1, totalPageCH = 1;
let pagePK = 1, totalPagePK = 1;
let pageKS = 1, totalPageKS = 1;
let pageSPA = 1, totalPageSPA = 1;
let pageShowroom = 1, totalPageShowroom = 1;
let pageGas = 1, totalPageGas = 1;
let indexTable = 1
let dataIndex = {
    1:[],
    2:[],
    3:[],
    4:[],
    5:[],
    6:[],
}

function showData(services, total, currentPage, parent, typeServices) {
    let html = '';
    let totalPages = Math.ceil(total / limit);
    let start = Number(currentPage - 1) * limit + 1 + 0;
    let end = Number(currentPage - 1) * limit + 1 + services.length;
    services.forEach((service, index) => {
        service.codeMP = typeServices == '0' ? createCuaHang(service.code) : typeServices == '1' ? createPhongKham(service.code) : typeServices == '2' ? createKhachSan(service.code) : createSpa(service.code)
        let htmlService = ''
        if (service.status==1){
            htmlService=`<a href="javascript:;" class="hide-service" title="Ẩn">
            <img src="/template/ui/img/hide.svg" alt="">
        </a>`
        } else {
            htmlService=`<a href="javascript:;" class="show-service" title="Hiện thị">
            <img src="/template/ui/img/check.svg" alt="">
            </a>`
        }
        if (service.canDelete){
            htmlService+=`<a href="javascript:;" class="delete-all-service" title="Xóa">
            <img src="/template/ui/img/delete-2-bage.svg" alt="">
            </a>`
        }
        html += `
            <tr id="${service._id}">
                <td>${Number(currentPage - 1) * limit + 1 + index}</td>
                <td>${service.codeMP}</td>                
                <td>${
                        service.isFeatured ? `<i class="fa fa-star main-color badge-featured mr-2" aria-hidden="true" title="Cửa hàng nổi bật" data-store-id="${service._id}" data-store-name="${service.name}" data-store-featured="${service.isFeatured}"></i>`: 
                            `<i class="fa fa-star fa-star-o main-color badge-featured mr-2" aria-hidden="true" title="Cửa hàng không nổi bật" data-store-id="${service._id}" data-store-name="${service.name}" data-store-featured="${service.isFeatured}"></i>`
                    }${service.name}
                </td>
                <td>${service.address}</td>            
                <td>${service.status == 0 ? 'Ẩn' : 'Hiển thị'}</td>
                <td>
                    <a href="/admin/chinh-sua-thuong-hieu/${service._id}.html" class="edit-service" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    ${htmlService}
                </td>
            </tr>
            `
    })
    $(parent + ' .pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${Number(total)} kết quả`)
    $(parent + ' .table_portfolio .tbody-table').html(html);


    let min = 1;
    let max = 1
    let html2 = '';
    $(parent + " .pagination_container ul").html('');
    html2 += `<li typeServices="${typeServices}" class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html2 += `<li typeServices="${typeServices}" class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html2 += `<li typeServices="${typeServices}" class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html2 += `<li typeServices="${typeServices}" class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html2 += `<li typeServices="${typeServices}" class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html2 += `<li typeServices="${typeServices}" class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html2 += `<li typeServices="${typeServices}" class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(parent + ' .pagination_container .pagination-list').html(html2);
}

$(() => {
    $(document).on('click', 'body .badge-featured', function (e) {
        e.preventDefault();
        const { storeId, storeName, storeFeatured } = e.target.dataset;
        const ask = confirm(`Bạn có chắc chắn đặt ${storeName} thành ${storeFeatured == 'true' ? '"Không nổi bật"': '"Nổi bật"'} không?`);
        if (ask) {
            post('/rest/v1/store/toggle-featured', {storeId}, (res) => {
                if (res && !res.error) {
                    if (storeFeatured == 'true') {
                        e.target.setAttribute('data-store-featured', 'false');
                    } else {
                        e.target.setAttribute('data-store-featured', 'true');
                    }
                    if (e.target.classList.contains('fa-star')) {
                        e.target.classList.toggle('fa-star-o');
                    } else {
                        e.target.classList.toggle('fa-star');
                    }
                }
            }, (error) => {
                console.log(error);
            })
        }
    });
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()

        let typeServices = $(this).attr('typeServices');
        switch (Number(typeServices)) {
            case 0:
                if (pageCH > 1) {
                    pageCH = pageCH - 1;
                    getCuahang();
                }
                break;
            case 1:
                if (pagePK > 1) {
                    pagePK = pagePK - 1;
                    getPhongKham();
                }
                break;
            case 2:
                if (pageKS > 1) {
                    pageKS = pageKS - 1;
                    getKhachSan();
                }
                break;
            case 3:
                if (pageSPA > 1) {
                    pageSPA = pageSPA - 1;
                    getSpa();
                }
                break;
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        let typeServices = $(this).attr('typeServices');
        switch (Number(typeServices)) {
            case 0:
                pageCH = 1;
                getCuahang();
                break;
            case 1:
                pagePK = 1;
                getPhongKham();
                break;
            case 2:
                pageKS = 1;
                getKhachSan();
                break;
            case 3:
                pageSPA = 1;
                getSpa();
                break;
            case 4:
                pageShowroom = 1;
                getShowroom();
                break;
            case 5:
                pageGas = 1;
                getGas();
                break;
        }
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault();
        let typeServices = $(this).attr('typeServices');
        switch (Number(typeServices)) {
            case 0:
                pageCH = totalPageCH;
                getCuahang();
                break;
            case 1:
                pagePK = totalPagePK;
                getPhongKham();
                break;
            case 2:
                pageKS = totalPageKS;
                getKhachSan();
                break;
            case 3:
                pageSPA = totalPageSPA;
                getSpa();
                break;
            case 4:
                pageShowroom = totalPageShowroom;
                getShowroom();
                break;
            case 5:
                pageGas = totalPageGas;
                getGas();
                break;
        }
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        let typeServices = $(this).attr('typeServices');
        console.log('typeServices', typeServices)

        switch (Number(typeServices)) {
            case 0:
                if (Number(pageCH) < Number(totalPageCH)) {
                    pageCH = pageCH + 1;
                    getCuahang();
                }
                break;
            case 1:
                if (Number(pagePK) < Number(totalPagePK)) {
                    pagePK = pagePK + 1;
                    getPhongKham();
                }
                break;
            case 2:
                if (Number(pageKS) < Number(totalPageKS)) {
                    pageKS = pageKS + 1;
                    getKhachSan();
                }
                break;
            case 3:
                if (Number(pageSPA) < Number(totalPageSPA)) {
                    pageSPA = pageSPA + 1;
                    getSpa();
                }
                break;
            case 4:
                if (Number(pageShowroom) < Number(totalPageShowroom)) {
                    pageShowroom = pageShowroom + 1;
                    getShowroom();
                }
                break;
            case 5:
                if (Number(pageGas) < Number(totalPageGas)) {
                    pageGas = pageGas + 1;
                    getGas();
                }
                break;
        }
    });

    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let typeServices = $(this).attr('typeServices');
        console.log('typeServices', typeServices)
        switch (Number(typeServices)) {
            case 0:
                pageCH = Number(e.currentTarget.value);
                getCuahang();
                break;
            case 1:
                pagePK = Number(e.currentTarget.value);
                getPhongKham();
                break;
            case 2:
                pageKS = Number(e.currentTarget.value);
                getKhachSan();
                break;
            case 3:
                pageSPA = Number(e.currentTarget.value);
                getSpa();
                break;
            case 4:
                pageShowroom = Number(e.currentTarget.value);
                getShowroom();
                break;
            case 5:
                pageGas = Number(e.currentTarget.value);
                getGas();
                break;
        }
    });

    $(document).on('click', '.hide-service', function (e) {
        let conf = confirm('Bạn có chắc muốn ẩn?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/hide-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.delete-all-service', function (e) {
        let conf = confirm('Bạn có chắc muốn xóa?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/delete-all-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.show-service', function (e) {
        let conf = confirm('Bạn có chắc muốn hiển thị?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/show-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
})

function filterList(typeServices) {
    console.log('typeServices', typeServices)
    debugger
    switch (Number(typeServices)) {
        case 0:
            pageCH = 1;
            totalPageCH = 1;
            getCuahang();
            break;
        case 1:
            pagePK = 1;
            totalPagePK = 1;
            getPhongKham();
            break;
        case 2:
            pageKS = 1;
            totalPageKS = 1;
            getKhachSan();
            break;
        case 3:
            pageSPA = 1;
            totalPageSPA = 1;
            getSpa();
            break;
        case 4:
            pageShowroom = 1;
            totalPageShowroom = 1;
            getShowroom();
            break;
        case 5:
            pageGas = 1;
            totalPageGas = 1;
            getGas();
            break;
    }
}


function getShowroom() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.showroom .search-goods').val(),
        // typePet: $('.showroom .type-pet').val(),
        status: $('.showroom .status').val(),
        // isFeatured: $('.showroom .featured').val(),
        page: pageCH,
        typeServices: 4,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[5] = data.data.services
            totalPageShowroom = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.showroom', 4);
        }
    })
}

function getGas() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.gas .search-goods').val(),
        // typePet: $('.gas .type-pet').val(),
        status: $('.gas .status').val(),
        // isFeatured: $('.gas .featured').val(),
        page: pageCH,
        typeServices: 5,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[6] = data.data.services
            totalPageGas = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.gas', 5);
        }
    })
}

function getSpa() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.spa .search-goods').val(),
        typePet: $('.spa .type-pet').val(),
        status: $('.spa .status').val(),
        isFeatured: $('.spa .featured').val(),
        page: pageCH,
        typeServices: 3,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[4] = data.data.services
            totalPageSPA = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.spa', 3);
        }
    })
}

function getKhachSan() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.khach-san .search-goods').val(),
        typePet: $('.khach-san .type-pet').val(),
        status: $('.khach-san .status').val(),
        isFeatured: $('.khach-san .featured').val(),
        page: pageCH,
        typeServices: 2,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[3] = data.data.services
            totalPageKS = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.khach-san', 2);
        }
    })
}

function getPhongKham() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.phong-kham .search-goods').val(),
        typePet: $('.phong-kham .type-pet').val(),
        status: $('.phong-kham .status').val(),
        isFeatured: $('.phong-kham .featured').val(),
        page: pageCH,
        typeServices: 1,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[2] = data.data.services
            totalPagePK = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.phong-kham', 1);
        }
    })
}

function getCuahang() {
    post('/admin/tai-danh-sach-cua-hang.html', {
        searchKey: $('.cua-hang .search-goods').val(),
        typePet: $('.cua-hang .type-pet').val(),
        status: $('.cua-hang .status').val(),
        isFeatured: $('.cua-hang .featured').val(),
        page: pageCH,
        typeServices: 0,
        limit: limit, managerId
    }, (data) => {
        if (!data.error) {
            dataIndex[1] = data.data.services
            totalPageCH = Math.ceil(data.data.total / limit);
            showData(data.data.services, Number(data.data.total), Number(data.data.page), '.cua-hang', 0);
        }
    })
}

$(function () {
    getCuahang();
    getPhongKham();
    getKhachSan();
    getSpa();
    getShowroom();
    getGas();

    $(document).on('click', '.menu_tab li', function (e) {
        indexTable = Number($(this).attr('index'))
    });

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-shop-trung-tam.html`, {
            services: dataIndex[indexTable],
            indexTable:indexTable,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });
})
