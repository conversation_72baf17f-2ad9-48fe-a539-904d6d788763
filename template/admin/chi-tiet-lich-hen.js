function successSchedule(element) {
    let conf = confirm('Bạn có chắc muốn đồng ý lịch hẹn này?');
    if (conf) {
        post(`${$(element).attr('url')}`, {}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

function successDoneScheduleViewBox(element) {
    $('#successDoneScheduleViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

function cancelScheduleViewBox() {
    $('#cancelScheduleViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

function guiLenhTuChoi() {
    let message = $('#lyDoTuChoi').val().trim();
    if (message == '') {
        alert("<PERSON>ui lòng gửi lý do");
    } else {
        if (!confirm("Bạn chắc chắn muốn từ chối lịch hẹn này? Sau khi từ chối thì bạn sẽ không thể hoàn tác thao tác này")) return;
        post(`/store/change-status-schedule/${dataBook._id}.html?status=2&index=${dataBook.type}`, {message}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

function xacNhanHoanThanh() {
    let gia = $('#gia').val().trim().replace("VND", '').split(",").join("").trim();
    if (gia == '' || isNaN(Number(gia))) {
        alert("Vui lòng nhập chi phí");
    } else {
        if (!confirm("Bạn chắc chắn đã hoàn thành lịch hẹn này")) return;
        post(`/store/change-status-schedule-done/${dataBook._id}.html?status=3&index=${dataBook.type}`, {price: Number(gia)}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

$(function () {
    $('#gia').on('keydown', function (e) {
        if (e.which == 13) {
            e.preventDefault();
            xacNhanHoanThanh();
        }
    });
});