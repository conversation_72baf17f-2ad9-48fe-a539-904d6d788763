function addClassify() {
    let count = document.querySelectorAll('.classify-item').length
    $('#classify-list').append(`
        <div class="col-md-12 classify-item" id="classify-item-${count + 1}">
            <div class="input_field schedule_field">
                <label for="" class="title-classify">Nhóm ${count + 1}</label>
                    <div class="row type-product">
                    <label for="">Tên nhóm</label>
                    <div class="col-md-12">
                        <div class="input_field schedule_field">
                            <input placeholder="Tên nhóm" name="name-type" type="text">
                        </div>
                    </div>
                    <label for="">Nhóm con</label>
                    <div style="width: 100%;" class="type-list">
                        <div class="col-md-12 type-item">
                            <div class="input_field schedule_field">
                                <input name="nameProd" placeholder="Nhập phân loại hàng" type="text" class="mr-3" value="">
                                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency" value="">
                                <a href="javascript:;" class="delete-service" title="Xoá" onclick="deleteTypeItem(this)">
                                    <img src="/template/ui/img/delete-2-bage.svg" alt="">
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input_field schedule_field">
                            <a class="button-classify" style="background: white;border: solid 2px #8db7ff; color: #8db7ff;" onclick="addType(this)"><img src="/template/ui/img/plus-nhat.svg" alt=""> Thêm</a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input_field schedule_field">
                            <a class="button-classify" style="background: white;border: solid 2px #FF1615; color: #FF1615;" onclick="deleteClassify(this)"><img src="/template/ui/img/delete-2-bage-red.svg" alt=""> Xóa</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `)
}

function deleteTypeItem(element) {
    $(element).parents('.type-item').remove()
}

function addType(element) {
    $(element).parents('.classify-item').find('.type-list').append(`                        
        <div class="col-md-12 type-item">
            <div class="input_field schedule_field">
                <input name="nameProd" placeholder="Nhập phân loại dịch vụ" type="text" class="mr-3">
                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency">
                <a href="javascript:;" class="delete-service" title="Xoá" onclick="deleteTypeItem(this)">
                    <img src="/template/ui/img/delete-2-bage.svg" alt="">
                </a>
            </div>
        </div>
        `)
}

function deleteClassify(element) {
    $(element).parents('.classify-item').remove()
    document.querySelectorAll('.classify-item').forEach((item, index) => {
        item.querySelector('.title-classify').innerHTML = `Nhóm ${index + 1}`
    })
}

$(() => {
    var editor = CKEDITOR.replace('description', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 300
    });


    loadBranch(storeId)

    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })


    $('.delete-image').on('click', function () {
        let idInput = $(this).parents('.upfile').find('input').attr('id')
        let element = $(this).parent()
        setTimeout(() => {
            element.attr('for', idInput)
        }, 500)
        element.find('img').attr('src', '/template/ui/img/upload.png')
        $(this).remove()
    })

    $('#form-add-product').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-product').serializeArray();
        let formData = new FormData();
        data.forEach(item => {
            if (item.name == 'price') {
                item.value = item.value.replace("VND", '').split(",").join("").trim();
            }
            if (item.name != 'description') {
                formData.append(item.name, item.value);
            }
        })

        let classify = []
        document.querySelectorAll('.classify-item').forEach(item => {
            if (item.querySelector('input[name="name-type"]').value.trim() != '') {
                let newClassify = {
                    name: item.querySelector('input[name="name-type"]').value.trim(),
                    data: []
                }
                item.querySelectorAll('.type-item').forEach(item2 => {
                    const classifyData = {};
                    if (item2.querySelector('input[name="nameProd"]')) {
                        classifyData.name = item2.querySelector('input[name="nameProd"]').value.trim();
                    }
                    if (item2.querySelector('input[name="sellingPrice"]')) {
                        classifyData.price = item2.querySelector('input[name="sellingPrice"]').value.trim();
                    }
                    newClassify.data.push(classifyData)
                })
                classify.push(newClassify)
            }
        })
        formData.append(`classify`, JSON.stringify(classify))
        formData.append('description', editor.getData());
        formData.append('pictureOlds', JSON.stringify(pictureOlds));
        formData.append(`thumbail`, $(`#upload-product-0`).prop('files')[0])
        ajaxFile(`/admin/edit-room/${productId}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    window.location.href = "/admin/mng-parking.html"
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })

    /**
     * Chọn cửa hàng thì lấy tất cả cơ sở cửa hàng đó
     */
    $('select[name="branchId"]').select2({
        multiple: true,
        // allowClear: true,
        theme: "bootstrap4",
        width: 'resolve',
        placeholder: '[[---------Chọn cơ sở --------]]',
        data: []
    });

    $('select[name="storeId"]').on('change', function (event) {
        const selectedStoreId = event.target.value;
        loadBranch(selectedStoreId)
    })
    function loadBranch(id) {
        $.ajax({
            url: `/rest/v1/branches-by-admin?storeId=${id}`,
            data: {},
            type: 'get',
            success: function (response) {
                let dataIdArr = []
                response.branches.forEach(branch => {
                    if (branchId.includes(branch._id))
                    {
                        dataIdArr.push(branch._id)
                    }
                })

                $('select[name="branchId"]').select2({
                    multiple: true,
                    allowClear: true,
                    theme: "bootstrap4",
                    width: 'resolve',
                    placeholder: '[[---------Chọn cơ sở --------]]',
                    data: response.branches.map(branch => ({id: branch._id, text: branch.name})),
                }).val(dataIdArr).trigger('change')
            },
            error: function (err) {

            }
        })
    }
})
