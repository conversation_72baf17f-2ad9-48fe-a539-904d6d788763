let page = 1;
let totalPage = 1;

let page2 = 1;
let totalPage2 = 1;

let itemsPerPage = 30;
let users = [];
let histories = [];

function renderTable() {
    let html = '';
    users.forEach((user, i) => {
        html += `
      <tr id="${user._id}">
          <td>${(page - 1) * itemsPerPage + i + 1}</td>
          <td>${user.fullName}</td>
          <td>${user.email}</td>
          <td>${user.phone || ''}</td>
          <td>${user.address}</td>
          <td>${numberFormat(user.wallet)} VNĐ</td>`;

        html += `<td>
            <a href="javascript:;" class="thanh-toan-tai-khoan" userId="${user._id}" amount="${user.wallet}"  title="Thanh toán">
              <img src="/template/ui/img/check.svg" alt="">
            </a>
          </td>
      </tr>`
    });
    $(`.pagination p`).text(`Hiển thị trang ${page} trong tổng số ${totalPage} trang`);
    $(`.table .tbody-table`).html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1;
    let html = '';
    $(`.pagination ul`).html('');
    html += `<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPage) {
        min = 1;
        max = totalPage
    } else if (Number(page) + 2 > Number(totalPage)) {
        max = totalPage;
        min = totalPage - 4
    } else if (Number(page) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(page) - 2;
        max = Number(page) + 2
    }
    if (min == 2) {
        html += `<li class="page ${1 == Number(page) ? 'active' : ''}" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(page) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPage - 2) {
        html += `<li class="page ${totalPage == Number(page) ? 'active' : ''}" value="${totalPage}"><a href="javascript:;">${totalPage}</a></li>`
    }
    html += `<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(`.pagination .pagination-list`).html(html);
}


function taiDanhSachStoreCoWallet() {
    let keySearch = $('#search-goods').val().trim();
    get(`/admin/ds-store-co-quy-online.html?search=${keySearch}&page=${page}&itemsPerPage=${itemsPerPage}`, {}, res => {
        if (!res.error) {
            totalPage = res.data.totalPage;
            users = res.data.users;
            renderTable()
        }
    }, () => {
        displayError('Lỗi kết nối');
    })
}

function lichSuTraTienChoCuaHang() {
    let keySearch = $('#search-goods-2').val().trim();
    let timeStart = $('#time-start').val().trim();
    let timeEnd = $('#time-end').val().trim();

    try {
        let timeStartArr = timeStart.split(',');
        timeStart = `${timeStartArr[0].trim()} ${timeStartArr[1].trim().split("/")[1]}/${timeStartArr[1].trim().split("/")[0]}/${timeStartArr[1].trim().split("/")[2]}`
        let timeEndArr = timeEnd.split(',');
        timeEnd = `${timeEndArr[0].trim()} ${timeEndArr[1].trim().split("/")[1]}/${timeEndArr[1].trim().split("/")[0]}/${timeEndArr[1].trim().split("/")[2]}`
    } catch (e) {
        timeStart = '';
        timeEnd = '';
    }

    get(`/admin/lich-su-thanh-toan-online.html?search=${keySearch}&page=${page2}&itemsPerPage=${itemsPerPage}&timeStart=${timeStart}&timeEnd=${timeEnd}`, {}, res => {
        if (!res.error) {
            totalPage2 = res.data.totalPage;
            histories = res.data.histories;
            renderTable2()
        }
    }, () => {
        displayError('Lỗi kết nối');
    })
}

function taiViHeThong() {
    get(`/admin/tai-vi-he-thong.html`, {}, res => {
        if (!res.error) {
            let wallet = res.data.wallet;
            $('.dl1').text(`${numberFormat(wallet.total)} VNĐ`);
            $('.dl2').text(`${numberFormat(wallet.storeValue)} VNĐ`);
            $('.dl3').text(`${numberFormat(wallet.total - wallet.storeValue)} VNĐ`);
        }
    }, () => {

    })
}

$(function () {
    $('.dateTimeHandle').datetimepicker({
        i18n: {
            de: {
                months: [
                    'Tha.1', 'Tha.2', 'Tha.3', 'Tha.4',
                    'Tha.5', 'Tha.6', 'Tha.7', 'Tha.8',
                    'Tha.9', 'Tha.10', 'Tha.11', 'Tha.12',
                ],
                dayOfWeek: [
                    "CN", "Thu.2", "Thu.3", "Thu.4",
                    "Thu.5", "Thu.6", "Thu.7",
                ]
            }
        },
        format: 'H:i, d/m/Y',
        scrollMonth: false,
        scrollInput: false,
        scrollTime: false

    });

    taiDanhSachStoreCoWallet();
    taiViHeThong();
    lichSuTraTienChoCuaHang();

    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (page > 1) {
            page = page - 1;
            taiDanhSachStoreCoWallet();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        page = 1;
        taiDanhSachStoreCoWallet();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        page = totalPage;
        taiDanhSachStoreCoWallet();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault();
        if (Number(page) < Number(totalPage)) {
            page = page + 1;
            taiDanhSachStoreCoWallet();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        page = e.currentTarget.value;
        taiDanhSachStoreCoWallet();
    });

    $(document).on('click', '.thanh-toan-tai-khoan', async function (e) {
        e.preventDefault();
        let userId = $(this).attr('userId');
        let amount = $(this).attr('amount');
        const {value: amountPay} = await Swal.fire({
            title: `Nhập số tiền muốn thanh toán`,
            html: `<span style="color: red;">Tối đa: ${numberFormat(amount)} VNĐ</span>`,
            input: 'number',
            inputPlaceholder: 'Nhập số tiền muốn thanh toán',
            confirmButtonText: 'Thanh toán'
        });

        if (amountPay) {
            if (Number(amountPay) > 0) {
                if (!confirm(`Bạn chắc chắn muốn thanh toán ${numberFormat(amountPay)} VNĐ cho tài khoản này`)) return;
                post(`/admin/thanh-toan-wallet-online.html`, {userId, amount: amountPay}, res => {
                    if (res.error) {
                        displayError(res.message);
                    } else {
                        taiDanhSachStoreCoWallet();
                        taiViHeThong();
                        lichSuTraTienChoCuaHang();
                        displaySuccess('Thanh toán thành công');
                    }
                }, () => {
                    displayError('Lỗi kết nối');
                })
            }
        }
    });

    $(document).on('click', '.xoa-lich-su', async function (e) {
        e.preventDefault();
        let historyId = $(this).attr('historyId');

        if (!confirm(`Bạn chắc chắn muốn lịch sử thanh toán này? Lưu ý: chỉ xoá lịch sử, chứ không hoàn lại tiền`)) return;
        post(`/admin/xoa-lich-su-thanh-toan-online.html`, {historyId}, res => {
            if (res.error) {
                displayError(res.message);
            } else {
                lichSuTraTienChoCuaHang();
                displaySuccess('Thanh toán thành công');
            }
        }, () => {
            displayError('Lỗi kết nối');
        })
    });
});


function renderTable2() {
    let html = '';
    histories.forEach((history, i) => {
        html += `
      <tr id="${history._id}">
          <td>${(page2 - 1) * itemsPerPage + i + 1}</td>
          <td>${history.fullName}</td>
          <td>${history.email}</td>
          <td>${history.phone || ''}</td>
          <td>${history.address}</td>
          <td>${numberFormat(history.amount)} VNĐ</td>
          <td>${moment(history.createAt).format('HH:mm DD/MM/YYYY')}</td>`;

        html += `<td>
            <a href="javascript:;" class="xoa-lich-su" historyId="${history._id}" title="Xoá lịch sử">
              <img src="/template/ui/img/delete.svg" alt="">
            </a>
          </td>
      </tr>`
    });
    $(`.pagination-2 p`).text(`Hiển thị trang ${page} trong tổng số ${totalPage} trang`);
    $(`.table-2 .tbody-table`).html(html);
    renderPagination2()
}

function renderPagination2() {
    let min = 1;
    let max = 1;
    let html = '';
    $(`.pagination-2 ul`).html('');
    html += `<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPage2) {
        min = 1;
        max = totalPage2
    } else if (Number(page) + 2 > Number(totalPage2)) {
        max = totalPage2;
        min = totalPage2 - 4
    } else if (Number(page2) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(page2) - 2;
        max = Number(page2) + 2
    }
    if (min == 2) {
        html += `<li class="page ${1 == Number(page2) ? 'active' : ''}" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(page2) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPage2 - 2) {
        html += `<li class="page ${totalPage2 == Number(page2) ? 'active' : ''}" value="${totalPage2}"><a href="javascript:;">${totalPage2}</a></li>`
    }
    html += `<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(`.pagination-2 .pagination-list`).html(html);
}
