$(() => {

    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    // Load danh sách sản phẩm cho dropdown
    function loadProductDropdown() {
        get('/admin/lay-danh-sach-san-pham-dropdown.html', {}, (response) => {
            if (!response.error && response.data && response.data.products) {
                let html = '<option value="">-- <PERSON><PERSON><PERSON> sản phẩm --</option>';
                response.data.products.forEach(product => {
                    html += `<option value="${product._id}">${product.name} (${product.code || 'N/A'})</option>`;
                });
                $('#product-select').html(html);
            } else {
                console.error('Error loading products:', response.message);
            }
        });
    }

    // Xử lý thay đổi screen type
    function handleScreenTypeChange() {
        const screenType = $('#screen').val();
        const productContainer = $('#product-dropdown-container');
        const paramsInput = $('#params');

        if (screenType === 'PRODUCT_DETAILS') {
            productContainer.show();
            paramsInput.parent().parent().hide(); // Ẩn input params thông thường
            loadProductDropdown();
        } else {
            productContainer.hide();
            paramsInput.parent().parent().show(); // Hiện input params thông thường
        }
    }

    // Xử lý khi chọn sản phẩm từ dropdown
    $('#product-select').on('change', function() {
        const selectedProductId = $(this).val();
        $('#params').val(selectedProductId); // Cập nhật params với product ID
    });

    // Bind event cho screen dropdown
    $('#screen').on('change', handleScreenTypeChange);

    // Khởi tạo trạng thái ban đầu
    handleScreenTypeChange();

    $('#form-add-news').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-news').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        formData.append(`thumbail`, $(`#upload-news-0`).prop('files')[0])
        ajaxFile('/admin/them-banner-app.html', formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/quan-ly-banner-app.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
