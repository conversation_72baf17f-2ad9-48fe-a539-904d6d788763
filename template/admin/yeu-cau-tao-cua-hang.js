var userShows = {
    1: [],
    2: [],
    3: [],
    4: [],
    5: [],
}
let indexTable = 1
users.forEach(item => {
    if (item.fee == 0 && item.statusStore == 1 && item.status == 1) {
        userShows[1].push(item)
    }

    if (item.fee == 1 && item.statusStore == 1 && item.status == 1) {
        userShows[2].push(item)
    }

    if (item.statusStore == 0 && item.status == 1) {
        userShows[3].push(item)
    }

    if (item.statusStore == 2 && item.status == 1) {
        userShows[4].push(item)
    }

    if (item.status == 2) {
        userShows[5].push(item)
    }
})
let itemsPerPage = 15;

let currentPage = {
    1: 1,
    2: 1,
    3: 1,
    4: 1,
    5: 1,
};

let totalPages = {
    1: Math.ceil(userShows[1].length / itemsPerPage),
    2: Math.ceil(userShows[2].length / itemsPerPage),
    3: Math.ceil(userShows[3].length / itemsPerPage),
    4: Math.ceil(userShows[4].length / itemsPerPage),
    5: Math.ceil(userShows[5].length / itemsPerPage),
}

function getDoanhThuUser(userId) {
    get(`/admin/lay-thong-tin-doanh-thu/${userId}.html`, {}, (response) => {
        if (!response.error) {
            userShows[1].forEach(item => {
                if (item._id == userId) {
                    item.doanhThu = response.data.doanhThu
                    $(`#${userId} .doanh-thu`).html(`${numberFormat(item.doanhThu)} VNĐ`)
                }
            })
        } else {
            displayError(response.message);
        }
    })
}

function renderTable(index) {
    let html = '';
    let start = (currentPage[index] - 1) * itemsPerPage;
    let end = currentPage[index] * itemsPerPage
    if (end > userShows[index].length - 1) {
        end = userShows[index].length
    }
    for (let i = start; i <= end; i++) {
        if (userShows[index][i]) {
            let user = userShows[index][i]
            let htmlChuatinhPhi = ''
            if (index == 1) {
                htmlChuatinhPhi = `<td class="doanh-thu">${user.doanhThu ? numberFormat(user.doanhThu) + 'VNĐ' : ''}</td>`
                if (!user.doanhThu) {
                    getDoanhThuUser(user._id)
                }
            }
            if (user.statusStore == 0) {
                buttonHtml = `
                <a href="javascript:;" class="success-user" title="Xác nhận thành công">
                    <img src="/template/ui/img/check.svg" alt="">
                </a>
                <a href="javascript:;" class="cancel-user"title="Xác nhận thất bại">
                    <img src="/template/ui/img/close.svg" alt="">
                </a> `
            } else if (user.statusStore == 1) {
                buttonHtml = `
                <a href="javascript:;" class="wait-user" title="Chuyển sang chờ duyệt">
                    <img src="/template/ui/img/tailai.svg" alt="">
                </a>
                <a href="javascript:;" class="cancel-user" title="Xác nhận thất bại">
                    <img src="/template/ui/img/close.svg" alt="">
                </a> `
            } else {
                buttonHtml = `
                <a href="javascript:;" class="wait-user" title="Chuyển sang chờ duyệt">
                    <img src="/template/ui/img/tailai.svg" alt="">
                </a>
                <a href="javascript:;" class="success-user" title="Xác nhận thành công">
                    <img src="/template/ui/img/check.svg" alt="">
                </a> `
            }

            let htmlTimeActive = '';
            if (index == 1) {
                let currentTime = new Date().getTime() - user.createAt;
                let days = Number(currentTime / 1000 / 60 / 60 / 24).toFixed(1);
                htmlTimeActive = `<td>${days} ngày</td>`;
            }
            html += `
            <tr id="${user._id}">
                    <td>${i + 1}</td>
                    <td>${user.fullName}</td>
                    <td>${user.email}</td>
                    <td>${user.phone}</td>
                    <td>${user.address}</td>
                    <td>${moment(Number(user.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                    ${htmlTimeActive}
                    ${htmlChuatinhPhi}
                    <td>
                        ${buttonHtml}
                        <a href="/admin/chinh-sua-yeu-cau/${user._id}.html" title="Chỉnh sửa">
                            <img src="/template/ui/img/edit.svg" alt="">
                        </a>
                    </td>
            </tr>
            `
        }
    }
    $(`.pagination-${index} p`).text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${userShows[index].length} kết quả`)
    $(`.table-${index} .tbody-table`).html(html);
    renderPagination(index)
}

function renderPagination(index) {
    let min = 1;
    let max = 1
    let html = '';
    $(`.pagination-${index} ul`).html('');
    html += `<li class="first-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages[index]) {
        min = 1;
        max = totalPages[index]
    } else if (Number(currentPage[index]) + 2 > Number(totalPages[index])) {
        max = totalPages[index];
        min = totalPages[index] - 4
    } else if (Number(currentPage[index]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage[index]) - 2;
        max = Number(currentPage[index]) + 2
    }
    if (min == 2) {
        html += `<li class="page" index="${index}" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage[index]) ? 'active' : ''}" index="${index}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[index] - 2) {
        html += `<li class="page ${totalPages[index] == Number(currentPage[index]) ? 'active' : ''}" index="${index}"  value="${totalPages[index]}"><a href="javascript:;" >${totalPages[index]}</a></li>`
    }
    html += `<li class="next-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(`.pagination-${index} .pagination-list`).html(html);
}

function filterList(index) {
    index = Number(index);
    currentPage[index] = 1;
    userShows[index] = [];
    let data0 = [];

    users.forEach(item => {
        if (index == 1 && item.fee == 0 && item.statusStore == 1 && item.status == 1) {
            data0.push(item)
        } else if (index == 2 && item.fee == 1 && item.statusStore == 1 && item.status == 1) {
            data0.push(item)
        } else if (index == 3 && item.statusStore == 0 && item.status == 1) {
            data0.push(item)
        } else if (index == 4 && item.statusStore == 2 && item.status == 1) {
            data0.push(item)
        } else if (index == 5 && item.status == 2) {
            data0.push(item)
        }
    });

    let key = removeUtf8($(`#search-goods-${index}`).val().trim());
    let data2 = []
    data0.forEach(item => {
        if (removeUtf8(item.fullName).includes(key)
            || removeUtf8(item.email.toString()).includes(key)
            || removeUtf8((item.phone).toString()).includes(key)
            || removeUtf8(item.address.toString()).includes(key)) {
            data2.push(item)
        }
    });

    userShows[index] = data2;
    totalPages[index] = Math.ceil(userShows[index].length / itemsPerPage);
    renderTable(index)
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (currentPage[index] > 1) {
            currentPage[index] = currentPage[index] - 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = 1;
        renderTable(index);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = totalPages[index];
        renderTable(index);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (Number(currentPage[index]) < Number(totalPages[index])) {
            currentPage[index] = currentPage[index] + 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let index = $(this).attr('index')
        let page = e.currentTarget.value;
        currentPage[index] = page;
        renderTable(index);
    });

    $(document).on('click', '.wait-user', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái ẩn?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-store-status-user/${id}.html?status=0`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.success-user', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái thành công?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-store-status-user/${id}.html?status=1`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    $(document).on('click', '.cancel-user', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái thất bại?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/change-store-status-user/${id}.html?status=2`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.menu_tab li', function (e) {
        indexTable = Number($(this).attr('index'))
    });

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-nguoi-kinh-doanh.html`, {
            users: userShows[indexTable],
            indexTable:indexTable==1? 1 :2,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

    renderTable(1);
    renderTable(2);
    renderTable(3);
    renderTable(4);
    renderTable(5);
});

