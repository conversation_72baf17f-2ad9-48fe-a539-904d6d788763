let productShows = {
    1: [],
    0: [],
    3: [],
};
let currentPage = {
    1: 1,
    0: 1,
    3: 1
};
let itemsPerPage = 10;
let indexTable = 1
let categoryObject = {
    1: 'all',
    0: 'all',
    3: 'all',
};
let statusTableObject = {
    1: 'all',
    0: 'all',
    3: 'all'
};
let countProductObject = {
    1: 0,
    0: 0,
    3: 0
};
let keySearch = {
    1: '',
    0: '',
    3: ''
};
let totalPages = {1: 1, 0: 1, 3: 1};

function matchStart(params, data) {
    if ($.trim(params.term) === '') {
        return data;
    }

    if (removeUtf8(data.text).includes(removeUtf8(params.term))){
        return data
    }
    return null;
}

function getDataByIndex(index) {
    indexTable=index
    post(`/admin/lay-danh-sach-dich-vu.html?status=${index}&page=${currentPage[index]}&search=${keySearch[index]}&shopId=${shopId}`, {
        // categoryId: categoryObject[index],
        typeService: statusTableObject[index]
    }, (response) => {
        if (response.error) {
            location.reload()
        } else {
            productShows[index] = response.data.products;
            totalPages[index] = Math.ceil(response.data.countProduct / itemsPerPage);
            countProductObject[index] = response.data.countProduct;
            renderTable(index)
        }
    })
}

function renderTable(index) {
    let html = '';
    let start = (currentPage[index] - 1) * itemsPerPage;
    let end = currentPage[index] * itemsPerPage
    if (end > countProductObject[index] - 1) {
        end = countProductObject[index]
    }
    for (let i = 0; i <= productShows[index].length; i++) {
        if (productShows[index][i]) {
            let product = productShows[index][i]
            html += `
            <tr id="${product._id}">
                <td>${start + i + 1}</td>
                <td>SP${product.code}</td>
                <td>${
                product.isFeatured ? `<i class="fa fa-star main-color badge-featured mr-2" aria-hidden="true" title="nổi bật" data-service-id="${product._id}" data-service-name="${product.name}" data-service-featured="${product.isFeatured}"></i><img src="${product.thumbail}" alt="" style="max-width: 40px; padding-left: 5px; padding-right: 5px"> `:
                    `<i class="fa fa-star fa-star-o main-color badge-featured mr-2" aria-hidden="true" title="không nổi bật" data-service-id="${product._id}" data-service-name="${product.name}" data-service-featured="${product.isFeatured}"></i><img src="${product.thumbail}" alt="" style="max-width: 40px; padding-left: 5px; padding-right: 5px"> `
            }${product.name}
                </td>
                <td><div style="cursor: pointer" onclick="filterByBrand('${product.storeName}')">${product.storeName}</div></td>       
                <td>${numberFormat(product.price)} VNĐ</td>
                <td class="status"><span class="${product.typeService == 1 ? 'stocking' : 'out_stock'}">${product.typeService == 1 ? 'Sẵn sàng' : 'tạm dừng'}</span></td>
                <td>${moment(product.createAt).format('HH:mm DD/MM/YY')}</td>
                <td>
                    <a href="/admin/edit-service-of-spa/${product._id}.html" class="edit-product" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    ${(product.status == 1 || product.status == 2) ? '<a href="javascript:;" class="cancel-product" title="Ẩn"> <img src="/template/ui/img/delete.svg" alt=""> </a>' : ''}
                    ${product.status == 0 ? '<a href="javascript:;" class="success-product" title="Hiện"> <img src="/template/ui/img/check.svg" alt=""> </a>' : ''}
                    ${product.status == 3 ? '<a href="javascript:;" class="success-product" title="Duyệt"> <img src="/template/ui/img/check.svg" alt=""> </a>' : ''}
                </td>
            </tr>
            `
        }
    }
    $(`#table-product-${index} .pagination_container p`).text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${countProductObject[index]} kết quả`)
    $(`#table-product-${index} .table_portfolio .tbody-table`).html(html);
    get_image_dropbox();
    renderPagination(index)
}

function renderPagination(index) {
    let min = 1;
    let max = 1;
    let html = '';
    console.log(`renderPagination: ${index}`);
    console.log(`totalPages:`, totalPages[index]);
    console.log(`totalPages:`, totalPages);
    $(`#table-product-${index} .pagination_container ul`).html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages[index]) {
        min = 1;
        max = totalPages[index]
    } else if (Number(currentPage[index]) + 2 > Number(totalPages[index])) {
        max = totalPages[index];
        min = totalPages[index] - 4
    } else if (Number(currentPage[index]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage[index]) - 2;
        max = Number(currentPage[index]) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage[index]) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[index] - 2) {
        html += `<li class="page ${totalPages[index] == Number(currentPage[index]) ? 'active' : ''}"  value="${totalPages[index]}"><a href="javascript:;" >${totalPages[index]}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $(`#table-product-${index} .pagination_container .pagination-list`).html(html);
    $(`#table-product-${index} .pagination_container .pagination-list li`).attr('index', index)
}

function filterList(index) {
    currentPage[index] = 1;
    productShows[index] = []
    // categoryObject[index] = $(`#table-product-${index} .categoryId`).val()
    statusTableObject[index] = $(`#table-product-${index} .status-product`).val()
    keySearch[index] = $(`#table-product-${index} .search-goods`).val().trim()
    shopId[index] = $(`#table-product-${index} .select-shop`).val()
    getDataByIndex(index)
}

function filterByBrand(brand) {
    $(`#table-product-${indexTable} .search-goods`).val(brand)
    filterList(indexTable)
}


$(() => {
    $(".select-shop").select2({
        matcher: matchStart
    });

    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (currentPage[index] > 1) {
            currentPage[index] = currentPage[index] - 1;
            getDataByIndex(index);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = 1;
        getDataByIndex(index);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = totalPages[index];
        getDataByIndex(index);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (Number(currentPage[index]) < Number(totalPages[index])) {
            currentPage[index] = currentPage[index] + 1;
            getDataByIndex(index);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let index = $(this).attr('index')
        currentPage[index] = e.currentTarget.value;
        getDataByIndex(index);
    });

    $(document).on('click', '.cancel-product', function (e) {
        let conf = confirm('Bạn có chắc muốn chuyển sang trạng thái ẩn?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/change-status-service/${id}.html?status=0`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    $(document).on('click', '.success-product', function (e) {
        let conf = confirm('Bạn chắc chắn muốn hiển thị sản phẩm này?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/admin/change-status-service/${id}.html?status=1`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-san-pham.html`, {
            products: productShows[indexTable],
            indexTable:indexTable,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

    $(document).on('click', 'body .badge-featured', function (e) {
        e.preventDefault();
        const { serviceId, serviceName, serviceFeatured } = e.target.dataset;
        console.log('data on click, ', JSON.stringify(e.target.dataset))
        const ask = confirm(`Bạn có chắc chắn đặt ${serviceName} thành ${serviceFeatured == 'true' ? '"Không nổi bật"': '"Nổi bật"'} không?`);
        if (ask) {
            post('/rest/v1/service/toggle-featured', {serviceId}, (res) => {
                if (res && !res.error) {
                    if (serviceFeatured == 'true') {
                        e.target.setAttribute('data-service-featured', 'false');
                    } else {
                        e.target.setAttribute('data-service-featured', 'true');
                    }
                    if (e.target.classList.contains('fa-star')) {
                        e.target.classList.toggle('fa-star-o');
                    } else {
                        e.target.classList.toggle('fa-star');
                    }
                }
            }, (error) => {
                console.log(error);
            })
        }
    });

    getDataByIndex(1)
})

