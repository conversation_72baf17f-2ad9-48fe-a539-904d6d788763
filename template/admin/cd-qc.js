function saveAds() {
    var formData = new FormData();
    if ($(`#upload-product-0`).prop('files')[0]) {
        formData.append(`homeCode`, $(`#upload-product-0`).prop('files')[0])
    }
    if ($(`#upload-product-1`).prop('files')[0]) {
        formData.append(`shopCode1`, $(`#upload-product-1`).prop('files')[0])
    }
    if ($(`#upload-product-2`).prop('files')[0]) {
        formData.append(`shopCode2`, $(`#upload-product-2`).prop('files')[0])
    }
    if ($(`#upload-product-3`).prop('files')[0]) {
        formData.append(`banner1`, $(`#upload-product-3`).prop('files')[0])
    }
    if ($(`#upload-product-4`).prop('files')[0]) {
        formData.append(`banner2`, $(`#upload-product-4`).prop('files')[0])
    }
    if ($(`#upload-product-5`).prop('files')[0]) {
        formData.append(`banner3`, $(`#upload-product-5`).prop('files')[0])
    }
    if ($(`#upload-product-6`).prop('files')[0]) {
        formData.append(`banner4`, $(`#upload-product-6`).prop('files')[0])
    }
    ajaxFile('/admin/setting-ads.html', formData, function (response) {
        if (response.error) {
            displayError(response.message);
        } else {
            displaySuccess("Cập nhật thành công");
            setTimeout(() => {
                location.reload()
            }, 500)
        }
    }, function () {

    });
}

$(() => {
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            let reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

})