$(() => {
    var editor = CKEDITOR.replace('content-news', {
        enterMode: CKEDITOR.ENTER_BR,
    });

    var editor2 = CKEDITOR.replace('content-term', {
        enterMode: CKEDITOR.ENTER_BR,
    });

    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    $('select[name="stores"]').select2({
        multiple: true,
        // allowClear: true,
        theme: "bootstrap4",
        width: 'resolve',
        placeholder: '[[---------Ch<PERSON>n thương hiệu --------]]',
        data: brandList.map(branch => ({id: branch._id, text: branch.name}))
    }).val(selectedBrand).trigger('change');

    $('#form-add-news').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-news').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        content = $('#content-news').val()
        formData.append('content', editor.getData());
        formData.append('contentTerm', editor2.getData());
        formData.append(`thumbnail`, $(`#upload-news-0`).prop('files')[0])
        formData.append(`cover`, $(`#upload-news-1`).prop('files')[0])
        ajaxFile(`/admin/promotion-edit/${news._id}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/promotions.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
