$(() => {
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    function renderListCity() {
        let html = '<option value="toan-quoc">Toàn Quốc</option>'
        for (let key in countrys) {
            html += `
        <option value="${removeUtf8ReplaceAll(key)}">${key}</option>
    `
        }
        $('#city-state').html(html);

        let valDefault = $('#city-state').attr('value');
        if (valDefault) $('#city-state').val(valDefault).change();
    }

    // Load danh sách sản phẩm cho dropdown
    function loadProductDropdown() {
        get('/admin/lay-danh-sach-san-pham-dropdown.html', {}, (response) => {
            if (!response.error && response.data && response.data.products) {
                let html = '<option value="">-- Chọn sản phẩm --</option>';
                response.data.products.forEach(product => {
                    html += `<option value="${product._id}">${product.name} (${product.code || 'N/A'})</option>`;
                });
                $('#product-select').html(html);

                // Nếu đang edit và có params là product ID, select nó
                if (news && news.screen === 'PRODUCT_DETAILS' && news.params) {
                    $('#product-select').val(news.params);
                }
            } else {
                console.error('Error loading products:', response.message);
            }
        });
    }

    // Xử lý thay đổi screen type
    function handleScreenTypeChange() {
        const screenType = $('#screen').val();
        const productContainer = $('#product-dropdown-container');
        const paramsInput = $('#params');

        if (screenType === 'PRODUCT_DETAILS') {
            productContainer.show();
            paramsInput.parent().parent().hide(); // Ẩn input params thông thường
            loadProductDropdown();
        } else {
            productContainer.hide();
            paramsInput.parent().parent().show(); // Hiện input params thông thường
        }
    }

    // Xử lý khi chọn sản phẩm từ dropdown
    $('#product-select').on('change', function() {
        const selectedProductId = $(this).val();
        $('#params').val(selectedProductId); // Cập nhật params với product ID
    });

    // Bind event cho screen dropdown
    $('#screen').on('change', handleScreenTypeChange);

    // Khởi tạo trạng thái ban đầu
    renderListCity();
    handleScreenTypeChange();

    $('#form-edit-banner-app').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-edit-banner-app').serializeArray();
        var formData = new FormData();
        data.forEach(item => {
            formData.append(item.name, item.value);
        })
        formData.append(`thumbail`, $(`#upload-news-0`).prop('files')[0])
        ajaxFile(`/admin/chinh-sua-banner-app/${news._id}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/admin/quan-ly-banner-app.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
