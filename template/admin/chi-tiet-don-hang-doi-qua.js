function tuChoiDonHang() {
    $('#tuChoiDonHang').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
    return false;
}

// function guiLenhTuChoi() {
//     let message = $('#lyDoTuChoiDonHang').val().trim();
//     if (message == '') {
//         alert("Vui lòng gửi lý do");
//     } else {
//         if (!confirm("Bạn chắc chắn muốn từ chối đơn hàng này? Sau khi từ chối thì bạn sẽ không thể chấp nhận bán hàng trên đơn hàng này")) return;
//         post(`/store/api/change-order-status/0/${buyInfo._id}?status=5`, {message}, (response) => {
//             if (!response.error) {
//                 location.reload()
//             } else {
//                 displayError(response.message);
//             }
//         })
//     }
// }

function updateStatus(status) {
    if (!confirm("Bạn chắc chắn muốn thay đổi trạng thái đơn hàng này?")) return;
    post(`/admin/change-don-hang-doi-qua/${buyInfo._id}.html?status=${status}`, {}, (response) => {
        debugger
        if (!response.error) {
            location.reload()
        } else {
            displayError(response.message);
        }
    })
}


function donHangBiTraLaiViewBox() {
    $('#donHangBiTraLaiViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
    return false;
}

function guiAdminreview() {
    let formData = new FormData();

    let maxFile = 0;
    for (let i = 1; i <= 3; i++) {
        if ($(`#upload-image-${i}`).prop('files')[0]) {
            maxFile++;
            formData.append(`pictures`, $(`#upload-image-${i}`).prop('files')[0])
        }
    }
    if (maxFile != 3) {
        displayError("Vui lòng gửi 3 ảnh");
    } else {
        ajaxFile(`/store/gui-anh-tra-hang.html/${buyInfo._id}`, formData, (res) => {
            if (res.error) {
                displayError(res.message);
            } else {
                location.reload();
            }
        }, (message) => {
            displayError(message)
        })
    }
}

function huyGiaoHang() {
    if (!confirm("Bạn chắc chắn muốn hủy dịch vụ vận chuyển")) return;
    post('/store/huy-don-van.html', {
        buyId: buyInfo._id
    }, res => {
        if (res.error) {
            displayError(res.message);
        } else {
            displaySuccess(res.message);
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
    }, error => {
        displayError('Lỗi kết nối');
    }, true)
}

$(function () {
    $('.inputBoxCustom p').on('click', function () {
        let name = $(this).parent().find('input').attr('name');
        let value = $(this).parent().find('input').val();

        post(`/store/cap-nhat-thong-tin-van-chuyen-don-hang.html/${buyInfo._id}`, {name, value}, (res) => {
            if (res.error) {
                displayError(res.message);
            } else {
                $(this).fadeOut();
            }
        }, (message) => {
            displayError(message);
        })
    });

    $('.upload-new-picture').on('change', function () {
        let that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            };
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })
})
