var billShows = {
    0: [],
    1: [],
    2: [],
    3: [],
    5: [],
}
let itemsPerPage = 10;
let indexBill = 0
let currentPage = {
    0:1,
    1: 1,
    2: 1,
    3: 1,
    5: 1,
};

let countBills = {
    0: 0,
    1: 0,
    2: 0,
    3: 0,
    5: 0,
};

let totalPages = {
    0: 1,
    1: 1,
    2: 1,
    3: 1,
    5: 1,
}

$(function () {
    $('.dateTimeHandle').datetimepicker({
        i18n: {
            de: {
                months: [
                    'Tha.1', 'Tha.2', 'Tha.3', 'Tha.4',
                    'Tha.5', 'Tha.6', 'Tha.7', 'Tha.8',
                    'Tha.9', 'Tha.10', 'Tha.11', 'Tha.12',
                ],
                dayOfWeek: [
                    "CN", "Thu.2", "Thu.3", "Thu.4",
                    "Thu.5", "Thu.6", "Thu.7",
                ]
            }
        },
        format: 'H:i, d/m/Y',
        scrollMonth : false,
        scrollInput : false,
        scrollTime:false

    });
})

function renderTable(index) {
    let html = '';
    let start = (currentPage[index] - 1) * itemsPerPage;
    let end = currentPage[index] * itemsPerPage
    if (end > countBills[index]) {
        end = countBills[index]
    }
    console.log('buid show ', billShows[index])
    for (let i = 0; i <= billShows[index].length-1; i++) {
        if (billShows[index][i]) {
            let bill = billShows[index][i]
            let image = ''
            let item = undefined
            if (bill?.data !== undefined)
            {
                item = JSON.parse(bill.data)
                image = JSON.parse(bill.data).image.data?.attributes.url
            }
            html += `
            <tr id="${bill._id}">
                <td>${(currentPage[index] - 1) * 10 + i + 1}</td>
                <td><a href="#">${bill._id}</a></td>
                <td>
                <div class="media">
                  <img class="mr-3" width="80" height="80" src="${image}" alt="">
                  <div class="media-body">
                    <h5 class="mt-0">${item?.productName || ''}</h5>
                    ${item?.description || ''}
                  </div>
                </div>
                </td>
                <td>${numberFormat(bill.point)}</td>
                <td>${moment(Number(bill.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                <td>
                <a href="/admin/chi-tiet-don-hang-doi-qua/${bill._id}.html" title="Chi tiết">
                    <img src="/template/ui/img/edit.svg" alt="">
                </a>
            </td>
            </tr>
            `
        }
    }
    $(`.pagination-${index} p`).text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${countBills[index]} kết quả`)
    $(`.table-${index} .tbody-table`).html(html);
    renderPagination(index)
}

function renderPagination(index) {
    let min = 1;
    let max = 1
    let html = '';
    $(`.pagination-${index} ul`).html('');
    html += `<li class="first-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page"  index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages[index]) {
        min = 1;
        max = totalPages[index]
    } else if (Number(currentPage[index]) + 2 > Number(totalPages[index])) {
        max = totalPages[index];
        min = totalPages[index] - 4
    } else if (Number(currentPage[index]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage[index]) - 2;
        max = Number(currentPage[index]) + 2
    }
    if (min == 2) {
        html += `<li class="page" index="${index}" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage[index]) ? 'active' : ''}" index="${index}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[index] - 2) {
        html += `<li class="page ${totalPages[index] == Number(currentPage[index]) ? 'active' : ''}" index="${index}"  value="${totalPages[index]}"><a href="javascript:;" >${totalPages[index]}</a></li>`
    }
    html += `<li class="next-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page" index="${index}"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $(`.pagination-${index} .pagination-list`).html(html);
}

function filterList(index) {
    indexBill=index
    // let search = removeUtf8($(`#search-goods-${index}`).val().trim());
    function formatTime(time){
        time = time.split(',')
        let timeDate = time[1].trim().split('/')
        time[1] = timeDate[1] + '/' + timeDate[0] +'/'+timeDate[2]
        return new Date(time.join(' ')).getTime()
    }
    let timeStart = formatTime($(`#time-start-${index}`).val())
    let timeEnd = formatTime($(`#time-end-${index}`).val())
    post(`/admin/lay-danh-sach-don-hang-doi-qua.html`, {
        page:currentPage[index],
        // search,
        index,
        timeStart,
        timeEnd,
        // payments:$(`#status-payments-${index}`).val()
    }, (res) => {
        if (!res.error) {
            console.log('res.data ', res.data)
            console.log('index ', index)
            billShows[index] = res.data.result
            countBills[index] = res.data.total
            totalPages[index] = Math.ceil(res.data.total / itemsPerPage)
            renderTable(index)
        } else {
            displayError(res.message);
        }
    })
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (currentPage[index] > 1) {
            currentPage[index] = currentPage[index] - 1;
            filterList(index);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = 1;
        filterList(index);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        currentPage[index] = totalPages[index];
        filterList(index);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        let index = $(this).attr('index')
        if (Number(currentPage[index]) < Number(totalPages[index])) {
            currentPage[index] = currentPage[index] + 1;
            filterList(index);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let index = $(this).attr('index')
        let page = e.currentTarget.value;
        currentPage[index] = page;
        filterList(index);
    });

    // $(document).on('click', '.export_excel', function (e) {
    //     e.preventDefault();
    //     post(`/parse-file-don-hang.html`, {
    //         bills: billShows[indexBill],
    //         indexService:1,
    //     }, res => {
    //         var element = document.createElement('a');
    //         element.setAttribute('href', res.data.path);
    //         element.setAttribute('target', '_blank');
    //         element.style.display = 'none';
    //         document.body.appendChild(element);
    //         element.click();
    //         document.body.removeChild(element);
    //     })
    // });

    filterList(0);
});

