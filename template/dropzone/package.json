{"name": "dropzone", "version": "5.7.0", "description": "Handles drag and drop of files for you.", "keywords": ["dragndrop", "drag and drop", "file upload", "upload"], "homepage": "http://www.dropzonejs.com", "main": "./dist/dropzone.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://www.colorglare.com"}], "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://www.colorglare.com"}], "scripts": {"test": "grunt && npm run test-prebuilt", "test-prebuilt": "mocha-headless-chrome -f test/test-prebuilt.html -a no-sandbox -a disable-setuid-sandbox"}, "bugs": {"email": "<EMAIL>", "url": "https://gitlab.com/meno/dropzone/issues"}, "license": "MIT", "repository": {"type": "git", "url": "https://gitlab.com/meno/dropzone.git"}}