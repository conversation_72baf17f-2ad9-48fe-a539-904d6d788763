$(function () {
    $('.baseFormRequest').on('submit', function () {
        $(this).find('button[type="submit"]').attr("disabled", true);
        let data = objectifyForm($(this).serializeArray());
        let method = $(this).attr('method').toLowerCase();
        let action = $(this).attr('action');
        let showE = $(this).attr('showE') || true;
        let showS = $(this).attr('showS') || true;
        let sRedirect = $(this).attr('sRedirect') || '';
        let eRedirect = $(this).attr('eRedirect') || '';
        switch (method) {
            case 'get':
                get(action, data, success => {
                    if (success.error) {
                        if (showE) displayError(success.message);

                        if (success.data && success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (eRedirect && eRedirect != '') {
                            setTimeout(() => {
                                location.href = eRedirect;
                            }, 500);
                        } else {
                            $(this).find('button[type="submit"]').attr("disabled", false);
                        }
                    } else {
                        if (showS) displaySuccess(success.message);
                        if (success.data && success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (sRedirect && sRedirect != '') {
                            setTimeout(() => {
                                location.href = sRedirect;
                            }, 500);
                        } else {
                            $(this).find('button[type="submit"]').attr("disabled", false);
                        }
                    }
                }, error => {
                    displayError(error.responseText);
                    $(this).find('button[type="submit"]').attr("disabled", false);
                });
                break;
            case 'post':
                post(action, data, success => {
                    if (success.error) {
                        if (showE) displayError(success.message);
                        if (success.data && success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (eRedirect && eRedirect != '') {
                            setTimeout(() => {
                                location.href = eRedirect;
                            }, 500);
                        } else {
                            $(this).find('button[type="submit"]').attr("disabled", false);
                        }
                    } else {
                        if (showS) displaySuccess(success.message);
                        if (success.data && success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (sRedirect && sRedirect != '') {
                            setTimeout(() => {
                                location.href = sRedirect;
                            }, 500);
                        } else {
                            $(this).find('button[type="submit"]').attr("disabled", false);
                        }
                    }
                }, error => {
                    displayError(error.responseText);
                    $(this).find('button[type="submit"]').attr("disabled", false);
                });
                break;
            default:
                $(this).find('button[type="submit"]').attr("disabled", false);
                break
        }
        return false
    });

    $('.baseFormDataRequest').on('submit', function () {
        let data = objectifyForm($(this).serializeArray());
        let formData = new FormData()
        for (let key in data) {
            formData.append(key, data[key]);
        }
        let arrs = document.querySelectorAll('.baseFormDataRequest input[type="file"]')
        arrs.forEach(item => {
            formData.append(item.getAttribute('name'), item.files[0])
        });
        let action = $(this).attr('action');
        let showE = $(this).attr('showE') || true;
        let showS = $(this).attr('showS') || true;
        let sRedirect = $(this).attr('sRedirect') || '';
        let eRedirect = $(this).attr('eRedirect') || '';
        ajaxFile(action, formData, success => {
            if (success.error) {
                if (showE) displayError(success.message);
                if (success.data && success.data.redirect) {
                    setTimeout(() => {
                        location.href = success.data.redirect;
                    }, 500);
                } else if (eRedirect && eRedirect != '') {
                    setTimeout(() => {
                        location.href = eRedirect;
                    }, 500);
                } else {
                    $(this).find('button[type="submit"]').attr("disabled", false);
                }
            } else {
                if (showS) displaySuccess(success.message);
                if (success.data && success.data.redirect) {
                    setTimeout(() => {
                        location.href = success.data.redirect;
                    }, 500);
                } else if (sRedirect && sRedirect != '') {
                    setTimeout(() => {
                        location.href = sRedirect;
                    }, 500);
                } else {
                    $(this).find('button[type="submit"]').attr("disabled", false);
                }
            }
        }, error => {
            $(this).find('button[type="submit"]').attr("disabled", false);
            displayError(error.responseText);
        });
        return false
    });

    if ($('.buttonPostRequest')) $('.buttonPostRequest').each(function () {
        let method = $(this).attr('method').toLowerCase();
        let action = $(this).attr('action');
        let showE = $(this).attr('showE') || true;
        let showS = $(this).attr('showS') || true;
        let sRedirect = $(this).attr('sRedirect') || '';
        let eRedirect = $(this).attr('eRedirect') || '';
        let arrsAttrs = $(this).attr('url').spilit('|')
        let data = {}
        for (let i = 0; i < arrsAttrs.length; i++) {
            data[arrsAttrs[i]] = $(this).attr(arrsAttrs[i])
        }
        switch (method) {
            case 'get':
                get(action, data, success => {
                    if (success.error) {
                        if (showE) displayError(success.message);

                        if (success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (eRedirect && eRedirect != '') {
                            setTimeout(() => {
                                location.href = eRedirect;
                            }, 500);
                        }
                    } else {
                        if (showS) displaySuccess(success.message);
                        if (success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (sRedirect && sRedirect != '') {
                            setTimeout(() => {
                                location.href = sRedirect;
                            }, 500);
                        } else {
                        }
                    }
                }, error => {
                    displayError(error.responseText);
                });
                break;
            case 'post':
                post(action, data, success => {
                    if (success.error) {
                        if (showE) displayError(success.message);
                        if (success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (eRedirect && eRedirect != '') {
                            setTimeout(() => {
                                location.href = eRedirect;
                            }, 500);
                        }
                    } else {
                        if (showS) displaySuccess(success.message);
                        if (success.data.redirect) {
                            setTimeout(() => {
                                location.href = success.data.redirect;
                            }, 500);
                        } else if (sRedirect && sRedirect != '') {
                            setTimeout(() => {
                                location.href = sRedirect;
                            }, 500);
                        } else {
                        }
                    }
                }, error => {
                    displayError(error.responseText);
                });
                break;
            default:
                break;
        }
    })
});
