$(() => {
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            };
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    });

    $(document).on('click', '.delete-account', function (e) {
        let conf = confirm('Bạn có chắc muốn xóa tài khoản ngân hàng này?');
        let id = $(this).attr('accountId');
        if (conf) {
            get(`/delete-account-bank/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
});