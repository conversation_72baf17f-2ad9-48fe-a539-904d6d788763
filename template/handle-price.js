function detectView() {
    let typePet = $('select[name="typePet"]').val();
    switch (typePet) {
        case 'null':
            $('.gia-thu-cung').hide();
            break;
        case '0':
            $('.gia-thu-cung .cho').show();
            $('.gia-thu-cung .meo').hide();
            $('.gia-thu-cung .thu-cung-khac').hide();
            $('.gia-thu-cung').fadeIn();
            break;
        case '1':
            $('.gia-thu-cung .cho').hide();
            $('.gia-thu-cung .meo').show();
            $('.gia-thu-cung .thu-cung-khac').hide();
            $('.gia-thu-cung').fadeIn();
            break;
        case '2':
            $('.gia-thu-cung .cho').hide();
            $('.gia-thu-cung .meo').hide();
            $('.gia-thu-cung .thu-cung-khac').show();
            $('.gia-thu-cung').fadeIn();
            break;
        case '3':
            $('.gia-thu-cung .cho').show();
            $('.gia-thu-cung .meo').show();
            $('.gia-thu-cung .thu-cung-khac').show();
            $('.gia-thu-cung').fadeIn();
            break;
    }
}

function addKhoangGia(el) {
    $(el).parents('.item-thu-cung').find('.khoang-gia').append(`<div style="width: 100%;" class="row">
                                                    <div class="col-md-6 type-item">
                                                        <div class="input_field schedule_field">
                                                            <input placeholder="Cân nặng: vd Dưới 5Kg" type="text">
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6 type-item">
                                                        <div class="input_field schedule_field">
                                                            <input placeholder="Giá/Ngày" type="text" data-type="currency" style="width: 70%">
                                                            <a href="javascript:;" class="delete-service" title="Xoá" style="width: 30%" onclick="deleteKhoangGia(this)">
                                                                <img src="/template/ui/img/delete-2-bage.svg" alt="">
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>`);
    reloadDataTypeCurrency();
}

function addGiaTriKhoangGia(parent, data) {
    $(parent).find('.khoang-gia').append(`<div style="width: 100%;" class="row">
                                                    <div class="col-md-6 type-item">
                                                        <div class="input_field schedule_field">
                                                            <input placeholder="Cân nặng: vd Dưới 5Kg" type="text" value="${data.label}">
                                                        </div>
                                                    </div>

                                                    <div class="col-md-6 type-item">
                                                        <div class="input_field schedule_field">
                                                            <input placeholder="Giá/Ngày" type="text" data-type="currency" style="width: 70%" value="${data.value}">
                                                            <a href="javascript:;" class="delete-service" title="Xoá" style="width: 30%" onclick="deleteKhoangGia(this)">
                                                                <img src="/template/ui/img/delete-2-bage.svg" alt="">
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>`);
    reloadDataTypeCurrency();
}

function deleteKhoangGia(el) {
    $(el).parent().parent().parent().remove();
}

$(function () {
    $('select[name="typePet"]').on('change', function () {
        detectView();
    });
    if (typeof dataService != "undefined" && dataService && dataService.type == 2) {
        let khoangGia = dataService.khoangGia;
       if(khoangGia.cho) {khoangGia.cho.forEach(el => {
            addGiaTriKhoangGia('.cho', el);
        });}

       if(khoangGia.meo){ khoangGia.meo.forEach(el => {
            addGiaTriKhoangGia('.meo', el);
        });}
        if(khoangGia.thuCungKhac){
        khoangGia.thuCungKhac.forEach(el => {
            addGiaTriKhoangGia('.thu-cung-khac', el);
        })}
    }
});
detectView();

function layKhoangGia() {
    let cho = [];
    let meo = [];
    let thuCungKhac = [];
    let error = false;
    let message = '';
    let typePet = $('select[name="typePet"]').val();
    let min = 0;
    let max = 0;
    if (typePet == 'null') {
        error = true;
        message = "Bạn chưa chọn loại thú cưng";
    }

    if (typePet == '0' || typePet == 3) {
        $('.cho .khoang-gia>.row').each(function () {
            let label = '', value = '';
            $(this).find('input').each(function (index, el,) {
                if (index == 0) {
                    label = $(this).val().trim();
                } else {
                    value = $(this).val().trim();
                }
            });

            value = value.replace('VND', '').trim();
            value = value.split(',').join('');

            if (value && value != '' && !isNaN(Number(value)) && label && label != '') {
                cho.push({label, value: Number(value)});
                if (min == 0) {
                    min = Number(value)
                } else if (min > Number(value)) {
                    min = Number(value)
                }

                if (max == 0) {
                    max = Number(value)
                } else if (max < Number(value)) {
                    max = Number(value)
                }
            } else {
                // có lỗi
                error = true;
                message = "Không được để trống khoảng giá Ô tô. Nếu không dùng, bạn có thể xoá";
            }
        });

        if (cho.length == 0) {
            error = true;
            message = "Bạn cần khai báo khoảng giá cho Ô tô";
        }
    }

    if (typePet == '1' || typePet == 3) {
        $('.meo .khoang-gia>.row').each(function () {
            let label = '', value = '';
            $(this).find('input').each(function (index, el,) {
                if (index == 0) {
                    label = $(this).val().trim();
                } else {
                    value = $(this).val().trim();
                }
            });

            value = value.replace('VND', '').trim();
            value = value.split(',').join('');

            if (value && value != '' && !isNaN(Number(value)) && label && label != '') {
                meo.push({label, value: Number(value)});
                if (min == 0) {
                    min = Number(value)
                } else if (min > Number(value)) {
                    min = Number(value)
                }

                if (max == 0) {
                    max = Number(value)
                } else if (max < Number(value)) {
                    max = Number(value)
                }
            } else {
                // có lỗi
                error = true;
                message = "Không được để trống khoảng giá mèo. Nếu không dùng, bạn có thể xoá";
            }
        });

        if (meo.length == 0) {
            error = true;
            message = "Bạn cần khai báo khoảng giá cho mèo";
        }
    }

    if (typePet == '2' || typePet == 3) {
        $('.thu-cung-khac .khoang-gia>.row').each(function () {
            let label = '', value = '';
            $(this).find('input').each(function (index, el,) {
                if (index == 0) {
                    label = $(this).val().trim();
                } else {
                    value = $(this).val().trim();
                }
            });

            value = value.replace('VND', '').trim();
            value = value.split(',').join('');

            if (value && value != '' && !isNaN(Number(value)) && label && label != '') {
                thuCungKhac.push({label, value: Number(value)});
                if (min == 0) {
                    min = Number(value)
                } else if (min > Number(value)) {
                    min = Number(value)
                }

                if (max == 0) {
                    max = Number(value)
                } else if (max < Number(value)) {
                    max = Number(value)
                }
            } else {
                // có lỗi
                error = true;
                message = "Không được để trống khoảng giá Phương tiện khác. Nếu không dùng, bạn có thể xoá";
            }
        })
        if (thuCungKhac.length == 0) {
            error = true;
            message = "Bạn cần khai báo khoảng giá cho Phương tiện khác";
        }
    }

    return {
        error, message, khoangGia: {cho, meo, thuCungKhac, min, max}
    }

}
