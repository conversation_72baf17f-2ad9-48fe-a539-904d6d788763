.sec_portfolio .table_portfolio tbody tr td:last-child {
    width: auto;
    min-width: 100px;
}

.sec_portfolio .table_portfolio tbody tr td a img {
    height: 15px;
    width: 20px;
}

.number-message {
    font-size: 11px;
    color: #ffffff;
    background: #d31e27;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    border-radius: 50%;
    position: absolute;
    top: 15px;
    right: -17px;
}

.number-notification {
    font-size: 11px;
    color: #ffffff;
    background: #d31e27;
    width: 16px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    border-radius: 50%;
    position: absolute;
    top: 15px;
    right: -17px;
}

.list_chat_container .chat_item a.no-read {
    background: #c3c3c3;
    box-shadow: 0 5px 15px 0 rgba(16, 27, 79, 0.15);
}

.inputBoxCustom {
    width: 100%;
    position: relative;
}

.inputBoxCustom input {
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 5px 30px 5px 5px;
    font-size: 13px;
}

.inputBoxCustom p {
    background: #08bca6;
    width: 30px;
    right: 0;
    position: absolute;
    text-align: center;
    color: #fff;
    top: 0;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    height: 100%;
    padding: 4px;
    font-weight: bold;
    cursor: pointer;
    display: none;
}

.list_medical .customer_medical.no-watch {
    background: #d2d0d0 !important;
}

.list_medical .customer_medical {
    cursor: pointer;
}

.input_field .type-product {
    padding: 20px;
    border: solid 2px #8db7ff;
}

.button-classify {
    display: inline-block;
    margin: 0 3px;
    width: auto;
    padding: 14px 30px;
    border-radius: 4px;
    line-height: 18px;
    font-weight: bold;
    font-style: normal;
    width: 100%;
    letter-spacing: 0;
    text-align: center;
    cursor: pointer;
    position: relative;
}

.type-item > div {
    display: flex;
}

.type-item input {
    width: 90%;
}

.type-item a {
    width: 10%;
    padding: 10px 10px 10px 15px;
}

.form_disable_chat {
    display: none;
}

.form_enter_chat {
    display: none;
}

.form_disable_chat p {
    background: #ccc;
    text-align: center;
    padding: 20px;
    font-weight: 500;
}

.thong-ke {
}

.thong-ke .tl {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: center !important;
}

.thong-ke .tl span {
    font-size: unset !important;
    line-height: unset !important;
    margin-bottom: unset !important;
}

.thong-ke .tl span:first-child {
    flex: 1;
}

.thong-ke .tl span:last-child {
}

.thong-ke .general_payed {
    width: 100% !important;
}

.thong-ke .inner_box {
    width: 45% !important;
}

.thong-ke .inner_box:first-child {
    margin-right: 10%;
}

.gia-thu-cung {
}

.gia-thu-cung .item-thu-cung {
    padding-bottom: 10px !important;
    padding-left: 0px !important;
    padding-right: 0px !important;
    border: 1px solid #e01b22;
}

.gia-thu-cung .title {
    font-size: 16px;
    line-height: 24px;
    background: #e01b22;
    color: #fff;
    margin: 0 !important;
    padding: 2px;
}

.gia-thu-cung h5 {
    color: #fff;
}

.gia-thu-cung .button-classify {
    margin-top: 15px !important;
}

.input_field .is_featured {
    float: right;
    font-weight: 700;
}

.input_field input[name='isFeatured'] {
    height: 14px;
    width: 14px;
    position: relative;
    bottom: -2px;
}

.them-dich-vu .delete-service img {
    max-width: unset;
}

.button-classify.disabled:before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    width: calc(100% + 4px);
    height: calc(100% + 4px);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.8);
}

.button-classify.disabled {
    border: rgba(255, 255, 255, 0.8);
    color: rgba(255, 255, 255, 0.8);
}

.button-classify.danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.badge-featured {
    font-size: 16px !important;
    line-height: 16px !important;
}

.main-color {
    color: #ff1615 !important;
}

/*.dropzone,*/
/*.dropzone * {*/
/*    box-sizing: border-box;*/
/*}*/
/*.dropzone {*/
/*    display: flex;*/
/*    flex-wrap: wrap;*/
/*    align-items: flex-start;*/
/*    align-content: flex-start;*/
/*    border: 2px dotted #f443365e !important;*/
/*    padding: 0 !important;*/
/*    margin: 15px 5px;*/
/*}*/
/*.dropzone.dz-clickable {*/
/*    cursor: pointer;*/
/*}*/
/*.dropzone.dz-clickable * {*/
/*    cursor: default;*/
/*}*/
/*.dropzone.dz-clickable .dz-message {*/
/*    font-size: 14px;*/
/*    margin: auto;*/
/*    margin-top: 15px;*/
/*}*/
/*.dropzone.dz-clickable .dz-message,*/
/*.dropzone.dz-clickable .dz-message * {*/
/*    cursor: pointer;*/
/*}*/
/*.dropzone.dz-clickable .dz-message .note {*/
/*    display: block;*/
/*    font-size: 14px;*/
/*}*/
/*.dropzone.dz-started .dz-message {*/
/*    display: none;*/
/*}*/
/*.dropzone.dz-drag-hover .dz-message {*/
/*    opacity: 0.5;*/
/*}*/
/*.dropzone .dz-message {*/
/*    text-align: center !important;*/
/*    margin: 2em 0 !important;*/
/*    margin: auto !important;*/
/*    margin-top: 40px !important;*/
/*    display: block;*/
/*}*/
/*.dz-preview {*/
/*    position: relative;*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    overflow: hidden;*/
/*    width: 140px;*/
/*    height: 210px;*/
/*    margin: 0 13px 13px 0;*/
/*    border-radius: 6px;*/
/*    border: 1px solid rgba(0, 0, 0, 0.2);*/
/*    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);*/
/*}*/
/*.dz-preview.dz-complete.dz-error {*/
/*    order: 1;*/
/*}*/
/*.dz-preview .dz-remove {*/
/*    display: none; !* none is default*!*/
/*    position: absolute;*/
/*    top: 0;*/
/*    right: 0;*/
/*    bottom: auto;*/
/*    left: 0;*/
/*    padding: 0 0 13px 0;*/
/*    font-size: 13px;*/
/*    text-align: center;*/
/*    background-image: linear-gradient(#fff 0, #fff 21px, transparent 36px);*/
/*}*/
/*.dz-preview.dz-complete.dz-error .dz-remove {*/
/*    display: block;*/
/*}*/
/*.dz-preview .dz-image {*/
/*    position: relative;*/
/*    display: block;*/
/*    overflow: hidden;*/
/*    width: 100% !important;*/
/*    height: 75% !important;*/
/*    z-index: 0 !important;*/
/*    border-radius: 0 !important;*/
/*}*/
/*.dz-preview .dz-image img {*/
/*    display: block;*/
/*}*/

/*.dz-preview.dz-processing .dz-progress {*/
/*    opacity: 0;*/
/*    transition: all 0.2s linear;*/
/*}*/
/*.dz-preview.dz-complete .dz-progress {*/
/*    opacity: 0;*/
/*    transition: opacity 0.4s ease-in;*/
/*}*/
/*!* opacity 1 neu muon show process*!*/
/*.dz-preview .dz-progress {*/
/*    opacity: 1;*/
/*    z-index: 1000;*/
/*    pointer-events: none;*/
/*    position: absolute;*/
/*    height: 16px;*/
/*    left: 50%;*/
/*    top: 50%;*/
/*    margin-top: -8px;*/
/*    width: 80px;*/
/*    margin-left: -40px;*/
/*    background-color: rgba(255, 255, 255, 0.9);*/
/*    border: 1px solid;*/
/*    -webkit-transform: scale(1);*/
/*    border-radius: 8px;*/
/*    overflow: hidden;*/
/*}*/
/*.dz-preview .dz-progress .dz-upload {*/
/*    background: #333;*/
/*    background-image: linear-gradient(to bottom, #8bc34a, #689f38);*/
/*    position: absolute;*/
/*    top: 0;*/
/*    left: 0;*/
/*    bottom: 0;*/
/*    width: 0;*/
/*}*/
/*.dz-preview .dz-error-message {*/
/*    display: none;*/
/*    padding: 6px;*/
/*    line-height: 16px;*/
/*    font-size: 13px;*/
/*    background: linear-gradient(to bottom, #be2626, #a92222);*/
/*    color: #fff;*/
/*}*/
/*.dz-preview.dz-complete.dz-error .dz-error-message {*/
/*    display: block;*/
/*}*/
/*.dz-preview .dz-filename {*/
/*    font-size: 13px;*/
/*    padding: 6px;*/
/*    line-height: 16px;*/
/*    text-align: center;*/
/*    word-break: break-all;*/
/*}*/
#dropzone, #upload-images{
    padding: 20px;
}
.dropzone {
    background: white;
    border-radius: 5px;
    border: 2px dashed #c7c0c0!important;
    border-image: none;
}
.dropzone .dz-filename {
    display: none;
}
.button_submit_form {
    align-items: baseline;
    width: 100%;
    margin: auto;
    margin-left: auto;
    justify-content: space-between;
    margin-top: 30px;
    margin-bottom: 30px;
    text-align: center;
}
.button_submit_form button {
    display: inline-block;
    margin: 0 3px;
    width: auto;
    padding: 14px 30px;
    border-radius: 4px;
    line-height: 18px;
}
.button_submit_form a {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    padding: 14px 30px;
}
.button_submit_form a:hover {
    background-color: #ff1615;
    color: #fff;
    border-color: #ff1615;
}

li.list-group-item {
    padding: 10px 0;
}
.col-inner{
    height: 100%;
}
.row.row-eq-height {
    margin-bottom: 40px;
}
.my-3.p-3.bg-white.rounded.box-shadow {
    height: 100%;
}
.booking-detail .button_submit .confirm_schedule_form {
    background: #0CD216;
    color: #fff;
    border-color: #0CD216;
}
.booking-detail a {
    border: 1px solid #E2E2E2;
    border-radius: 4px;
}
.booking-detail .button_submit a, .booking-detail .button_submit button {
    display: inline-block;
    margin: 0 3px;
    width: auto;
    padding: 14px 30px;
    border-radius: 4px;
    line-height: 18px;
}
.payment-note {
    padding: 10px;
    background: #fb1c1c;
    font-size: 13px;
    font-weight: bold;
    margin: 10px 0;
    color: #fff;
    border-radius: 3px;
}

form#form-add-service , #form-add-product{
    padding: 15px;
}
.btn-co-so{
    background: #ff1615;
    padding: 5px 10px;
    border: 1px solid #ca1015;
    border-radius: 3px;
    color: #fff;
}
.btn-co-so:hover{
    background: #ca1015;
    color: #fff;
}
li.list-group-item.sv {
    background: #c6efc6;
    padding: 10px;
    margin-bottom: 10px;
    font-size: 0.9em;
}

.order-list-price{
}
.order-list-price li label{
    min-width: 130px;
}
.content-scroll-notification {
    height: 600px;
    overflow: auto;
}

/* Tab Management - Hide all tab content by default */
.content_tab .inner_box {
    display: none;
}

/* Show only the first tab content by default (will be overridden by JS) */
.content_tab .inner_box:first-child {
    display: block;
}

/* Tab URL Management Styles */
.tab_container .menu_tab li {
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab_container .menu_tab li.active {
    font-weight: bold;
    color: #333;
}

/* Quan ly lich hen - Hide all inner_box except the last one (tab 4) */
.search_portfolio.form_schedule .content_tab .inner_box {
    display: none;
}

.search_portfolio.form_schedule .content_tab .inner_box:last-child {
    display: block;
}

/* Status styles for booking table */
.status-pending {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #EFB719;
    border-radius: 15.5px;
    padding: 6px 12px;
    min-width: 80px;
}

.status-approved {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #0CD216;
    border-radius: 15.5px;
    padding: 6px 12px;
    min-width: 80px;
}

.status-cancelled {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #AAAAAA;
    border-radius: 15.5px;
    padding: 6px 12px;
    min-width: 80px;
}

.status-completed {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #1969EF;
    border-radius: 15.5px;
    padding: 6px 12px;
    min-width: 80px;
}

.status-unknown {
    display: inline-block;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 15.5px;
    padding: 6px 12px;
    min-width: 80px;
}
