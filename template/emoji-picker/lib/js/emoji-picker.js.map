{"version": 3, "file": "emoji-picker.js", "sourceRoot": "", "sources": ["emoji-picker.coffee"], "names": [], "mappings": ";AAAA;EAAM,IAAC,CAAA;IAMQ,qBAAC,OAAD;AACX,UAAA;;QADY,UAAU;;MACtB,CAAC,CAAC,SAAS,CAAC,QAAZ,4CAA0C;MAC1C,CAAC,CAAC,SAAS,CAAC,UAAZ,gDAA8C;MAC9C,IAAC,CAAA,qBAAD,CAAuB,OAAvB;MACA,IAAwD,CAAC,OAAO,CAAC,kBAAjE;QAAA,OAAO,CAAC,kBAAR,GAA6B,wBAA7B;;MACA,IAAI,CAAC,OAAL,GAAe;IALJ;;0BAOb,QAAA,GAAU,SAAA;AACR,UAAA;MAAA,KAAA,GAAQ,kBAAkB,CAAC,IAAnB,CAAwB,SAAS,CAAC,SAAlC,CAAA,IAAgD,CAAC,MAAM,CAAC;MAChE,IAAI,KAAJ;AACE,eADF;;aAGA,CAAA,CAAE,IAAI,CAAC,OAAO,CAAC,kBAAf,CAAkC,CAAC,SAAnC,CAA6C,CAAC,CAAC,MAAF,CAAS;QAClD,UAAA,EAAY,IADsC;QAElD,UAAA,EAAY,IAFsC;OAAT,EAGxC,IAAI,CAAC,OAHmC,CAA7C;IALQ;;0BAWV,qBAAA,GAAsB,SAAC,OAAD;AACpB,UAAA;MAAA,KAAA,GAAQ;MACR,YAAA,GAAe;MACf,CAAA,GAAI;MACJ,CAAA,GAAI;MACJ,GAAA,GAAM;MACN,IAAA,GAAO;MACP,QAAA,GAAW;MACX,GAAA,GAAM;MACN,MAAA,GAAS;MACT,YAAA,GAAe;MACf,CAAA,GAAI;AACJ,aAAM,CAAA,GAAI,MAAM,CAAC,eAAe,CAAC,MAAjC;QACE,YAAA,GAAe,MAAM,CAAC,8BAA+B,CAAA,CAAA,CAAG,CAAA,CAAA;QACxD,CAAA,GAAI;AACJ,eAAM,CAAA,GAAI,MAAM,CAAC,eAAgB,CAAA,CAAA,CAAE,CAAC,MAApC;UACE,QAAA,GAAW,MAAM,CAAC,KAAM,CAAA,MAAM,CAAC,eAAgB,CAAA,CAAA,CAAG,CAAA,CAAA,CAA1B;UACxB,IAAA,GAAO,QAAS,CAAA,CAAA,CAAG,CAAA,CAAA;UACnB,GAAA,GAAM,IAAI,CAAC,KAAL,CAAW,CAAA,GAAI,YAAf;UACN,MAAA,GAAS,CAAA,GAAI;UACb,KAAM,CAAA,GAAA,GAAM,IAAN,GAAa,GAAb,CAAN,GAA0B,CAAC,CAAD,EAAI,GAAJ,EAAS,MAAT,EAAiB,GAAA,GAAM,IAAN,GAAa,GAA9B;UAC1B,YAAa,CAAA,IAAA,CAAb,GAAqB,QAAS,CAAA,CAAA;UAC9B,CAAA;QAPF;QAQA,CAAA;MAXF;MAaA,CAAC,CAAC,SAAS,CAAC,KAAZ,GAAoB;aACpB,CAAC,CAAC,SAAS,CAAC,YAAZ,GAA2B;IA1BP;;0BA4BtB,cAAA,GAAe,SAAC,KAAD;MACb,IAAG,CAAC,KAAJ;AACE,eAAO,GADT;;MAEA,IAAG,CAAC,MAAM,CAAC,SAAX;QACE,MAAM,CAAC,YAAP,CAAA,EADF;;aAEA,KAAK,CAAC,OAAN,CAAc,MAAM,CAAC,SAArB,EAAgC,SAAC,CAAD;AAC9B,YAAA;QAAA,GAAA,GAAM,MAAM,CAAC,QAAS,CAAA,CAAA;QACtB,IAAG,GAAH;iBACE,IADF;SAAA,MAAA;iBAGE,GAHF;;MAF8B,CAAhC;IALa;;0BAYf,6BAAA,GAA8B,SAAC,OAAD,EAAU,KAAV;AAC5B,UAAA;MAAA,IAAG,CAAC,KAAJ;AACE,eAAO,GADT;;MAEA,IAAG,CAAC,MAAM,CAAC,QAAX;QACE,MAAM,CAAC,YAAP,CAAA,EADF;;MAGA,gBAAA,GAAmB,KAAK,CAAC,KAAN,CAAY,MAAM,CAAC,QAAnB;AACnB,WAAA,kDAAA;;QACE,GAAA,GAAM;QACN,IAAG,MAAM,CAAC,QAAQ,CAAC,IAAhB,CAAqB,IAArB,CAAH;UACE,GAAA,GAAM,MAAM,CAAC,UAAW,CAAA,IAAA;UACxB,IAAG,GAAH;YACE,GAAA,GAAM,GAAA,GAAM,GAAN,GAAY;YAClB,GAAA,GAAM,CAAC,CAAC,SAAS,CAAC,UAAZ,CAAuB,CAAC,CAAC,SAAS,CAAC,KAAM,CAAA,GAAA,CAAzC,EAFR;WAFF;SAAA,MAAA;UAME,GAAA,GAAM,QAAQ,CAAC,cAAT,CAAwB,IAAxB,EANR;;QAOA,OAAO,CAAC,MAAR,CAAe,GAAf;AATF;aAWA,KAAK,CAAC,OAAN,CAAc,MAAM,CAAC,QAArB,EAA+B,SAAC,CAAD;AAC7B,YAAA;QAAA,GAAA,GAAM,MAAM,CAAC,UAAW,CAAA,CAAA;QACxB,IAAG,GAAH;UACE,GAAA,GAAM,GAAA,GAAM,GAAN,GAAY;UAClB,IAAA,GAAO,CAAC,CAAC,SAAS,CAAC,UAAZ,CAAuB,CAAC,CAAC,SAAS,CAAC,KAAM,CAAA,GAAA,CAAzC;iBACP,KAHF;SAAA,MAAA;iBAKE,GALF;;MAF6B,CAA/B;IAlB4B;;0BA2B9B,YAAA,GAAa,SAAC,KAAD;MACX,IAAG,CAAC,KAAJ;AACE,eAAO,GADT;;MAEA,IAAG,CAAC,MAAM,CAAC,SAAX;QACE,MAAM,CAAC,YAAP,CAAA,EADF;;aAEA,KAAK,CAAC,OAAN,CAAc,MAAM,CAAC,SAArB,EAAgC,SAAC,CAAD;AAC9B,YAAA;QAAA,IAAG,CAAH;UACE,IAAA,GAAO,CAAC,CAAC,SAAS,CAAC,UAAZ,CAAuB,CAAC,CAAC,SAAS,CAAC,KAAM,CAAA,CAAA,CAAzC;iBACP,KAFF;SAAA,MAAA;iBAIE,GAJF;;MAD8B,CAAhC;IALW;;;;;AA3Ff"}