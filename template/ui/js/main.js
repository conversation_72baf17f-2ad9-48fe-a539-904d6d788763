;
(function ($) {

    'use strict'

    var isMobile = {
        Android: function () {
            return navigator.userAgent.match(/Android/i);
        },
        BlackBerry: function () {
            return navigator.userAgent.match(/BlackBerry/i);
        },
        iOS: function () {
            return navigator.userAgent.match(/iPhone|iPad|iPod/i);
        },
        Opera: function () {
            return navigator.userAgent.match(/Opera Mini/i);
        },
        Windows: function () {
            return navigator.userAgent.match(/IEMobile/i);
        },
        any: function () {
            return (isMobile.Android() || isMobile.BlackBerry() || isMobile.iOS() || isMobile.Opera() || isMobile.Windows());
        }
    };

    var ShowPopup = function () {
        var popupClose = $('.popup .popup_container .title_popup .close_popup');

        var popupClose2 = $('.popup .popup_container .submit_popup a.back');


        popupClose.on('click', function (e) {
            e.preventDefault();
            $(this).closest('.boxed').children('.popup').removeClass('open');
            $('.modal-backdrop.fade.show').remove();
        });
        popupClose2.on('click', function (e) {
            e.preventDefault();
            $(this).closest('.boxed').children('.popup').removeClass('open');
            $('.modal-backdrop.fade.show').remove();
        });
        $('.popup').on('click', function (e) {
            e.stopPropagation();
        });

        var popupfinish = $('.sec_pagination .pagination-list li .next');
        var popupfinishClose = $('.popup-finish .submit-box .cancle-popup');
        var popupfinishComplete = $('.popup-finish .submit-box .complete-popup');

        // Order
        popupfinish.on('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $(this).closest('.boxed').children('.popup-finish').addClass('open');
            $('body').append('<div class="modal-backdrop fade show"></div>');
        });
        popupfinishClose.on('click', function (e) {
            e.preventDefault();
            $(this).closest('.boxed').children('.popup-finish').removeClass('open');
            $('.modal-backdrop.fade.show').remove();
        });
        popupfinishComplete.on('click', function (e) {
            e.preventDefault();
            $(this).closest('.boxed').children('.popup-finish').removeClass('open');
            $('.modal-backdrop.fade.show').remove();
        });
        $('.popup-finish').on('click', function (e) {
            e.stopPropagation();
        });


        $('.box_account_info .name_account > a').on('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $(this).toggleClass('active');
            $(this).next('.menu_account_info').toggleClass('open');
        });

        $('.box_account_info').on('click', function (e) {
            e.stopPropagation();
        });

        $('body').on('click', function () {
            $('.box_account_info .name_account > a').removeClass('active');
            $('.box_account_info').find('.menu_account_info').removeClass('open');
            // $('.modal-backdrop.fade.show').remove();
        });

        $('.box_info_order .title_box a').on('click', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $(this).closest('.boxed').children('.popup').addClass('open');
            $('body').append('<div class="modal-backdrop fade show"></div>');
        });

        $('.popup .popup_detail_product .submit_popup .confirm').on('click', function (e) {
            e.preventDefault();
            $(this).closest('.boxed').children('.popup').removeClass('open');
            $('.modal-backdrop.fade.show').remove();
        });

    };

    var tabContainer = function () {
        $('.tab_container').each(function () {
            $('.menu_tab').children('li').first().addClass('active');
            $(this).children('.content_tab').children().hide();
            $(this).children('.content_tab').children().first().show();
            $(this).find('.menu_tab').children('li').on('click', function (e) {
                var liActive = $(this).index(),
                    contentActive = $(this).siblings().removeClass('active').parents('.tab_container').children('.content_tab').children().eq(liActive);

                contentActive.addClass('active').fadeIn('slow');
                contentActive.siblings().removeClass('active');
                $(this).addClass('active').parents('.tab_container').children('.content_tab').children().eq(liActive).siblings().hide();
                e.preventDefault();
            });
        });
    };

    var Scroll = function () {
        $(document).on('click', 'a[href^="#"]', function (event) {
            var href = $.attr(this, 'href');
            var $this = $(this);

            // Bỏ qua pagination links và javascript links
            if ($this.closest('.pagination-list').length > 0 ||
                $this.closest('.pagination_container').length > 0) {
                return; // Không xử lý pagination links
            }

            // Chỉ xử lý anchor links thực sự, bỏ qua javascript: links
            if (href && href !== '#' && !href.startsWith('#javascript')) {
                var targetElement = $(href);
                if (targetElement.length > 0) {
                    event.preventDefault();

                    $('html, body').animate({
                        scrollTop: targetElement.offset().top
                    }, 500);
                }
            }
        });
    };

    var removePreloader = function () {
        $(window).load(function () {
            $('#preloader').css('opacity', 0);
            setTimeout(function () {
                $('#preloader').hide();
            }, 1000);
        });

    };

    // Dom Ready
    $(function () {
        ShowPopup();
        tabContainer();
        Scroll();
        removePreloader();
    });

})(jQuery);
