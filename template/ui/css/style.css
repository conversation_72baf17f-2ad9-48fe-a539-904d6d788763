@import url('https://fonts.googleapis.com/css?family=Roboto:400,500,700,900&display=swap');
@import url("./font/sanfrancisco-font.css");

@font-face {
    font-family: "iCielSamsungSharpSans";
    src: url(font/SamsungSharpSans-Bold-UTF8.ttf);
}

/* Reset
-------------------------------------------------------------- */
html {
    overflow-y: scroll;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

a, abbr, acronym, address, applet, article, aside, audio, b, big, blockquote, body, caption, canvas, center, cite, code,
dd, del, details, dfn, dialog, div, dl, dt, em, embed, fieldset, figcaption, figure, form, footer, header, hgroup, h1, h2, h3, h4, h5, h6, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, output, p, pre, q, ruby, s, samp, section, small, span, strike, strong, sub, summary, sup, tt, table, tbody, textarea, tfoot, thead, time, tr, th, td, u, ul, var, video {
    font-family: inherit;
    font-size: 100%;
    font-weight: inherit;
    font-style: inherit;
    vertical-align: baseline;
    margin: 0;
    padding: 0;
    border: 0;
    outline: 0;
    background: transparent;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
    display: block;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
    quotes: none;
}

figure {
    margin: 0;
}

:focus {
    outline: 0;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100%;
}

img {
    border: 0;
    -ms-interpolation-mode: bicubic;
    vertical-align: middle;
}

legend {
    white-space: normal;
}

button,
input,
select,
textarea {
    font-size: 100%;
    margin: 0;
    max-width: 100%;
    vertical-align: baseline;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

button,
input {
    line-height: normal;
}

input,
textarea {
    background-image: -webkit-linear-gradient(hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0));
    /* Removing the inner shadow, rounded corners on iOS inputs */
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
    line-height: 1;
    cursor: pointer;
    /* Improves usability and consistency of cursor style between image-type 'input' and others */
    -webkit-appearance: button;
    /* Corrects inability to style clickable 'input' types in iOS */
    border: none;
}

input[type="checkbox"],
input[type="radio"] {
    padding: 0;
    /* Addresses excess padding in IE8/9 */
}

input[type="search"] {
    -webkit-appearance: textfield;
    /* Addresses appearance set to searchfield in S5, Chrome */
}

input[type="search"]::-webkit-search-decoration {
    /* Corrects inner padding displayed oddly in S5, Chrome on OSX */
    -webkit-appearance: none;
}

button::-moz-focus-inner,
input::-moz-focus-inner {
    /* Corrects inner padding and border displayed oddly in FF3/4 www.sitepen.com/blog/2008/05/14/the-devils-in-the-details-fixing-dojos-toolbar-buttons/ */
    border: 0;
    padding: 0;
}

*,
*:before,
*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

/* Repeatable Patterns
-------------------------------------------------------------- */
*,
*:before,
*:after {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

body {
    font: 14px/23px "Roboto", sans-serif;
    font-family: "Roboto";
    font-weight: 400;
    color: #1e1e1e;
}

a {
    text-decoration: none;
    color: #1e1e1e;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

a:hover,
a:focus {
    color: #3e76db;
    text-decoration: none;
    outline: 0;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

ul, ol {
    padding: 0;
}

img {
    max-width: 100%;
    height: auto;
}

b, strong {
    font-weight: 700;
}

button {
    border: none;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
    font-weight: bold;
    font-style: normal;
    text-align: center;
    color: #ffffff;
    width: 100%;
    height: 46px;
    line-height: 46px;
    border-radius: 4px;
    background: #FF1615;
    display: block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover,
button:hover i:before {
    background-color: #CA1015;
}

button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button:focus {
    border-color: transparent;
    outline: none;
}

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
input[type="email"] {
    position: relative;
    display: block;
    width: 100%;
    line-height: 24px;
    font-size: 13px;
    color: #000000;
    letter-spacing: 0;
    padding: 10px;
    line-height: 21px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    height: 42px;
    margin-bottom: 0px;
    -webkit-transition: all 0.3s ease-in-out;
    -moz-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}

textarea:focus,
input[type="text"]:focus,
input[type="password"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="time"]:focus,
input[type="week"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
input[type="tel"]:focus,
input[type="color"]:focus {
    border-color: #0066C2;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

textarea {
    width: 100%;
    padding: 10px 15px;
    height: 100px;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type="checkbox"] {
    display: inline;
}

textarea:-moz-placeholder,
textarea::-moz-placeholder,
input:-moz-placeholder,
input::-moz-placeholder {
    font-family: "Roboto";
    font-size: 14px;
    color: #333;
    opacity: 1;
}

input:-ms-input-placeholder {
    font-family: "Roboto";
    font-size: 14px;
    color: #333;
    opacity: 1;
}

textarea::-webkit-input-placeholder,
input::-webkit-input-placeholder {
    font-family: "Roboto";
    font-size: 14px;
    color: #333;
    opacity: 1;
    font-style: normal;
}

/* bootstrap resetting elements */
textarea,
input[type="text"],
input[type="submit"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"] {
    -webkit-appearance: none;
    text-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    -o-box-shadow: none;
    box-shadow: none;
    color: #000;
}

select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.select_form {
    position: relative;
}

.select_form:before {
    content: '';
    position: absolute;
    right: 10px;
    top: 15px;
    width: 15px;
    height: 15px;
    background: url(../img/arrow-1.svg) center center no-repeat;
    z-index: 5;
}

.select_form select {
    padding-right: 25px;
}

/* width */
::-webkit-scrollbar {
    width: 7px;
}

/* Track */
::-webkit-scrollbar-track {
    background: rgba(16, 27, 79, 0.1);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

p {
    margin-bottom: 15px;
}

p:last-of-type {
    margin-bottom: 0px;
}

h1, h2, h3, h4, h5, h6 {
    font-family: "Roboto", sans-serif;
    font-weight: 500;
    color: #1e1e1e;
}

h1 {
    font-size: 30px;
    line-height: 36px;
}

h2 {
    font-size: 24px;
    color: #333333;
    letter-spacing: 0;
    line-height: 32px;
}

h3 {
    font-size: 22px;
    line-height: 1.23;
}

h4 {
    font-size: 18px;
    line-height: 24px;
}

h5 {
    font-size: 16px;
    line-height: 24px;
}

h6 {
    font-size: 14px;
    line-height: 24px;
}

label {
    font-size: 14px;
    color: #4A4A4A;
    line-height: 20px;
    font-weight: 700;
    margin-bottom: 5px;
}

.container {
    max-width: 1440px;
}

.home .container {
    max-width: 1190px;
}

.boxed_sign_up {
    /*background: url(../img/bg-dangnhap.png) center bottom no-repeat;*/
    background-size: cover;
    height: 100vh;
}

.header_sign_up {
    padding: 25px;
    box-shadow: none;
}

.logo a img {
    width: 56px;
}

.sec_sign_up {
    width: 315px;
    margin: 10px auto 40px;
}

.sec_sign_up .title_sign_up {
    font-family: "iCielSamsungSharpSans";
    font-weight: bold;
    font-size: 24px;
    color: #000000;
    letter-spacing: 0;
    line-height: 32px;
    margin-bottom: 24px;
}

.form_sign_up .input_field {
    margin-bottom: 25px;
    position: relative;
}

.form_sign_up .input_field .dont_forget {
    font-size: 11px;
    color: #FF1615;
    letter-spacing: 0.2px;
    text-align: right;
    line-height: 18px;
    font-weight: 500;
    position: absolute;
    right: 0;
    bottom: 12px;
}

.form_sign_up .input_field label {
    font-family: "Roboto";
    font-weight: bold;
    font-size: 11px;
    color: #999999;
    letter-spacing: 0;
    line-height: 18px;
    margin-bottom: 0;
}

.form_sign_up .input_field input {
    border: none;
    border-bottom: 1px solid #E9E9E9;
    padding: 0;
}

.form_sign_up .input_field input:focus {
    border-color: #0066C2;
}

.sec_sign_up .sign_in_social a {
    display: block;
    text-align: center;
    padding: 14px;
    margin-top: 10px;
    font-weight: bold;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    border-radius: 4px;
}

.sec_sign_up .sign_in_social .facebook a {
    background: #3B5999;
    color: #fff;
}

.sec_sign_up .sign_in_social .google a {
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
}

.sec_sign_up .sign_in_social a:hover {
    opacity: 0.8;
}

.sec_sign_up .text_bottom {
    margin-top: 40px;
    font-size: 12px;
    color: #000000;
    letter-spacing: 0;
    text-align: center;
    line-height: 21px;
}

.sec_sign_up .text_bottom a {
    color: #FF1615;
    font-weight: bold;
}

.sec_sign_up .text_bottom a:hover {
    color: #CA1015;
}

.sec_sign_up .back {
    font-size: 15px;
    color: #000000;
    letter-spacing: 0;
    line-height: 21px;
    margin-bottom: 60px;
}

.sec_sign_up .back img {
    margin-right: 10px;
}

.boxed {
    min-height: 100vh;
}

.boxed .content_boxed {
    background-color: #f6f6f6;
    min-height: calc(100vh - 60px);
}

/* Header */
header {
    background: #FFFFFF;
    position: relative;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.13);
}

.header .header_wrap {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

#mainnav ul li {
    display: inline-block;
    margin: 0 28px;
}

#mainnav ul li a {
    display: block;
    position: relative;
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    line-height: 21px;
    font-weight: bold;
    line-height: 60px;
}

#mainnav ul li a:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 4px;
    background-color: #CA1015;
    transition: 0.3s;
}

#mainnav ul li a:hover:before,
#mainnav ul li.active a:before {
    left: 0;
    width: 100%;
}

.box_account_info .name_account img {
    width: 40px;
    margin-right: 7px;
}

/* header Style_2 */
header.style_2 {
    background-image: linear-gradient(-185deg, #DE1F25 0%, #CB1016 100%);
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.05);
}

header.style_2 .container {
    max-width: 1190px;
}

header.style_2 #mainnav ul li {
    margin: 0 20px;
}

header.style_2 #mainnav ul li a {
    color: #fff;
}

header.style_2 #mainnav ul li a:before {
    background-color: #fff;
}

header.style_2 #mainnav ul li.sign_in,
header.style_2 #mainnav ul li.sign_up {
    margin-right: 0;
}

header.style_2 #mainnav ul li.sign_up a,
header.style_2 #mainnav ul li.sign_in a {
    line-height: 44px;
    padding: 0 25px;
    border: 1px solid #fff;
    color: #DF1F25;
    background-color: #fff;
    border-radius: 4px;
}

header.style_2 #mainnav ul li.sign_in a {
    color: #fff;
    background: #FF1615;
}

header.style_2 #mainnav ul li.sign_up a:before,
header.style_2 #mainnav ul li.sign_in a:before {
    content: none;
}

/* Page Title */
.page_title {
    padding: 20px 0 16px;
}

.page_title .page_title_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

.page_title .page_title_container h1 a {
    display: inline-block;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    width: 39px;
    height: 46px;
    line-height: 42px;
    text-align: center;
    margin-right: 8px;
}

.page_title .permalink_page_title a {
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    padding: 8px 10px;
    display: inline-block;
    margin-left: 6px;
    font-size: 13px;
    color: #1969EF;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: 500;
}

.page_title .permalink_page_title a.add_new {
    color: #FF1615;
}

.page_title .permalink_page_title a img {
    margin-right: 5px;
}

/* Sec Portfolio */
.search_portfolio {
    margin-bottom: 10px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    padding: 0 15px;
    overflow: hidden;
}

.search_portfolio form .input_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

.search_portfolio form .input_field {
    padding: 10px 5px;
    width: 25%;
}

.search_portfolio form .input_field.search {
    width: 42%;
}

.search_portfolio form .input_field.portfolio_parent {
    width: 22%;
}

.search_portfolio form .input_field.date_time {
    width: 25%;
    border-right: 1px solid #e2e2e2;
}

.search_portfolio form .ico_time {
    position: relative;
}

.search_portfolio form .ico_time:before {
    /*content: '';*/
    position: absolute;
    width: 13px;
    height: 13px;
    background: url(../img/date.svg) center center no-repeat;
    background-size: cover;
    bottom: 25px;
    right: 12px;
    z-index: 5;
}

.search_portfolio form .input_field label {
    font-family: "SF Display";
    font-size: 11px;
    color: #999999;
    letter-spacing: 0;
    line-height: 15px;
    margin-bottom: 5px;
    font-weight: normal;
}

.search_portfolio form .input_field label .send_code {
    font-family: "SF Display";
    font-size: 11px;
    color: #CA1217;
    letter-spacing: 0;
    text-align: right;
    line-height: 15px;
    float: right;
}

.search_portfolio form .search_submit_portfolio {
    width: 150px;
    position: relative;
    margin-top: 19px;
}

/* .search_portfolio form .search_submit_portfolio:before {
  content: '';
  position: absolute;
  height: 90px;
  left: 5px;
  top: -50%;
  width: 1px;
  background-color: #E2E2E2;
} */
.search_portfolio form .search_submit_portfolio button {
    background-color: transparent;
    font-family: "SF Display";
    font-size: 13px;
    color: #1969EF;
    letter-spacing: 0;
    line-height: 18px;
}

.search_portfolio form .search_submit_portfolio button img {
    margin-right: 7px;
}

.sec_portfolio {
    padding-bottom: 30px;
}

.sec_portfolio .table_portfolio {
    border: 1px solid #E2E2E2;
}

.sec_portfolio .table_portfolio tr th {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
    font-weight: bold;
    padding: 12px 5px;
    background-color: #fff;
    border-bottom: 1px solid #E2E2E2;
    padding-left: 5px;
}

.sec_portfolio .table_portfolio tr th:last-child {
    text-align: center;
}

.sec_portfolio .table_portfolio tbody tr:nth-child(2n+1) td {
    background-color: #fff;
}

.sec_portfolio .table_portfolio tbody tr td {
    border-bottom: 1px solid #E2E2E2;
    padding: 11px 5px;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
}

.sec_portfolio .table_portfolio tbody tr td:first-child {
    width: 60px;
    text-align: center;
}

.sec_portfolio .table_portfolio tbody tr td:last-child {
    width: 90px;
    text-align: center;
}

.sec_portfolio .table_portfolio tbody tr td:last-child a:not(:last-child) {
    margin-right: 15px;
}

.search_portfolio.edit {
    padding: 0px;
}

.search_portfolio.edit form .input_container {
    padding: 5px 15px;
}

.search_portfolio.edit form .input_field.search {
    width: 75%;
}

.search_portfolio.edit form .input_field.portfolio_parent {
    width: 25%;
}

.search_portfolio.edit form .button_submit {
    text-align: center;
    padding: 12px 15px;
    margin: 0 -20px;
    border-top: 1px solid #E2E2E2;
}

.search_portfolio.edit form .button_submit a,
.search_portfolio.edit form .button_submit button {
    display: inline-block;
    margin: 0 3px;
    width: auto;
    padding: 14px 30px;
    border-radius: 4px;
    line-height: 18px;
}

.search_portfolio.edit form .button_submit a {
    border: 1px solid #E2E2E2;
    border-radius: 4px;
}

.search_portfolio.edit form .button_submit a:hover {
    background-color: #FF1615;
    color: #fff;
    border-color: #FF1615;
}

.popup {
    position: fixed;
    top: 200px;
    left: 50%;
    transform: translateX(-50%);
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    width: 70vw;
    max-width: 100%;
    opacity: 0;
    visibility: hidden;
    z-index: 1050;
    transition: 0.3s;
}

.popup.open {
    opacity: 1;
    visibility: visible;
    top: 30px;
}

.popup .popup_container .title_popup {
    position: relative;
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
    font-weight: bold;
    padding: 12px 20px;
}

.popup .popup_container .title_popup .close_popup {
    position: absolute;
    top: 12px;
    right: 20px;
    z-index: 99;
    cursor: pointer;
}

.popup .popup_container .content_popup {
    padding: 40px 15px;
    border-bottom: 1px solid #E2E2E2;
    border-top: 1px solid #E2E2E2;
}

.popup .popup_container .content_popup h6 {
    font-family: "SF Display";
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: normal;
    margin-bottom: 6px;
}

.popup .popup_container .content_popup p {
    font-family: "SF Display";
    font-size: 12px;
    color: #999999;
    letter-spacing: 0;
    line-height: 18px;
    margin-bottom: 2px;
}

.popup .popup_container .submit_popup {
    text-align: center;
    padding: 12px 15px;
}

.popup .popup_container .submit_popup a {
    display: inline-block;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    padding: 13px 30px;
    border-radius: 4px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    margin: 0 3px;
}

.popup .popup_container .submit_popup a.delete {
    color: #FFFFFF;
    font-weight: bold;
    background: #FF1615;
    border-color: #FF1615;
}

.popup .popup_container .submit_popup a:hover {
    background-color: #d60706;
    color: #fff;
}

.popup .popup_detail_product .content_popup {
    padding: 0;
}

.popup .popup_detail_product .content_popup .info_confirm {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    padding: 20px;
    border-bottom: 1px solid #e2e2e2;
}

.popup .popup_detail_product .content_popup .info_confirm h6 {
    font-family: "SF Display";
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
    margin-bottom: 6px;
}

.popup .popup_detail_product .content_popup .info_confirm p {
    font-family: "SF Display";
    font-size: 12px;
    color: #333;
    letter-spacing: 0;
    line-height: 18px;
}

.popup .popup_detail_product .content_popup .list_detail {
    padding-left: 40px;
    border-bottom: 1px solid #e2e2e2;
}

.popup .popup_detail_product .content_popup .list_detail li {
    padding: 20px 0;
}

.popup .popup_detail_product .content_popup .list_detail li:not(:last-child) {
    border-bottom: 1px solid #e2e2e2;
}

.popup .popup_detail_product .content_popup .list_detail li a {
    font-family: "SF Display";
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
}

.popup .popup_detail_product .submit_popup .confirm {
    background: #FF1615;
    border-color: #FF1615;
    color: #fff;
}

/* Pagination */
.pagination_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    padding: 12px 20px;
    border-top: none;
}

.pagination_container p {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
}

.pagination_container li {
    display: inline-block;
    margin: 3px;
}

.pagination_container li a {
    display: block;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 3px;
    width: 30px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
}

.pagination_container li a:hover,
.pagination_container li.active a {
    color: #fff;
    background: #FF1615;
    border-color: #FF1615;
}

/* Sec Income */
.sec_income .income_container {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    /* align-items: center; */
    align-content: center;
}

.sec_income .income_container .col_12 {
    width: 100%;
    padding-right: 10px;
}

.sec_income .income_container .col_8 {
    width: 72.5%;
    padding-right: 10px;
}

.sec_income .income_container .col_4 {
    width: 27.5%;
    padding-left: 10px;
}

.box_income {
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    margin-bottom: 20px;
}

.box_income .title_box {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    width: 100%;
    border-bottom: 1px solid #E2E2E2;
    padding: 11px 20px;
    font-family: "SF Display";
    font-weight: bold;
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
}

.box_income .title_box a {
    font-family: "SF Display";
    font-weight: normal;
    font-size: 17px;
    color: #1969EF;
    letter-spacing: 0;
    line-height: 23px;
}

.box_income .content_box .list_statement {
    padding: 15px 30px;
}

.box_income .content_box .list_statement li {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    padding: 19px 0;
    border-bottom: 1px solid #E2E2E2;
}

.box_income .content_box .list_statement li:last-child {
    border-bottom: none;
}

.box_income .content_box .list_statement li p {
    font-family: "SF Display";
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
}

.box_income.detail .content_box {
    padding: 20px;
}

.tab_container .menu_tab {
    margin-bottom: 16px;
}

.tab_container .menu_tab li {
    font-family: "SF Display";
    font-size: 15px;
    color: #999999;
    letter-spacing: 0;
    line-height: 23px;
    display: inline-block;
    margin-right: 30px;
    cursor: pointer;
    transition: 0.3s;
}

.tab_container .menu_tab li.active {
    color: #333333;
    font-weight: bold;
}

.box_income.detail .content_box .tab_container .inner_box .will_pay {
    padding: 10% 20px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    min-height: 316px;
}

.box_income.detail .content_box .tab_container .inner_box .will_pay p {
    font-family: "SF Display";
    font-size: 17px;
    color: #999999;
    letter-spacing: 0;
    line-height: 23px;
    margin-top: 10px;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table {
    border: 1px solid #E2E2E2;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table tr th {
    font-weight: bold;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
    padding: 12px 0;
    border-bottom: 1px solid #E2E2E2;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table tr:nth-child(2n+1) td {
    background: #F9F9F9;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table tr td {
    border-bottom: 1px solid #E2E2E2;
    padding: 10px 0;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table tr td .img_product {
    background-color: #fff;
    border: 1px solid #E2E2E2;
    border-radius: 3.22px;
    display: inline-block;
    padding: 3px;
    margin-right: 5px;
    float: left;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table tbody tr td:first-child {
    width: 65px;
    text-align: center;
}

.box_income.detail .content_box .tab_container .inner_box .payed_container table thead tr th:first-child {
    text-align: center;
}

.box_income.general .content_box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    align-content: center;
}

.box_income.general .content_box .general_will_pay {
    width: 33.333%;
    padding: 30px;
}

.box_income.general .content_box .general_payed {
    width: 66.667%;
    padding: 30px;
    border-left: 1px solid #E2E2E2;
}

.box_income.general .content_box .general_payed .general_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    align-content: center;
}

.box_income.general .content_box .general_payed .general_container .inner_box {
    width: 50%;
}

.box_income.general .content_box h5 {
    font-family: "SF Display";
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
    font-weight: bold;
    margin-bottom: 10px;
}

.box_income.general .content_box .general_container .tl {
    font-family: "SF Display";
    font-size: 15px;
    color: #999999;
    letter-spacing: 0;
    line-height: 20px;
    margin-bottom: 8px;
}

.box_income.general .content_box .general_container .price {
    font-family: "SF Display";
    font-weight: bold;
    font-size: 26px;
    color: #333333;
    letter-spacing: 0;
    line-height: 35px;
    margin-bottom: 10px;
}

.box_income.general .content_box .general_container .total_price {
    color: #1969EF;
}

/* Sec Message */
.sec_message .message_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    align-content: center;
}

.sec_message .message_container .box_chat {
    width: calc(100% - 300px);
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
}

.box_chat .title_box {
    padding: 20px 30px;
    display: flex;
    vertical-align: middle;
    align-items: center;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 500;
    border-bottom: 1px solid #E2E2E2;
}

.box_chat .title_box img {
    box-shadow: 0 6px 8px 0 rgba(16, 27, 79, 0.20);
    display: inline-block;
    width: 40px;
    margin-right: 15px;
    border-radius: 50%;
}

.box_chat .content_box .content_chat {
    padding: 30px;
    overflow-y: scroll;
}

.box_chat .content_box .content_chat ul {
    height: calc(100vh - 380px);
}

.box_chat .content_box .content_chat ul li:not(:last-child) {
    margin-bottom: 50px;
}

.box_chat .content_box .content_chat ul li.customer {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    align-content: stretch;
}

.box_chat .content_box .content_chat ul li.host + li.host {
    margin-top: -30px;
}

.box_chat .content_box .content_chat ul li.customer + li.customer {
    margin-top: -30px;
}

.box_chat .content_box .content_chat ul li.host {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;;
    align-content: stretch;
}

.box_chat .content_box .content_chat ul li .img_chat {
    box-shadow: 0 6px 8px 0 rgba(16, 27, 79, 0.20);
    width: 40px;
    border-radius: 50%;
}

.box_chat .content_box .content_chat ul li .text_chat {
    max-width: 640px;
    padding: 10px 25px;
    margin-left: 18px;
    margin-right: 25px;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    font-weight: 500;
    background: #c5c5c5;
    border-radius: 0px 20px 20px 20px;
    margin-bottom: 10px;
}

.box_chat .content_box .content_chat ul li .time_chat {
    font-size: 12px;
    color: #9B9B9B;
    letter-spacing: 0;
}

.box_chat .content_box .content_chat ul li.host .text_chat {
    background: #ff1614;
    border-radius: 20px 0px 20px 20px;
}

.box_chat .content_box .content_chat ul li .text_chat.waiting {
    background: rgba(31, 202, 116, 0.1);
}

.box_chat .content_box .content_chat ul li .text_chat.waiting img {
    width: 50px;
}

.sec_message .message_container .list_chat_item {
    width: 300px;
    background: #EDEDED;
}

.form_enter_chat form {
    position: relative;
}

.form_enter_chat form input {
    background: #FFFFFF;
    border: none;
    border-top: 1px solid #E2E2E2;
    border-radius: 0;
    height: 80px;
    opacity: 0.99;
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    padding: 10px 60px 10px 20px;
}

.form_enter_chat form input:focus {
    border-color: #E2E2E2;
}

.form_enter_chat .send_enter_chat {
    position: absolute;
    width: auto;
    top: 15px;
    background-color: transparent;
    right: 6px;
}

.list_chat_item .search_message {
    padding: 20px;
}

.list_chat_item .search_message .input_field {
    position: relative;
}

.list_chat_item .search_message .input_field input {
    background: rgba(16, 27, 79, 0.05);
    border-radius: 6px;
    padding: 5px 5px 5px 35px;
    font-weight: 500;
    font-size: 12px;
    color: rgba(16, 27, 79, 0.5);
    letter-spacing: 0;
}

.list_chat_item .search_message .input_field input::placeholder {
    font-size: 12px;
    color: rgba(16, 27, 79, 0.5);
}

.list_chat_item .search_message .input_field button {
    position: absolute;
    top: 0;
    background-color: transparent;
    left: 0px;
    width: 35px;
    height: auto;
    line-height: 40px;
}

.list_chat_item .search_message .input_field button img {
    width: 15px;
}

.list_chat_container {
    padding: 0px 10px 10px;
    overflow-y: scroll;
}

.list_chat_container ul {
    height: calc(100vh - 250px);
}

.list_chat_container .chat_item {
    /* border-bottom: 1px solid ; */
    position: relative;
}

.list_chat_container .chat_item:before {
    content: '';
    position: absolute;
    bottom: 0;
    right: 10px;
    left: 10px;
    height: 1px;
    background-color: rgba(16, 27, 79, 0.1);
}

.list_chat_container .chat_item a {
    display: block;
    border-radius: 10px;
    padding: 20px 10px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    align-content: stretch;
}

.list_chat_container .chat_item a:hover,
.list_chat_container .chat_item a.active {
    background: #FF1615 !important;
    box-shadow: 0 5px 15px 0 rgba(16, 27, 79, 0.15);
}

.list_chat_container .chat_item a .img {
    box-shadow: 0 6px 8px 0 rgba(16, 27, 79, 0.20);
    width: 40px;
    border-radius: 50%;
}

.list_chat_container .chat_item a .content_chat_item {
    width: calc(100% - 40px);
    padding-left: 10px;
}

.list_chat_container .chat_item a .content_chat_item .name_chat {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    align-content: stretch;
    margin-bottom: 5px;
}

.list_chat_container .chat_item a .content_chat_item .name_chat .name {
    font-size: 13px;
    color: #101B4F;
    letter-spacing: 0;
    font-weight: 500;
    transition: 0.3s;
}

.list_chat_container .chat_item a .content_chat_item .name_chat .time_chat {
    font-size: 12px;
    color: #101B4F;
    letter-spacing: 0;
    text-align: right;
    color: rgba(16, 27, 79, 0.4);
    font-weight: 500;
    transition: 0.3s;
}

.list_chat_container .chat_item a .content_chat_item .text_chat {
    font-size: 12px;
    color: #9B9B9B;
    letter-spacing: 0;
    font-weight: 500;
    line-height: 20px;
    transition: 0.3s;
}

.list_chat_container .chat_item a:hover .content_chat_item .name_chat .name,
.list_chat_container .chat_item a:hover .content_chat_item .name_chat .time_chat,
.list_chat_container .chat_item a:hover .content_chat_item .text_chat,
.list_chat_container .chat_item a.active .content_chat_item .name_chat .name,
.list_chat_container .chat_item a.active .content_chat_item .name_chat .time_chat,
.list_chat_container .chat_item a.active .content_chat_item .text_chat {
    color: #fff;
}

.table_schedule .symptom {
    width: 35%;
}

.table_schedule .note {
    width: 20%;
}

.status span {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 17px;
    font-weight: bold;
    background: #FF1615;
    border-radius: 15.5px;
    padding: 6px;
    width: 109px;
    text-align: center;
}

.status span.confirm {
    background: #0CD216;
}

.status span.cancle {
    background: #AAAAAA;
}

.status span.wait_product {
    background: #EFB719;
}

.status span.delivery_product {
    background: #1969EF;
}

.status span.deliveried_product {
    background: #0CD216;
}

.status span.no_process {
    color: #333333;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
}

.status span.stocking {
    background: #fff;
    color: #1969EF;
    border: 1px solid #E2E2E2;
}

.form_schedule form .schedule_field {
    width: 100%;
    padding: 10px 0;
}

.form_schedule form .row {
    margin: 0 -5px;
}

.form_schedule form .row .col-md-3,
.form_schedule form .row .col-md-9,
.form_schedule form .row .col-md-12,
.form_schedule form .row .col-md-6,
.form_schedule form .row .col-md-4,
.form_schedule form .row .col-md-8 {
    padding: 0 10px;
}

.form_schedule.edit form .button_submit {
    margin-top: 12px;
}

.form_schedule.edit form input:disabled,
.form_schedule.edit form select:disabled,
.form_schedule.edit form textarea:disabled {
    background: #EDEDED;
}

.form_schedule form .schedule_field textarea {
    height: 120px;
}

.search_portfolio.edit form .button_submit .confirm_schedule_form {
    background: #0CD216;
    color: #fff;
    border-color: #0CD216;
}

.search_portfolio.edit form .button_submit .confirmed_schedule_form {
    background: #BFBFBF;
    color: #fff;
    border-color: #BFBFBF;
}

.search_portfolio.edit form .button_submit .edit_schedule_form {
    background: #1969EF;
    color: #fff;
    border-color: #1969EF;
}

.table_product tr td .img_product {
    background-color: #fff;
    border: 1px solid #E2E2E2;
    border-radius: 3.22px;
    display: inline-block;
    padding: 3px;
    margin-right: 5px;
    float: left;
}

.upload_img_product .upload_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: center;
    align-content: center;
}

.upload_img_product .upload_container .upfile {
    position: relative;
    width: 170px;
    border: 1px dashed #1969EF;
    text-align: center;
    border-radius: 4px;
    padding: 65.5px 0;
    align-items: center;
    margin-right: 20px;
}

.upload_img_product .upload_container .upfile:last-child {
    margin-right: 0;
}

.upload_img_product .upload_container .upfile label {
    cursor: pointer;
    margin-bottom: 0;
}

.upload_img_product .upload_container .upfile label:before {
    content: '';
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0;
    top: 0;
    background-color: transparent;
    z-index: 9;
}

.upload_img_product .upload_container .upfile input[type="file"] {
    position: absolute;
    opacity: 0;
    visibility: hidden;
    z-index: -1;
    left: 0;
}

.upload_img_product .upload_container.img_cover .upfile.file_uploaded {
    width: auto;
    padding: 0;
    border: none;
}

.upload_img_product .upload_container.img_child .upfile.file_uploaded {
    padding: 0;
    border: 1px solid #E2E2E2;
}

.box_account_info .name_account {
    position: relative;
}

.box_account_info .name_account > a {
    display: block;
    position: relative;
    padding-right: 20px;
    height: 40px;
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.box_account_info .name_account > a:before {
    content: '';
    position: absolute;
    border: solid black;
    border-width: 0 1px 1px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg) translateY(-50%);
    -webkit-transform: rotate(45deg) translateY(-50%);
    right: 3px;
    top: 42%;
    /*margin-top: -6px;*/
    transition: 0.3s;
}

.box_account_info .name_account > a.active:before {
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
}

.box_account_info .name_account .menu_account_info {
    position: absolute;
    width: 212px;
    right: 0;
    top: 150%;
    border: 1px solid #E2E2E2;
    z-index: 7;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
}

.box_account_info .name_account .menu_account_info.open {
    opacity: 1;
    visibility: visible;
    top: 126%;
}

.box_account_info .name_account .menu_account_info:before {
    content: '';
    position: absolute;
    height: 15px;
    width: 15px;
    transform: rotate(45deg);
    top: -6px;
    left: 50%;
    z-index: -1;
    border-top: 1px solid #e2e2e2;
    border-left: 1px solid #e2e2e2;
}

.box_account_info .name_account .menu_account_info li a {
    display: block;
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    background: #FFFFFF;
    border-bottom: 1px solid #E2E2E2;
    padding: 11px 15px;
}

.box_account_info .name_account .menu_account_info li a img {
    width: 14px;
    margin-right: 10px;
}

.box_account_info .name_account .menu_account_info li a:hover,
.box_account_info .name_account .menu_account_info li.active a {
    background: #F4F4F4;
}

.search_portfolio .tab_container .menu_tab {
    padding: 12px 20px;
    margin-bottom: 8px;
    border-bottom: 1px solid #E2E2E2;
}

.search_portfolio .tab_container .menu_tab li {
    font-family: "SF Display";
    font-size: 17px;
    color: #999999;
    letter-spacing: 0;
    line-height: 23px;
    position: relative;
}

.search_portfolio .tab_container .menu_tab li:before {
    content: '';
    position: absolute;
    height: 2px;
    background: #CA1015;
    width: 0;
    left: 50%;
    bottom: -14px;
    transition: 0.3s;
}

.search_portfolio .tab_container .menu_tab li.active:before,
.search_portfolio .tab_container .menu_tab li:hover:before {
    width: 100%;
    left: 0;
}

.search_portfolio .tab_container .menu_tab li.active,
.search_portfolio .tab_container .menu_tab li:hover {
    color: #333;
    font-weight: bold;
}

/* Banking Container */
.banking_container .card_banking {
    padding: 20px;
    border-bottom: 1px solid #E2E2E2;
}

.banking_container .title_box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: flex-start;
    align-content: center;
    margin-bottom: 15px;
}

.banking_container .title_box h3 {
    font-family: "SF Display";
    font-weight: bold;
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
}

.banking_container .title_box a.add_banking {
    font-size: 15px;
    color: #FF1615;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: 500;
    display: inline-block;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    padding: 13px 20px;
}

.banking_container .title_box a.add_banking:hover {
    background-color: #ececec;
}

.banking_container .title_box a.add_banking img {
    margin-right: 5px;
}

.banking_container .no_card {
    padding: 7% 0;
}

.banking_container .no_card p {
    font-family: "SF Display";
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
}

.banking_container .account_banking {
    padding: 20px;
}

.banking_container .account_banking .content_box .account_item {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    align-content: center;
}

.banking_container .account_banking .content_box .account_item > div:not(:last-child) {
    margin-right: 30px;
}

.banking_container .account_banking .content_box .account_item .info_banking h3 {
    font-size: 15px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 500;
    margin-bottom: 10px;
}

.banking_container .account_banking .content_box .account_item .info_banking ul li {
    font-size: 13px;
    color: #999999;
    letter-spacing: 0;
}

.banking_container .account_banking .content_box .account_item .status_banking {
    line-height: 18px;
}

.banking_container .account_banking .content_box .account_item .status_banking span {
    display: inline-block;
    font-size: 13px;
    color: #055ABF;
    letter-spacing: 0;
}

.banking_container .account_banking .content_box .account_item .delete_banking a {
    font-size: 13px;
    color: #999999;
}

.banking_container .account_banking .content_box .account_item .delete_banking img {
    margin-right: 12px;
}

/* sec_product */
.sec_product {
    padding-bottom: 30px;
}

.status_order_product {
    padding: 10px 20px;
    background: #08BCA6;
    color: #fff;
    margin-bottom: 20px;
}

.status_order_product span {
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 17px;
}

.status_order_product .status_order {
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 17px;
    margin-right: 40px;
}

.status_order_product .status_order img {
    margin-right: 5px;
    width: 24px;
}

.info_order_product {
    margin-bottom: 18px;
}

.info_order_product .info_order_product_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: center;
    margin: 0 -10px;
}

.info_order_product .info_order_product_container .col_12 {
    width: 100%;
    padding: 0 10px;
}

.info_order_product .info_order_product_container .col_3 {
    width: 25%;
    padding: 0 10px;
}

.info_order_product .info_order_product_container .col_4 {
    width: 33%;
    padding: 0 10px;
}

.info_order_product .info_order_product_container .col_6 {
    width: 50%;
    padding: 0 10px;
}

.box_info_order {
    background-color: #fff;
    border: 1px solid #E2E2E2;
    height: 100%;
}

.box_info_order .title_box {
    padding: 11px 20px;
    border-bottom: 1px solid #E2E2E2;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: center;
}

.box_info_order .title_box h6 {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
    width: 100%;
    font-weight: bold;
}

.box_info_order .title_box h6 img {
    max-width: 19px;
    margin-right: 5px;
}

.box_info_order .title_box a {
    font-size: 13px;
    color: #1969EF;
    letter-spacing: 0;
    text-align: right;
    line-height: 17px;
    width: 100px;
    text-align: right;
}

.box_info_order .content_box {
    padding: 15px 20px;
    font-family: "SF Display";
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
}

.box_info_order .content_box ul li {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: center;
}

.box_info_order .content_box p {
    margin-bottom: 5px;
}

.box_info_order .content_box .not_fn {
    color: #a2a2a2;
}

.box_info_order .content_box ul li:not(:last-child) {
    margin-bottom: 12px;
}

.box_info_order .content_box .code_order {
    color: #08BCA6;
}

.box_info_order .content_box .datetime {
    color: #a2a2a2;
}

.box_info_order .content_box ul li .chat {
    font-family: "SF Display";
    font-size: 13px;
    color: #1969EF;
    letter-spacing: 0;
    text-align: right;
    line-height: 18px;
}

.detail_order_container .more {
    margin-top: 20px;
}

.detail_order_container .more a {
    background: #FF1615;
    border-radius: 4px;
    display: inline-block;
    font-size: 13px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 18px;
    font-weight: bold;
    padding: 14px 30px;;
}

.detail_order_container .more a:hover {
    background-color: #CA1015;
}

.detail_order_container .title_box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: center;
    padding: 12px 20px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-bottom: none;
}

.detail_order_container .title_box h6 {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
    font-weight: bold;
}

.detail_order_container .title_box .code_product {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 17px;
}

.detail_order_container .content_box {
    padding: 20px;
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
}

.detail_order_container .info_product_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: center;
}

.detail_order_container .info_product_container_image {
    display: flex;
    flex-direction: row;
    align-items: stretch;
    align-content: center;
}

.detail_order_container .info_product_container .image_product {
    width: 190px;
    padding-right: 20px;
}

.detail_order_container .info_product_container .image_product figure {
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 11.21px;
    overflow: hidden;
    text-align: center;
}

.detail_order_container .info_product_container .text_product {
    width: calc(100% - 190px);
    padding-left: 20px;
}

.detail_order_container .info_product_container .text_product .name_product {
    font-size: 19px;
    color: #333333;
    letter-spacing: 0;
    line-height: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.detail_order_container .info_product_container .text_product .price_product {
    font-size: 15px;
    color: #FF1615;
    letter-spacing: 0;
    line-height: 18px;
    font-weight: bold;
}

.detail_order_container .info_product_container .text_product ul.list_detail_child {
    margin: 20px 0;
}

.detail_order_container .info_product_container .text_product ul.list_detail_child li {
    display: inline-block;
}

.detail_order_container .info_product_container .text_product ul.list_detail_child li:not(:last-child) {
    margin-right: 10%;
}

.detail_order_container .info_product_container .text_product ul.list_detail_child li h6,
.detail_order_container .info_product_container .text_product .desc h6 {
    font-size: 11px;
    color: #999999;
    letter-spacing: 0;
    line-height: 15px;
    margin-bottom: 6px;
}

.detail_order_container .info_product_container .text_product ul.list_detail_child li p {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
}

.detail_order_container .info_product_container .text_product .desc {
    font-family: "SF Display";
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    line-height: 18px;
}

/* sec_calendar */
.sec_calendar {
    padding-bottom: 30px;
}

.calendar_container {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: stretch;
    align-content: stretch;
    background-color: #fff;
}

.calendar_container .medical_schedule {
    width: 252px;
    z-index: 5;
    border: 1px solid #D3DCE5;
    box-shadow: 9px 0 16px 0 rgba(0, 0, 0, 0.06);
    border-radius: 3px 0px 0px 3px;
}

.calendar_container .medical_schedule .title_box {
    padding: 16px 20px;
    border-bottom: 1px solid #D3DCE5;
}

.calendar_container .medical_schedule .title_box h5,
.calendar_schedule .title_box h5 {
    font-size: 17px;
    color: #333333;
    text-align: left;
    line-height: 23px;
}

.list_medical .customer_medical {
    padding: 15px;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    border-bottom: 1px solid #D2D6E0;
}

.list_medical .customer_medical .image_customer {
    width: 40px;
    margin-right: 10px;
    border-radius: 50%;
}

.list_medical .customer_medical .info_customer {
    width: calc(100% - 50px);
}

.list_medical .customer_medical .info_customer .name {
    font-size: 13px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 500;
    line-height: 18px;
    margin-bottom: 2px;
}

.list_medical .customer_medical .info_customer .time {
    font-size: 12px;
    color: #999999;
    letter-spacing: 0;
    line-height: 16px;
}

.calendar_container .calendar_schedule {
    width: calc(100% - 252px);
    border: 1px solid #D3DCE5;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.06);
    border-radius: 0px 3px 3px 0px;
    border-left: none;
}

.calendar_schedule .title_box {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
    padding: 9.5px 20px;
    border-bottom: 1px solid #D2D6E0;
}

.calendar_schedule .choose_calendar {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

.calendar_schedule .choose_calendar .pagination_calendar {
    margin-right: 12px;
    border-radius: 3px;
    overflow: hidden;
}

.calendar_schedule .choose_calendar .pagination_calendar a {
    display: block;
    width: 36px;
    height: 36px;
    text-align: center;
    line-height: 36px;
    border: 1px solid #D2D6E0;
    float: left;
}

.calendar_schedule .choose_calendar .pagination_calendar a:not(:last-child) {
    margin-right: -1px;
}

.calendar_schedule .choose_calendar .choose_year select {
    border: 1px solid #D2D6E0;
    border-radius: 3px;
    height: 36px;
    width: 83px;
    padding: 0 12px;
}

.calendar_schedule .choose_calendar .choose_year:before {
    right: 5px;
    top: 12px;
}

.calendar_schedule .choose_calendar .choose_datetime {
    margin-left: 10px;
    overflow: hidden;
    border-radius: 3px;
}

.calendar_schedule .choose_calendar .choose_datetime ul li {
    float: left;
}

.calendar_schedule .choose_calendar .choose_datetime ul li a {
    display: block;
    font-size: 13px;
    color: #333333;
    text-align: left;
    line-height: 18px;
    padding: 7.5px 14px;
    border: 1px solid #D2D6E0;
}

.calendar_schedule .choose_calendar .choose_datetime ul li:not(:last-child) a {
    margin-right: -1px;
}

.calendar_schedule .choose_calendar .choose_datetime ul li.active a,
.calendar_schedule .choose_calendar .choose_datetime ul li a:hover {
    background: #CA1015;
    border: 1px solid #CA1015;
    color: #fff;
}

.table_calendar {
    padding: 20px;
}

.table_calendar {
    padding-left: 58px;
}

.table_calendar table tr {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: stretch;
    align-content: center;
    position: relative;
}

.table_calendar table tr th .day_week {
    font-size: 11px;
    color: #333333;
    line-height: 15px;
    display: block;
    text-align: left;
    margin-bottom: 0;
}

.table_calendar table tr th .daytime {
    font-size: 20px;
    color: #333333;
    line-height: 26px;
    display: block;
    text-align: left;
}

.table_calendar table tr th,
.table_calendar table tr td {
    border-bottom: 1px solid #D2D6E0;
    min-height: 48px;
}

.table_calendar table tr th:first-child,
.table_calendar table tr td:first-child {
    width: 20px !important;
    border-right: 1px solid #D2D6E0;
}

.table_calendar.day table tr td:last-child,
.table_calendar.day table tr th:last-child {
    width: calc(100% - 20px);
}

.table_calendar table tr td:last-child {
    padding: 0 9.5px;
}

.table_calendar.day table tr th:last-child {
    padding: 10px 20px;
}

.table_calendar table tr td .oclock {
    position: absolute;
    top: -11px;
    left: -40px;
}

.schedule_item {
    padding: 4px 8px 4px 22px;
    background: #0CBD15;
    border: 1px solid #0CBD15;
    border-radius: 3px;
    color: #fff;
    display: inline-block;
    margin-bottom: 7px;
    width: 32.33%;
}

.schedule_item {
    margin-right: 1%;
}

.schedule_item .name {
    font-size: 11px;
    color: #FFFFFF;
    line-height: 16px;
    font-weight: bold;
    position: relative;
}

.schedule_item .name:before {
    content: '';
    position: absolute;
    height: 6px;
    width: 6px;
    background-color: #fff;
    border-radius: 50%;
    left: -12px;
    top: 5px;
}

.schedule_item .date_time {
    line-height: 16px;
}

.schedule_item .date_time span {
    font-size: 11px;
    color: #FFFFFF;
    line-height: 16px;
    display: inline-block;
    line-height: 16px;
}

.schedule_item .date_time .pet {
    margin-left: 18px;
}

.schedule_item.wait-work {
    background: orange;
    border: 1px solid orange;
}

.table_calendar.week table tr th,
.table_calendar.week table tr td {
    width: calc((100% - 20px) / 7);
    border-right: 1px solid #D2D6E0;
}

.table_calendar.week table tr th {
    border-right: 1px solid #D2D6E0;
}

.table_calendar table tr th .day_week,
.table_calendar table tr th .daytime {
    text-align: center;
}

.table_calendar.week table tr td,
.table_calendar.week table tr th:last-child {
    padding: 0 2px;
}

.table_calendar.week table tr td .schedule_item {
    width: 100%;
    margin-right: 0;
}

.table_calendar.month {
    padding: 20px;
}

.table_calendar.month table tr th:first-child,
.table_calendar.month table tr td:first-child {
    width: calc(100% / 7) !important;
    border-right: 1px solid #fff;
}

.table_calendar.month table tr th,
.table_calendar.month table tr td {
    width: calc(100% / 7);
    padding-right: 0;
    margin-right: 10px;
    border-top: 1px solid #D2D6E0;
    border-bottom: none;
}

.table_calendar.month table tr th {
    padding: 6px 0;
    min-height: auto;
    border-right: 1px solid #fff;
    border-top: none;
}

.table_calendar.month table tr td {
    min-height: 96px;
    padding-top: 25px;
    position: relative;
}

.table_calendar.month table tr th .day_week {
    text-align: right;
}

.table_calendar.month table tr td .schedule_item {
    width: 100%;
    margin-right: 0px;
}

.table_calendar.month table tr td .numb_day {
    position: absolute;
    top: 5px;
    right: 0;
    font-size: 15px;
    color: #333333;
    text-align: right;
    line-height: 17px;
}

.table_calendar.month table tr td .numb_day.not_month {
    color: #9D9D9D;
}

.table_calendar.month.year table tr td {
    width: 33.333% !important;
    min-height: 130px;
}

.table_calendar.month.year table tr td .schedule_item {
    width: 49%;
}

/* Sec Banner */
.sec_banner {
    padding: 12% 0 20%;
    background: url(../img/bg-1.png) center center no-repeat;
    background-size: cover;
}

.sec_banner .banner_text .title_box {
    margin-bottom: 45px;
}

.sec_banner .banner_text .title_box span {
    font-family: "iCielSamsungSharpSans";
    font-size: 36px;
    color: #333333;
    letter-spacing: 0;
    line-height: 61px;
}

.sec_banner .banner_text .title_box h1 {
    font-family: "iCielSamsungSharpSans";
    font-size: 60px;
    color: #fff;
    letter-spacing: 0;
    line-height: 79px;
}

.sec_banner .banner_text .content_text p {
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
    margin-bottom: 7px;
}

.sec_banner .banner_text .content_text .more {
    margin-top: 28px;
}

.sec_banner .banner_text .content_text .more a {
    display: inline-block;
    font-size: 15px;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 20px;
    font-weight: bold;
    background: #FF1615;
    border: 1px solid #FFFFFF;
    border-radius: 4px;
    padding: 13px 25px;
}

.sec_banner.style_2 {
    background-image: url(../img/bg-3.png);
}

/* Sec Function */
.sec_function {
    padding: 60px 0 70px;
    background: url(../img/bg-2.png) center center no-repeat;
    background-size: cover;
}

.sec_function .title_box {
    color: #fff;
    width: 52%;
    margin: 0 auto 30px;
}

.sec_function .title_box h2 {
    font-family: "iCielSamsungSharpSans";
    font-size: 36px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 48px;
    margin-bottom: 14px;
}

.sec_function .title_box p {
    font-size: 17px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 26px;
}

.tab_container.style_2 .menu_tab {
    text-align: center;
    margin-bottom: 40px;
}

.tab_container.style_2 .menu_tab li {
    background: #FFFFFF;
    border-radius: 4px;
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 23px;
    font-weight: bold;
    padding: 8px 20px;
    margin: 0 8px;
    font-family: "Roboto";
}

.tab_container.style_2 .menu_tab li img {
    margin-right: 6px;
}

.tab_container.style_2 .menu_tab li.active,
.tab_container.style_2 .menu_tab li:hover {
    color: #DE1E24;
}

.sec_function.style_2 {
    padding: 80px 0;
    background-image: url(../img/bg-4.png);
}

.sec_function.style_2 .title_box p,
.sec_function.style_2 .title_box h2 {
    color: #333333;
}

.sec_function.style_2 .contact_form .input_field {
    width: 533px;
    margin: 0 auto;
    position: relative;
}

.sec_function.style_2 .contact_form .input_field input {
    background: #FFFFFF;
    border: 1px solid #E2E2E2;
    border-radius: 4px;
    height: 70px;
    width: 100%;
    padding: 25px 30px;
    padding-right: 150px;
    font-size: 17px;
    color: #999999;
    letter-spacing: 0;
    line-height: 20px;
}

.sec_function.style_2 .contact_form .input_field button {
    width: 130px;
    line-height: 70px;
    height: 70px;
    position: absolute;
    top: 0;
    right: 0;
}

/* Footer */
footer .footer_container {
    border-top: 1px solid #E2E2E2;
    padding: 19px 0;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    align-content: center;
}

footer .footer_container .menu_ft li {
    display: inline-block;
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 26px;
}

footer .footer_container .menu_ft li:not(:last-child) {
    margin-right: 40px;
}

footer .footer_container .social_ft span {
    display: inline-block;
    margin-right: 40px;
    font-size: 17px;
    color: #333333;
    letter-spacing: 0;
    line-height: 26px;
}

footer .footer_container .social_ft ul,
footer .footer_container .social_ft ul li {
    display: inline-block;
}

footer .footer_container .social_ft ul li:not(:last-child) {
    margin-right: 13.5px;
}


/* Preloader
-------------------------------------------------------------- */
#preloader {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    overflow: visible;
    background: #fff;
    display: table;
    text-align: center;
}

.loader {
    display: table-cell;
    vertical-align: middle;
    height: 100%;
    width: 100%;
    position: relative;
    width: 200px;
    height: 200px;
}

.loader-icon {
    width: 80px;
    height: 80px;
    border: 2px solid #e01b22;
    border-right-color: #eee;
    border-radius: 50%;
    position: relative;
    animation: loader-rotate 1s linear infinite;
    margin: 0 auto;
}

@keyframes loader-rotate {
    0% {
        transform: rotate(0);
    }

    100% {
        transform: rotate(360deg);
    }
}

a.payment {
    background: #1969ef;
    color: #fff;
}

a.confirmed_schedule_form {
    background: red;
    color: #fff;
}

td .badge {
    font-size: 100%;
}

label.form-check-label {
    font-size: 13px!important;
    margin-bottom: 0!important;
    cursor: pointer;
}


.social-btns {
    display: flex;
}

.flex {
    display: flex;
    align-items: center;
    justify-content: center;
}

.app-btn {
    width: 45%;
    max-width: 160px;
    color: #fff;
    margin: 20px 10px;
    text-align: left;
    border-radius: 5px;
    text-decoration: none;
    font-family: "Lucida Grande", sans-serif;
    font-size: 10px;
    text-transform: uppercase;
}
.app-btn.blu {
    background-color: #101010;
    transition: background-color 0.25s linear;
}
.app-btn.blu:hover {
    background-color: #454545;
    color: #fff;
}
.app-btn i {
    width: 20%;
    text-align: center;
    font-size: 28px;
    margin-right: 7px;
}
.app-btn .big-txt {
    font-size: 17px;
    text-transform: capitalize;
}
