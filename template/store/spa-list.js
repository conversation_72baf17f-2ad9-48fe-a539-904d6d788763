let products = {
    1: [],
    0: [],
    3: [],
};
let productShows = {
    1: [],
    0: [],
    3: [],
};

let currentPages = {
    1: 1,
    0: 1,
    3: 1,
};

let itemsPerPage = 15;
let indexTable = 1

let categoryIds = {
    1: 'all',
    0: 'all',
    3: 'all',
};

let statusTable = {
    0: 'all',
    1: 'all',
    2: 'all',
};

let currentPage= {
    1:1,
    0:1,
    3:1
}

let totalPages = {
    1: 1,
    0: 1,
    3: 1,
};


function renderTable(status) {

    console.log('status')
    console.log(status)
    console.log(productShows[status])
    console.log('productShows')
    console.log('currentPages ', currentPages)
    let html = '';
    let start = (currentPages[status] - 1) * itemsPerPage;
    let end = currentPages[status] * itemsPerPage - 1;
    console.log('currentPages[status]: ' + currentPages[status])
    console.log('itemsPerPage: ' + itemsPerPage)
    console.log('start: ' + start)
    console.log('end: ' + end)
    if (end > productShows[status].length - 1) {
        end = productShows[status].length
    }

    console.log('start: ' + start)
    console.log('end: ' + end)

    for (let i = start; i <= end; i++) {
        if (productShows[status][i]) {
            let product = productShows[status][i];
            console.log('product: ',  product);
            let button = '';
            if (status == 1 || status == 3) {
                button = `<a id="${product._id}" href="javascript:;" class="hide-product" title="Ẩn">
                        <img src="/template/ui/img/delete.svg" alt="">
                    </a>`
            } else if (status == 0) {
                button = `<a href="javascript:;" class="show-product" title="Hiển thị">
                        <img src="/template/ui/img/check.svg" alt="">
                    </a>`
            }
            html += `
            <tr id="${product._id}">
                <td>${i + 1}</td>
                <td>${
                product.isFeatured ? `<i class="fa fa-star main-color badge-featured mr-2" aria-hidden="true" title="Dịch vụ nổi bật" data-service-id="${product._id}" data-service-name="${product.name}" data-service-featured="${product.isFeatured}"></i>`:
                    `<i class="fa fa-star fa-star-o main-color badge-featured mr-2" aria-hidden="true" title="Dịch vụ không nổi bật" data-service-id="${product._id}" data-service-name="${product.name}" data-service-featured="${product.isFeatured}"></i>`
            }${product.name}
                </td>
                <td>${product.categoryName}</td>
                <td>${product.storeName}</td>
                <td>${numberFormat(product.price)} VNĐ</td>
                <td class="status"><span class="${product.typeService == 1 ? 'stocking' : 'out_stock'}">${product.typeService == 1 ? 'Sẵn sàng!' : 'Tạm dừng'}</span></td>
                <td>
                    <a href="/store/edit-spa-wash/${product._id}.html" class="edit-product" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    ${button}
                </td>
            </tr>
            `
        }
    }
    $('.sp' + status + ' ' + '.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${productShows[status].length} kết quả`)
    $('.sp' + status + ' ' + '.table_portfolio .tbody-table').html(html);
    renderPagination(status)
}

function renderPagination(status) {
    let min = 1;
    let max = 1
    let html = '';
    $('.sp' + status + ' ' + ".pagination_container ul").html('');
    html += `<li status="${status}" class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li status="${status}" class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages[status]) {
        min = 1;
        max = totalPages[status]
    } else if (Number(currentPages[status]) + 2 > Number(totalPages[status])) {
        max = totalPages[status];
        min = totalPages[status] - 4
    } else if (Number(currentPages[status]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPages[status]) - 2;
        max = Number(currentPages[status]) + 2
    }
    if (min == 2) {
        html += `<li status="${status}" class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li status="${status}" class="page ${i == Number(currentPages[status]) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[status] - 2) {
        html += `<li status="${status}" class="page ${totalPages[status] == Number(currentPages[status]) ? 'active' : ''}"  value="${totalPages[status]}"><a href="javascript:;" >${totalPages[status]}</a></li>`
    }
    html += `<li status="${status}" class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`;
    html += `<li status="${status}" class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`;
    $('.sp' + status + ' ' + '.pagination_container .pagination-list').html(html);
}

function filterList(status) {
    currentPages[status] = 1;
    let data1s = [];
    categoryId = $('.sp' + status + ' .categoryId').val();
    if (categoryId == 'all') {
        data1s = products[status];
    } else {
        products[status].forEach(product => {
            if (product.categoryId == categoryId) {
                data1s.push(product)
            }
        })
    }
    let data2s = [];
    statusTable = $('.sp' + status + ' .status-product').val();
    if (statusTable == 'all') {
        data2s = data1s
    } else {
        data1s.forEach(product => {
            if (product.typeService == statusTable) {
                data2s.push(product)
            }
        })
    }
    let key = removeUtf8($('.sp' + status + ' .search-goods').val().trim())
    let data3s = [];
    data2s.forEach(item => {
        if (removeUtf8(item.name).includes(key) || removeUtf8('SP' + item.code.toString()).includes(key)) {
            data3s.push(item)
        }
    });
    productShows[status] = data3s;
    totalPages[status] = Math.ceil(productShows[status].length / itemsPerPage);
    renderTable(status)
}


function getProducts(status) {
    get(`/store/get-service.html/${status}`, {}, ({data}) => {
        productShows[status] = data.products;
        products[status] = data.products;
        totalPages[status] = Math.ceil(data.products.length / itemsPerPage);
        renderTable(status);
    })
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault();
        let status = Number($(this).attr('status'));
        if (currentPages[status] > 1) {
            currentPages[status] = currentPages[status] - 1;
            renderTable(status);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault();
        let status = Number($(this).attr('status'));
        currentPages[status] = 1;
        renderTable(status);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault();
        let status = Number($(this).attr('status'));
        currentPages[status] = totalPages[status];
        renderTable(status);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault();
        let status = Number($(this).attr('status'));
        if (Number(currentPages[status]) < Number(totalPages[status])) {
            currentPage = currentPage + 1;
            renderTable(status);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let status = Number($(this).attr('status'));
        currentPage[status] = e.currentTarget.value;
        renderTable(status);
    });

    $(document).on('click', '.hide-product', function (e) {
        let conf = confirm('Bạn có chắc muốn ẩn sản phẩm này?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/update-status-spa/${id}.html?status=0`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                    getProducts(1);
                    getProducts(0);
                    getProducts(3);
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.show-product', function (e) {
        let conf = confirm('Bạn có chắc muốn hiển thị sản phẩm?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/update-status-spa/${id}.html?status=1`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                    getProducts(1);
                    getProducts(0);
                    getProducts(3);
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.menu_tab li', function (e) {
        indexTable = Number($(this).attr('index'))
        console.log('indexTable', indexTable)
        getProducts(indexTable)
    })

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-san-pham.html`, {
            products: products[indexTable],
            indexTable:indexTable,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

    $(document).on('click', 'body .badge-featured', function (e) {
        e.preventDefault();
        const { serviceId, serviceName, serviceFeatured } = e.target.dataset;
        console.log('data on click, ', JSON.stringify(e.target.dataset))
        const ask = confirm(`Bạn có chắc chắn đặt ${serviceName} thành ${serviceFeatured == 'true' ? '"Không nổi bật"': '"Nổi bật"'} không?`);
        if (ask) {
            post('/rest/v1/service/toggle-featured', {serviceId}, (res) => {
                if (res && !res.error) {
                    if (serviceFeatured == 'true') {
                        e.target.setAttribute('data-service-featured', 'false');
                    } else {
                        e.target.setAttribute('data-service-featured', 'true');
                    }
                    if (e.target.classList.contains('fa-star')) {
                        e.target.classList.toggle('fa-star-o');
                    } else {
                        e.target.classList.toggle('fa-star');
                    }
                }
            }, (error) => {
                console.log(error);
            })
        }
    });

    getProducts(1);
});

