let products = {
    1: [],
    2: [],
    3: [],
};
let productShows = {
    1: [],
    2: [],
    3: [],
};

let currentPages = {
    1: 1,
    2: 1,
    3: 1,
};

let itemsPerPage = 15;
let indexTable = 1

// Function để lấy tab từ URL
function getTabFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    return tab ? parseInt(tab) : 1; // Default là tab 1
}

// Function để cập nhật URL với tab hiện tại
function updateUrlWithTab(tabIndex) {
    const url = new URL(window.location);
    url.searchParams.set('tab', tabIndex);
    window.history.replaceState({}, '', url);
}

let categoryIds = {
    1: 'all',
    2: 'all',
    3: 'all',
};

let statusTable = {
    1: 'all',
    2: 'all',
    3: 'all',
};
let totalPages = {
    1: 1,
    2: 1,
    3: 1,
};


function renderTable(status) {
    let html = '';
    let start = (currentPages[status] - 1) * itemsPerPage;
    let end = currentPages[status] * itemsPerPage - 1;
    if (end > productShows[status].length - 1) {
        end = productShows[status].length - 1
    }
    for (let i = start; i <= end; i++) {
        if (productShows[status][i]) {
            let product = productShows[status][i];
            let button = '';

            let productThumb = product.thumbail;
            if (productThumb) {
                productThumb = productThumb.indexOf('/') == 0 ? productThumb.substring(1) : productThumb;
            }

            if (status == 1 || status == 3) {
                button = `<a id="${product._id}" href="javascript:;" class="hide-product" title="Ẩn sản phẩm">
                        <img src="/template/ui/img/delete.svg" alt="">
                    </a>`
            } else if (status == 2) {
                button = `<a href="javascript:;" class="show-product" title="Hiển thị sản phẩm">
                        <img src="/template/ui/img/check.svg" alt="">
                    </a>`
            }
            html += `
            <tr id="${product._id}">
                <td>${i + 1}</td>
                <td>SP${product.code}</td>
                <td><span class="img_product"><img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${encodeURIComponent(productThumb)}" alt="" style="max-width: 40px;"></span> ${product.name}</td>
                <td>${product.storeName}</td>
                <td>${product.categoryName}</td>
                <td>${numberFormat(product.price)} VNĐ</td>
                <td class="status"><span class="${product.typeProduct == 1 ? 'stocking' : 'out_stock'}">${product.typeProduct == 1 ? 'Còn hàng' : 'Hết hàng'}</span></td>
                <td>
                    <a href="/store/edit-product/${product._id}.html" class="edit-product" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    ${button}
                </td>
            </tr>
            `
        }
    }
    $('.sp' + status + ' ' + '.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end + 1} trong tổng số ${productShows[status].length} kết quả`)
    $('.sp' + status + ' ' + '.table_product .tbody-table').html(html);
    get_image_dropbox();
    renderPagination(status)
}

function renderPagination(status) {
    let min = 1;
    let max = 1
    let html = '';
    $('.sp' + status + ' ' + ".pagination_container ul").html('');
    html += `<li status="${status}" class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li status="${status}" class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages[status]) {
        min = 1;
        max = totalPages[status]
    } else if (Number(currentPages[status]) + 2 > Number(totalPages[status])) {
        max = totalPages[status];
        min = totalPages[status] - 4
    } else if (Number(currentPages[status]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPages[status]) - 2;
        max = Number(currentPages[status]) + 2
    }
    if (min == 2) {
        html += `<li status="${status}" class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li status="${status}" class="page ${i == Number(currentPages[status]) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages[status] - 2) {
        html += `<li status="${status}" class="page ${totalPages[status] == Number(currentPages[status]) ? 'active' : ''}"  value="${totalPages[status]}"><a href="javascript:;" >${totalPages[status]}</a></li>`
    }
    html += `<li status="${status}" class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`;
    html += `<li status="${status}" class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`;
    $('.sp' + status + ' ' + '.pagination_container .pagination-list').html(html);
}

function filterList(status) {
    currentPages[status] = 1;
    let data1s = [];
    let categoryId = $('.sp' + status + ' .categoryId').val();
    if (categoryId == 'all') {
        data1s = products[status];
    } else {
        products[status].forEach(product => {
            if (product.categoryId == categoryId) {
                data1s.push(product)
            }
        })
    }
    let data2s = [];
    let statusTable = $('.sp' + status + ' .status-product').val();
    if (statusTable == 'all') {
        data2s = data1s
    } else {
        data1s.forEach(product => {
            if (product.typeProduct == statusTable) {
                data2s.push(product)
            }
        })
    }
    let key = removeUtf8($('.sp' + status + ' .search-goods').val().trim())
    let data3s = [];
    data2s.forEach(item => {
        if (removeUtf8(item.name).includes(key) || removeUtf8('SP' + item.code.toString()).includes(key)) {
            data3s.push(item)
        }
    });
    productShows[status] = data3s;
    totalPages[status] = Math.ceil(productShows[status].length / itemsPerPage);
    renderTable(status)
}


function getProducts(status) {
    get(`/store/tai-san-pham.html/${status}`, {}, ({data}) => {
        productShows[status] = data.products;
        products[status] = data.products;
        totalPages[status] = Math.ceil(data.products.length / itemsPerPage);
        renderTable(status);
    })
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let status = Number($(this).attr('status'));
        if (currentPages[status] > 1) {
            currentPages[status] = currentPages[status] - 1;
            renderTable(status);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let status = Number($(this).attr('status'));
        currentPages[status] = 1;
        renderTable(status);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let status = Number($(this).attr('status'));
        currentPages[status] = totalPages[status];
        renderTable(status);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let status = Number($(this).attr('status'));
        if (Number(currentPages[status]) < Number(totalPages[status])) {
            currentPages[status] = currentPages[status] + 1;
            renderTable(status);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let status = Number($(this).attr('status'));
        currentPages[status] = e.currentTarget.value;
        renderTable(status);
    });

    $(document).on('click', '.hide-product', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let conf = confirm('Bạn có chắc muốn ẩn sản phẩm này?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/hide-product/${id}.html`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.show-product', function (e) {
        e.preventDefault();
        e.stopPropagation();
        let conf = confirm('Bạn có chắc muốn hiển thị sản phẩm?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/show-product/${id}.html`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.menu_tab li', function (e) {
        e.preventDefault();
        e.stopPropagation();
        indexTable = Number($(this).attr('index'))
        updateUrlWithTab(indexTable) // Cập nhật URL với tab hiện tại
        getProducts(indexTable)
    })

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-san-pham.html`, {
            products: products[indexTable],
            indexTable:indexTable,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

    // Khởi tạo tab từ URL hoặc mặc định tab 1
    indexTable = getTabFromUrl();

    // Cập nhật active tab trong UI
    $('.menu_tab li').removeClass('active');
    $('.menu_tab li[index="' + indexTable + '"]').addClass('active');

    // Hiển thị content tab tương ứng
    $('.content_tab .inner_box').hide();
    $('.content_tab .sp' + indexTable).show();

    // Load data cho tab hiện tại
    getProducts(indexTable);
});

