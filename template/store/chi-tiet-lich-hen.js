function successSchedule(element) {
    let conf = confirm('Bạn có chắc muốn đồng ý lịch hẹn này?');
    if (conf) {
        post(`${$(element).attr('url')}`, {}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

function successDoneScheduleViewBox(element) {
    if (dataBook.type) {
        $('#successDoneScheduleViewBox').addClass('open');
        $('body').prepend('<div class="modal-backdrop fade show"></div>');
    } else {
        if (!confirm("Bạn chắc chắn đã hoàn thành dịch vụ này")) return;
        post(`/store/change-status-schedule-done/${dataBook._id}.html?status=3&index=${dataBook.type}`, {price: Number(dataBook.datePrice * dataBook.dateTime * dataBook.countRooms)}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }

}

function cancelScheduleViewBox() {
    $('#cancelScheduleViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

function guiLenhTuChoi() {
    let message = $('#lyDoTuChoi').val().trim();
    if (message == '') {
        alert("Vui lòng gửi lý do");
    } else {
        if (!confirm("Bạn chắc chắn muốn từ chối lịch hẹn này? Sau khi từ chối thì bạn sẽ không thể hoàn tác thao tác này")) return;
        post(`/store/change-status-schedule/${dataBook._id}.html?status=2&index=${dataBook.type}`, {message}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

function xacNhanHoanThanh() {
    let priceAddition = $('#priceAddition').val().trim().replace("VND", '').split(",").join("").trim();
    if (priceAddition == '' || isNaN(Number(priceAddition))) {
        alert("Vui lòng nhập phụ thu. Nếu không thì nhập 0");
    } else {
        if (!confirm("Bạn chắc chắn đã hoàn thành dịch vụ này")) return;
        post(`/store/change-status-schedule-done/${dataBook._id}.html?status=3&index=${dataBook.type}`, {priceAddition}, (response) => {
            if (!response.error) {
                location.reload()
            } else {
                displayError(response.message);
            }
        })
    }
}

function xacNhanThanhToan() {
    if (!confirm("Bạn chắc chắn chuyển đơn này thành đã thanh toán")) return;
    post(`/store/api/change-order-status/${dataBook.type}/${dataBook._id}?status=3&isPayOnline=1`, {}, (response) => {
        if (!response.error) {
            location.reload()
        } else {
            displayError(response.message);
        }
    })
}

$(function () {
    $('#gia').on('keydown', function (e) {
        if (e.which == 13) {
            e.preventDefault();
            xacNhanHoanThanh();
        }
    });
});
