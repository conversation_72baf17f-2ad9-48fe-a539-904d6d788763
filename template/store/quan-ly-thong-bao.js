var notifications

function renderNotification() {
    let html = ''
    notifications.forEach(item => {
        let title = '';
        switch (item.type) {
            case 0:
                title = `<span class="name">${item.userName}</span> đã đặt hàng một đơn hàng mới - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chinh-sua-don-hang/${item.requestId}.html">Xem chi tiết</a>`
                break;
            case 1:
                title = `<span class="name">${item.title}</span>
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chi-tiet-lich-hen/${item.bookId}.html?index=${item.typeService}">Xem chi tiết</a>`
                break;
            case 6:
                title = `<span class="name">${item.userName}</span> đã hủy đơn hàng với lý do "${item.message}" - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chinh-sua-don-hang/${item.requestId}.html">Xem chi tiết</a>`
                break;
            case 7:
                title = `<span class="name">${item.userName}</span> đã hủy lịch hẹn với lý do "${item.message}" - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chi-tiet-lich-hen/${item.bookId}.html?index=${item.typeService}">Xem chi tiết</a>`
                break;
            case 8:
                title = `<span class="name">${item.userName}</span> đã nhận hàng - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chi-tiet-lich-hen/${item.bookId}.html?index=${item.typeService}">Xem chi tiết</a>`
                break;
            case 9:
                title = `<span class="name">Admin</span> đã xác nhận trả hàng cho đơn hàng - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chinh-sua-don-hang/${item.requestId}.html">Xem chi tiết</a>`
                break;
            case 10:
                item.avatarUser = '/template/ui/img/logo.png';
                title = `<span class="name">langnuoibienvandon.com</span> thông báo phòng sắp đến hạn trả - 
                <a style="color: #2196F3; cursor: pointer;" target="_blank" href="/store/chi-tiet-lich-hen/${item.bookId}.html?index=${item.typeService}">Xem chi tiết</a>`
                break;
            case 12:
                item.avatarUser = '/template/ui/img/logo.png';
                title = `<span class="name">langnuoibienvandon.com</span> thông báo <span class="name">${item.title}</span> - Nội dung "${item.message}"`
                break;
        }
        html += `
            <li class="customer_medical ${item.watched == 0 ? 'no-watch' : ''}" onclick="watchedNotification('${item._id}')" id="${item._id}">
                <div class="image_customer">
                    <img style="border-radius: 50%;" src="${item.avatarUser}" alt="">
                </div>
                <div class="info_customer">
                    ${title}
                    <p class="time">${moment(Number(item.createAt)).format('HH:mm DD/MM/YYYY')}</p>
                </div>
            </li>
        `
    })
    if (notifications.length > 0) {
        $('.list_medical').show()
        $('.no-notification').hide()
    } else {
        $('.list_medical').hide()
        $('.no-notification').show()
    }
    $('.medical_schedule .list_medical').html(html)
}

function watchedNotification(notificationId) {
    notifications.forEach(item => {
        if (item.watched == 0 && item._id == notificationId) {
            item.watched = 1;
            $(`#${notificationId}`).removeClass('no-watch')
            get(`/da-xem-thong-bao/${notificationId}.html`, {}, (res) => {
            })
        }
    })
}

function resetCountNotifications() {
    get('/user/api/reset-count-notification.html', {}, (res) => {
        get('/lay-du-lieu-thong-bao.html', {}, (res) => {
            if (!res.error) {
                notifications = res.data.notifications
                renderNotification()
            }
        })
    });
    $('#number-notification').hide()
    $('#number-notification').text(0)
}

resetCountNotifications();

