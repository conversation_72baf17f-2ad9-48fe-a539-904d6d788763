function renderListCity() {
    let html = '<option value="">Chọn Tỉnh / Thành phố</option>'
    for (let key in countrys) {
        html += `
    <option value="${key}">${key}</option>
    `
    }
    $('#city-state').html(html);

    let valDefault = $('#city-state').attr('value');
    if (valDefault) $('#city-state').val(valDefault).change();
}
jQuery.datetimepicker.setLocale('vi');
jQuery('.dateTimePicker').datetimepicker({
    format: 'H:i d/m/Y',
});

var myDropzone;

$(() => {
    setup("upload-images");

    var editor = CKEDITOR.replace('content', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 300
    });
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    });

    $('#form-add-service').on('submit', (e) => {
        let formData = new FormData();
        e.preventDefault();
        let data = $('#form-add-service').serializeArray();
        data.forEach(item => {
            formData.append(item.name, item.value);
        });

        myDropzone.files.forEach(file => {
            delete file.previewElement
            delete file.previewTemplate
            delete file.status
            delete file.upload
            delete file.width
            delete file.height
            delete file.accepted
            delete file.processing
            formData.append(`pictures`, file)
        })

        formData.append('content', editor.getData());
        formData.append('thumbnail', $(`#upload-thumbnail`).prop('files')[0])
        ajaxFile('/store/them-thuong-hieu.html', formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/store/thuong-hieu.html'
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })

    $(document).on('change', '#city-state', function (e) {
        let city = $(this).val()
        let html = '<option value="">Chọn Quận / Huyện</option>'
        for (let key in countrys[city]) {
            html += `
            <option value="${key}">${key}</option>
            `
        }
        $('#district-state').html(html);

        let valDefault = $('#district-state').attr('value');
        if (valDefault) $('#district-state').val(valDefault).change();
    });

    $(document).on('change', '#district-state', function (e) {
        let huyen = $(this).val()
        let html = '<option value="">Chọn Phường / Xã</option>'
        let city = $('#city-state').val()
        if (city && huyen && countrys[city]) {
            countrys[city][huyen].forEach(item => {
                html += `
            <option value="${item}">${item}</option>
            `
            })
        }
        $('#ward-state').html(html);

        let valDefault = $('#ward-state').attr('value');
        if (valDefault) $('#ward-state').val(valDefault).change();
    });

    renderListCity()
})


Dropzone.autoDiscover = false;

function setup(id) {

    myDropzone = new Dropzone(`#${id}`, {
        url: '/upload',
        parallelUploads: 2,
        filesizeBase: 1000,
        thumbnailHeight: 210,
        thumbnailWidth: 140,
        maxFilesize: 1024 * 3,
        maxFiles: 10,
        dictResponseError: "Server not Configured",
        dictFileTooBig: "Kích thước file quá lớn ({{filesize}}MB). Cần nhỏ hơn {{maxFilesize}}MB.",
        dictCancelUpload: "",
        acceptedFiles: ".png,.jpg,.jpeg",
        previewTemplate: `<div class="dz-preview dz-file-preview">
                                    <div class="dz-image"><img data-dz-thumbnail /></div>
                                    <div class="dz-error-message"><i class="fa fa-warning">&nbsp;</i><span data-dz-errormessage></span></div>
                                    <div class="dz-filename"><span data-dz-name></span></div>
                                    <div class="dz-progress">
                                        <span class="dz-upload" data-dz-uploadprogress></span>
                                    </div>
                                    <div class="dz-remove">
                                        <a href="javascript:undefined;" data-dz-remove=""><i class="fa fa-trash-o"></i>&nbsp;<span>Xóa</span></div>
                                </div>`,
        thumbnail: function(file, dataUrl) {
            if (file.previewElement) {
                file.previewElement.classList.remove("dz-file-preview");
                var images = file.previewElement.querySelectorAll("[data-dz-thumbnail]");
                for (var i = 0; i < images.length; i++) {
                    var thumbnailElement = images[i];
                    thumbnailElement.alt = file.name;
                    thumbnailElement.src = dataUrl;
                }
                setTimeout(function() { file.previewElement.classList.add("dz-image-preview"); }, 1);
            }
        }

    });


    // Now fake the file upload, since GitHub does not handle file uploads
    // and returns a 404

    var minSteps = 6,
        maxSteps = 60,
        timeBetweenSteps = 100,
        bytesPerStep = 100000;

    myDropzone.uploadFiles = function(files) {
        var self = this;

        for (var i = 0; i < files.length; i++) {

            var file = files[i];
            totalSteps = Math.round(Math.min(maxSteps, Math.max(minSteps, file.size / bytesPerStep)));

            for (var step = 0; step < totalSteps; step++) {
                var duration = timeBetweenSteps * (step + 1);
                setTimeout(function(file, totalSteps, step) {
                    return function() {
                        file.upload = {
                            progress: 100 * (step + 1) / totalSteps,
                            total: file.size,
                            bytesSent: (step + 1) * file.size / totalSteps
                        };

                        self.emit('uploadprogress', file, file.upload.progress, file.upload.bytesSent);
                        if (file.upload.progress == 100) {
                            file.status = Dropzone.SUCCESS;
                            self.emit("success", file, 'success', null);
                            self.emit("complete", file);
                            self.processQueue();
                            //document.getElementsByClassName("dz-success-mark").style.opacity = "1";
                        }
                    };
                }(file, totalSteps, step), duration);
            }
        }
    }
}

function submitForm() {
    $('#form-add-service').submit()
}
