function addClassify() {
    let count = document.querySelectorAll('.classify-item').length
    $('#classify-list').append(`
        <div class="col-md-12 classify-item" id="classify-item-${count + 1}">
            <div class="input_field schedule_field">
                <label for="" class="title-classify">Nhóm ${count + 1}</label>
                    <div class="row type-product">
                    <label for="">Tên nhóm </label>
                    <div class="col-md-12">
                        <div class="input_field schedule_field">
                            <input placeholder="Tên nhóm" name="name-type" type="text">
                        </div>
                    </div>
                    <label for="">Nhóm con</label>
                    <div style="width: 100%;" class="type-list">
                        <div class="col-md-12 type-item">
                            <div class="input_field schedule_field">
                                <input name="nameProd" placeholder="Nhập phân loại dịch vụ" type="text" class="mr-3">
                                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency">                               
                                <a href="javascript:;" class="delete-service" title="Xoá" onclick="deleteTypeItem(this)">
                                    <img src="/template/ui/img/delete-2-bage.svg" alt="">
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input_field schedule_field">
                            <a class="button-classify" style="background: white;border: solid 2px #8db7ff; color: #8db7ff;" onclick="addType(this)"><img src="/template/ui/img/plus-nhat.svg" alt=""> Thêm</a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input_field schedule_field">
                            <a class="button-classify" style="background: white;border: solid 2px #FF1615; color: #FF1615;" onclick="deleteClassify(this)"><img src="/template/ui/img/delete-2-bage-red.svg" alt=""> Xóa</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `)
}

function deleteTypeItem(element) {
    $(element).parents('.type-item').remove()
}

function addType(element) {
    $(element).parents('.classify-item').find('.type-list').append(`                       
        <div class="col-md-12 type-item">
            <div class="input_field schedule_field">
                <input name="nameProd" placeholder="Nhập phân loại dịch vụ" type="text" class="mr-3">
                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency">
                <a href="javascript:;" class="delete-service" title="Xoá" onclick="deleteTypeItem(this)">
                    <img src="/template/ui/img/delete-2-bage.svg" alt="">
                </a>
            </div>
        </div>
        `)
}

function deleteClassify(element) {
    $(element).parents('.classify-item').remove()
    document.querySelectorAll('.classify-item').forEach((item, index) => {
        item.querySelector('.title-classify').innerHTML = `Nhóm ${index + 1}`
    })
}

var myDropzone;

$(() => {
    setup("upload-images");

    var editor = CKEDITOR.replace('description', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 300
    });

    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    })

    $('#form-add-service').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-service').serializeArray();
        let formData = new FormData();
        data.forEach(item => {
            if (item.name == 'price') {
                item.value = item.value.replace("VND", '').split(",").join("").trim();
            }
            if (item.name != 'description')
            {
                formData.append(item.name, item.value);
            }
        })

        myDropzone.files.forEach(file => {
            delete file.previewElement
            delete file.previewTemplate
            delete file.status
            delete file.upload
            delete file.width
            delete file.height
            delete file.accepted
            delete file.processing
            formData.append(`pictures`, file)
        })

        let classify = []
        document.querySelectorAll('.classify-item').forEach(item => {
            if (item.querySelector('input[name="name-type"]').value.trim() != '') {
                let newClassify = {
                    name: item.querySelector('input[name="name-type"]').value.trim(),
                    data: []
                }
                item.querySelectorAll('.type-item').forEach(item2 => {
                    const classifyData = {};
                    if (item2.querySelector('input[name="nameProd"]')) {
                        classifyData.name = item2.querySelector('input[name="nameProd"]').value.trim();
                    }
                    if (item2.querySelector('input[name="sellingPrice"]')) {
                        classifyData.price = item2.querySelector('input[name="sellingPrice"]').value.trim();
                    }
                    newClassify.data.push(classifyData)
                })
                classify.push(newClassify)
            }
        })
        formData.append(`classify`, JSON.stringify(classify))
        formData.append(`thumbail`, $(`#upload-service-0`).prop('files')[0])
        formData.append('description', editor.getData());
        ajaxFile('/store/create-parking.html', formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/store/mng-parking.html'
                }, 1000)
            } else {
                displayError(res.message);
            }
        })
    })

    /**
     * Chọn cửa hàng thì lấy tất cả cơ sở cửa hàng đó
     */
    $('select[name="branchId"]').select2({
        multiple: true,
        // allowClear: true,
        theme: "bootstrap4",
        width: 'resolve',
        placeholder: '[[---------Chọn cơ sở --------]]',
        data: []
    });
    $('select[name="storeId"]').on('change', function(event) {
        const selectedStoreId = event.target.value;
        $.ajax({
            url: `/rest/v1/branches?storeId=${selectedStoreId}`,
            data: {},
            type: 'get',
            success: function (response) {
                $('select[name="branchId"]').select2({
                    multiple: true,
                    allowClear: true,
                    theme: "bootstrap4",
                    width: 'resolve',
                    placeholder: '[[---------Chọn cơ sở --------]]',
                    data: response.branches.map(branch => ({id: branch._id, text: branch.name})),
                }).val(response.branches.map(branch => branch._id)).trigger('change')
            },
            error: function (err) {

            }
        })
    })
})


Dropzone.autoDiscover = false;

function setup(id) {
    console.log("Loading dropzone", id)
    myDropzone = new Dropzone(`#${id}`, {
        url: '/upload',
        parallelUploads: 2,
        filesizeBase: 1000,
        thumbnailHeight: 210,
        thumbnailWidth: 140,
        maxFilesize: 1024 * 3,
        maxFiles: 10,
        dictResponseError: "Server not Configured",
        dictFileTooBig: "Kích thước file quá lớn ({{filesize}}MB). Cần nhỏ hơn {{maxFilesize}}MB.",
        dictCancelUpload: "",
        acceptedFiles: ".png,.jpg,.jpeg",
        previewTemplate: `<div class="dz-preview dz-file-preview">
                                    <div class="dz-image"><img data-dz-thumbnail /></div>
                                    <div class="dz-error-message"><i class="fa fa-warning">&nbsp;</i><span data-dz-errormessage></span></div>
                                    <div class="dz-filename"><span data-dz-name></span></div>
                                    <div class="dz-progress">
                                        <span class="dz-upload" data-dz-uploadprogress></span>
                                    </div>
                                    <div class="dz-remove">
                                        <a href="javascript:undefined;" data-dz-remove=""><i class="fa fa-trash-o"></i>&nbsp;<span>Xóa</span></div>
                                </div>`,
        thumbnail: function(file, dataUrl) {
            if (file.previewElement) {
                file.previewElement.classList.remove("dz-file-preview");
                var images = file.previewElement.querySelectorAll("[data-dz-thumbnail]");
                for (var i = 0; i < images.length; i++) {
                    var thumbnailElement = images[i];
                    thumbnailElement.alt = file.name;
                    thumbnailElement.src = dataUrl;
                }
                setTimeout(function() { file.previewElement.classList.add("dz-image-preview"); }, 1);
            }
        }

    });


    // Now fake the file upload, since GitHub does not handle file uploads
    // and returns a 404

    var minSteps = 6,
        maxSteps = 60,
        timeBetweenSteps = 100,
        bytesPerStep = 100000;

    myDropzone.uploadFiles = function(files) {
        var self = this;

        for (var i = 0; i < files.length; i++) {

            var file = files[i];
            totalSteps = Math.round(Math.min(maxSteps, Math.max(minSteps, file.size / bytesPerStep)));

            for (var step = 0; step < totalSteps; step++) {
                var duration = timeBetweenSteps * (step + 1);
                setTimeout(function(file, totalSteps, step) {
                    return function() {
                        file.upload = {
                            progress: 100 * (step + 1) / totalSteps,
                            total: file.size,
                            bytesSent: (step + 1) * file.size / totalSteps
                        };

                        self.emit('uploadprogress', file, file.upload.progress, file.upload.bytesSent);
                        if (file.upload.progress == 100) {
                            file.status = Dropzone.SUCCESS;
                            self.emit("success", file, 'success', null);
                            self.emit("complete", file);
                            self.processQueue();
                            //document.getElementsByClassName("dz-success-mark").style.opacity = "1";
                        }
                    };
                }(file, totalSteps, step), duration);
            }
        }
    }
}

function submitForm() {
    $('#form-add-service').submit()
}