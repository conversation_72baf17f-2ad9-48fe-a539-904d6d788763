let coupons = {
    1: [],
    2: [],
};
let currentPages = {
    1: 1,
    2: 1,
};

let totalPagesOb = {
    1: 1,
    2: 1,
};

let itemsPerPage = 15;

function renderTable(index) {
    let html = '';
    let start = (currentPages[index] - 1) * itemsPerPage;
    let end = currentPages[index] * itemsPerPage - 1
    if (end > coupons[index].length - 1) {
        end = coupons[index].length
    }
    for (let i = start; i <= end; i++) {
        if (coupons[index][i]) {
            let coupon = coupons[index][i];
            let buttonHtml = '';
            if (coupon.status == 1) {
                buttonHtml = `<a href="javascript:;" id="${coupon._id}" class="close-coupon" title="Tắt mã coupon">
                    <img src="/template/ui/img/delete.svg" alt="">
                </a>`
            } else {
                buttonHtml = `<a href="javascript:;" id="${coupon._id}" class="open-coupon" title="Mở mã coupon">
                    <img src="/template/ui/img/check.svg" alt="">
                </a> `
            }
            html += `
            <tr id="${coupon._id}">
                <td>${i + 1}</td>
                <td>${coupon.code}</td>
                <td>${coupon.startTime == -1 ? 'Không giới hạn' : moment(coupon.startTime).format('HH:mm DD/MM/YYYY')}</td>
                <td>${coupon.endTime == -1 ? 'Không giới hạn' : moment(coupon.endTime).format('HH:mm DD/MM/YYYY')}</td>
                <td>${coupon.countBooking == -1 ? 'Không giới hạn' : coupon.countBooking}</td>
                <td>${coupon.currentBooking}</td>
                <td>${numberFormat(coupon.minBillValue)} VND</td>
                <td>${moment(coupon.createAt).format('HH:mm DD/MM/YYYY')}</td>
                <td>${coupon.type==1? '% giá trị đơn hàng': 'Tiền mặt'}</td>
                <td>${coupon.type==1? coupon.value+'%': numberFormat(coupon.value)+' VND'}</td>
                <td>
                    ${buttonHtml}
                    <a href="/store/sua-coupon.html/${coupon._id}" id="${coupon._id}" class="edit-coupon" title="Sửa coupon">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                </td>
            </tr>
            `
        }
    }
    $('.table_coupon' + index + ' .pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${coupons[index].length} kết quả`)
    $('.table_coupon' + index + ' .table_portfolio .tbody-table').html(html);
    renderPagination(index)
}

function renderPagination(index) {
    let totalPages = totalPagesOb[index];
    let min = 1;
    let max = 1
    let html = '';
    $('.table_coupon' + index + " .pagination_container ul").html('');
    html += `<li class="first-page" index='${index}'><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>`
    html += `<li class="prev-page" index='${index}'><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>`
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPages[index]) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPages[index]) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPages[index]) - 2;
        max = Number(currentPages[index]) + 2
    }
    if (min == 2) {
        html += `<li class="page" index='${index}' value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li index='${index}' class="page ${i == Number(currentPages[index]) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page index='${index}' ${totalPages == Number(currentPages[index]) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += `<li class="next-page" index='${index}'><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>`
    html += `<li class="last-page" index='${index}'><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>`
    $('.table_coupon' + index + ' .pagination_container .pagination-list').html(html);
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault();
        let index = Number($(this).attr('index'));
        let currentPage = currentPages[index];
        if (currentPage > 1) {
            currentPages[index] = currentPage - 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault();
        let index = Number($(this).attr('index'));
        currentPages[index] = 1;
        renderTable(index);
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault();
        let index = Number($(this).attr('index'));
        currentPages[index] = totalPagesOb[index];
        renderTable(index);
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault();
        let index = Number($(this).attr('index'));
        let currentPage = currentPages[index];
        if (Number(currentPage) < Number(totalPagesOb[index])) {
            currentPages[index] = currentPage + 1;
            renderTable(index);
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let index = Number($(this).attr('index'));
        currentPages[index] = e.currentTarget.value;
        renderTable(index);
    });

    $(document).on('click', '.close-coupon', function (e) {
        let conf = confirm('Bạn chắc chắn muốn tắt mã coupon này?');
        if (conf) {
            let id = $(this).attr('id');
            get(`/store/change-coupon-status.html/${id}/0`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                    loadInactive();
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.open-coupon', function (e) {
        let conf = confirm('Bạn chắc chắn muốn mở mã coupon này?');
        if (conf) {
            let id = $(this).attr('id');
            get(`/store/change-coupon-status.html/${id}/1`, {}, (response) => {
                if (!response.error) {
                    $('#' + id).remove();
                    loadActives();
                } else {
                    displayError(response.message);
                }
            })
        }
    });
})

function loadActives() {
    get('/store/get-coupon-status.html/1', {}, res => {
        coupons[1] = res.data.coupons;
        totalPagesOb[1] = Math.ceil(coupons[1].length / itemsPerPage)
        renderTable(1);
    })
}

function loadInactive() {
    get('/store/get-coupon-status.html/0', {}, res => {
        coupons[2] = res.data.coupons;
        totalPagesOb[2] = Math.ceil(coupons[2].length / itemsPerPage)
        renderTable(2);
    })
}


$(function () {
    loadActives();
    loadInactive();
})
