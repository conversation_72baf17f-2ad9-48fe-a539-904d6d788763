var myDropzone;

$(() => {
    setup("upload-images");

    $('#form-add-product').on('submit', (e) => {
        e.preventDefault();
        let data = $('#form-add-product').serializeArray();
        let formData = new FormData();

        // for (let i = 1; i <= 3; i++) {
        //     if ($(`#upload-product-${i}`).prop('files')[0]) {
        //         formData.append(`pictures`, $(`#upload-product-${i}`).prop('files')[0])
        //     }
        // }

        formData.append(`excelFile`, $(`#upload-file`).prop('files')[0])
        ajaxFile('/store/import-san-pham.html', formData, (res) => {
            $.unblockUI();
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    location.href = '/store/mng-products.html'
                }, 1000)
            } else {
                displayError(res.message);
            }
        })
    })

    /**
     * Chọn cửa hàng thì lấy tất cả cơ sở cửa hàng đó
     */
    $('select[name="branchId"]').select2({
        multiple: true,
        // allowClear: true,
        theme: "bootstrap4",
        width: 'resolve',
        placeholder: '[[---------Chọn cơ sở --------]]',
        data: []
    });
    $('select[name="storeId"]').on('change', function (event) {
        const selectedStoreId = event.target.value;
        $.ajax({
            url: `/rest/v1/branches?storeId=${selectedStoreId}`,
            data: {},
            type: 'get',
            success: function (response) {
                $('select[name="branchId"]').select2({
                    multiple: true,
                    allowClear: true,
                    theme: "bootstrap4",
                    width: 'resolve',
                    placeholder: '[[---------Chọn cơ sở --------]]',
                    data: response.branches.map(branch => ({id: branch._id, text: branch.name})),
                }).val(response.branches.map(branch => branch._id)).trigger('change')
            },
            error: function (err) {

            }
        })
    })
})


Dropzone.autoDiscover = false;

function setup(id) {

    myDropzone = new Dropzone(`#${id}`, {
        url: '/upload',
        parallelUploads: 2,
        filesizeBase: 1000,
        thumbnailHeight: 210,
        thumbnailWidth: 140,
        maxFilesize: 1024 * 3,
        maxFiles: 9999,
        dictResponseError: "Server not Configured",
        dictFileTooBig: "Kích thước file quá lớn ({{filesize}}MB). Cần nhỏ hơn {{maxFilesize}}MB.",
        dictCancelUpload: "",
        acceptedFiles: ".png,.jpg,.jpeg",
        previewTemplate: `<div class="dz-preview dz-file-preview m-2">
                                    <div class="dz-image"><img data-dz-thumbnail /></div>
                                    <div class="dz-error-message"><i class="fa fa-warning">&nbsp;</i><span data-dz-errormessage></span></div>
                                    <div class="dz-filename"><span data-dz-name></span></div>
                                    <div class="dz-progress">
                                        <span class="dz-upload" data-dz-uploadprogress></span>
                                    </div>
                                    <div class="dz-remove">
                                        <a href="javascript:undefined;" data-dz-remove=""><i class="fa fa-trash-o"></i>&nbsp;<span>Xóa</span></div>
                                </div>`,
        thumbnail: function (file, dataUrl) {
            if (file.previewElement) {
                file.previewElement.classList.remove("dz-file-preview");
                var images = file.previewElement.querySelectorAll("[data-dz-thumbnail]");
                for (var i = 0; i < images.length; i++) {
                    var thumbnailElement = images[i];
                    thumbnailElement.alt = file.name;
                    thumbnailElement.src = dataUrl;
                }
                setTimeout(function () {
                    file.previewElement.classList.add("dz-image-preview");
                }, 1);
            }
        }

    });


    // Now fake the file upload, since GitHub does not handle file uploads
    // and returns a 404

    var minSteps = 6,
        maxSteps = 60,
        timeBetweenSteps = 100,
        bytesPerStep = 100000;

    myDropzone.uploadFiles = function (files) {
        var self = this;

        for (var i = 0; i < files.length; i++) {

            var file = files[i];
            totalSteps = Math.round(Math.min(maxSteps, Math.max(minSteps, file.size / bytesPerStep)));

            for (var step = 0; step < totalSteps; step++) {
                var duration = timeBetweenSteps * (step + 1);
                setTimeout(function (file, totalSteps, step) {
                    return function () {
                        file.upload = {
                            progress: 100 * (step + 1) / totalSteps,
                            total: file.size,
                            bytesSent: (step + 1) * file.size / totalSteps
                        };

                        self.emit('uploadprogress', file, file.upload.progress, file.upload.bytesSent);
                        if (file.upload.progress == 100) {
                            file.status = Dropzone.SUCCESS;
                            self.emit("success", file, 'success', null);
                            self.emit("complete", file);
                            self.processQueue();
                            //document.getElementsByClassName("dz-success-mark").style.opacity = "1";
                        }
                    };
                }(file, totalSteps, step), duration);
            }
        }
    }

    return myDropzone;
}

function submitImageForm() {
    if (!myDropzone || !myDropzone.files) return;
    const formData = new FormData();
    myDropzone.files.forEach(file => {
        delete file.previewElement;
        delete file.previewTemplate;
        delete file.status;
        delete file.upload;
        delete file.width;
        delete file.height;
        delete file.accepted;
        delete file.processing;
        formData.append(`pictures`, file)
    });

    ajaxFile('/rest/v1/upload-centralize', formData, (res) => {
        if (!res.error) {
            displaySuccess(res.message);
            if (res.data && res.data.pictures) {
                // const links = res.data.pictures.map(link => window.location.origin + link);
                $('#images-link-area').html(res.data.pictures.join(';')).removeClass('d-none');
            }
            $(".dz-preview").remove();
            myDropzone.files = [];
        } else {
            displayError(res.message);
            $('#images-link-area').removeClass('d-none').addClass('d-none');
        }
    })
}

function submitForm() {
    $.blockUI({
        css: {
            backgroundColor: '#0fe218',
            color: '#fff',
            padding: '20px',
            'border-radius': '8px',
            border: 'none'
        }, message: 'Vui lòng chờ...'
    });
    $('#form-add-product').submit()
}
