let months = [1,2,3,4,5,6,7,8,9,10,11,12]
let formatDay = {
    0:'CN',
    1:'T2',
    2:'T3',
    3:'T4',
    4:'T5',
    5:'T6',
    6:'T7'
}
let statusTable = 'day'
let timeDay = null
function getDateTime(){
    return new Date(timeDay[0],timeDay[1],timeDay[2]).getTime()
}
function changeDay(time){
    timeDay = [new Date(time).getFullYear(), new Date(time).getMonth(), new Date(time).getDate()]
}
changeDay(new Date().getTime())

function changeClassTable(key){
    $(`.choose_datetime li`).removeClass('active')
    $(`.choose_datetime li.${key}`).addClass('active')
    $('.table_calendar').removeClass('day week month year')
    $('.table_calendar').addClass(key)
    statusTable = key
}

function renderTimeSelect(){
    $('.title_box .day-select').text(`Ngày ${timeDay[2]}, Tháng ${timeDay[1]+1}, ${timeDay[0]} `)
}
renderTimeSelect()

function renderWorkToDay(){
    let time = new Date().setHours(0,0,0,0)
    let lisData = []
    bookExams.forEach(item=>{
        if (item.timeCheckIn>=time && item.timeCheckIn < time+24*60*60*1000){
            lisData.push(item)
        }
    })
    let html=''
    lisData.sort((a,b) => (a.timeCheckIn > b.timeCheckIn) ? 1 : ((b.timeCheckIn > a.timeCheckIn) ? -1 : 0));
    lisData.forEach(item=>{
        html+=`
            <li class="customer_medical">
                <a style="display: block;width: 100%;" target="_blank" href="/store/chi-tiet-lich-hen/${item._id}.html?index=1">
                    <div class="image_customer" style="float: left;">
                        <img style="border-radius: 50%;" src="${item.avatarUser}" alt="">
                    </div>
                    <div class="info_customer">
                        <div class="name">
                            ${item.userName}
                        </div>
                        <p class="time">${moment(Number(item.timeCheckIn)).format('HH:mm DD/MM/YYYY')}</p>
                    </div>
                </a>
            </li>
        `
    })
    $('.medical_schedule .list_medical').html(html)
}
renderWorkToDay()

function renderTimePerDay(){
    let keyData = {}
    let timeFirst= getDateTime()
    let timeEnd= timeFirst+24*60*60*1000
    bookExams.forEach(item=>{
        if (item.timeCheckIn>=timeFirst && item.timeCheckIn <timeEnd){
            let hour = new Date(item.timeCheckIn).getHours()
            if (!keyData[hour]){
                keyData[hour]=[]
            }
            keyData[hour].push(item)
        }
    })
    let html=''
    for (let i=0; i<=23; i++){
        let keyTime = ''
        i<10? keyTime=`0${i}:00` : keyTime=`${i}:00`
        let htmlItem=''
        if (keyData[i]){
            keyData[i].forEach(item=>{
                htmlItem+=`
                <a target="_blank" href="/store/chi-tiet-lich-hen/${item._id}.html?index=1">
                    <div class="schedule_item ${item.status== 3 ? 'done-work' : 'wait-work'}">
                        <div class="name">
                            ${item.userName}
                        </div>
                        <div class="date_time">
                            <span>Ngày khám: ${moment(Number(item.timeCheckIn)).format('HH:mm DD/MM/YYYY')}</span> <span
                                class="pet">Loại: ${item.typePet == 0 ? 'Ô tô' : item.typePet == 1 ? 'Xe máy' : 'Phương tiện khác'}</span>
                        </div>
                    </div>
                </a>
                `
            })
            htmlItem+=`
            `
        }
        html+=`
        <tr>
            <td>
                <div class="oclock">
                    ${keyTime}
                </div>
            </td>
            <td>
            ${htmlItem}
            </td>
        </tr>
        `
    }
    $('.table_calendar thead tr').html(`
    <th></th>
        <th>
            <p class="day_week">${formatDay[new Date(getDateTime()).getDay()]}</p>
            <p class="daytime">${timeDay[2]<10? `0${timeDay[2]}` : timeDay[2]}</p>
        </th>
    `)
    $('.table_calendar tbody').html(html)
    changeClassTable('day')
}

function renderTimePerWeek(){
    let keyData = {}
    let numberDay = new Date(getDateTime()).getDay()
    let timeFirst, timeEnd
    if (numberDay !=0){
        timeFirst = getDateTime()-(numberDay-1)*24*60*60*1000
        timeEnd = timeFirst + 7*24*60*60*1000
    } else {
        timeEnd = getDateTime()
        timeFirst = getDateTime() - 7*24*60*60*1000
    }
    bookExams.forEach(item=>{
        if (item.timeCheckIn>=timeFirst && item.timeCheckIn < timeEnd){
            let day = Math.ceil((item.timeCheckIn+1-timeFirst)/(24*60*60*1000))
            let hour = new Date(item.timeCheckIn).getHours()

            if (!keyData[`${day}-${hour}`]){
                keyData[`${day}-${hour}`]=[]
            }
            keyData[`${day}-${hour}`].push(item)
        }
    })
    let htmlThead = '<th></th>'
    for (let i=0;i<7;i++){
        let time = timeFirst + i*24*60*60*1000
        htmlThead+=`
            <th>
                <p class="day_week">${formatDay[new Date(time).getDay()]}</p>
                <p class="daytime">${new Date(time).getDate()<10? `0${new Date(time).getDate()}` : new Date(time).getDate()}</p>
            </th>
        `
    }
    let html=''
    for (let i=0; i<=23; i++){
        let keyTime = ''
        i<10? keyTime=`0${i}:00` : keyTime=`${i}:00`
        html+=`<tr>
                <td>
                    <div class="oclock">
                        ${keyTime}
                    </div>
                </td>`
        for (let j=1;j<=7;j++){
            let htmlItem=''
            if (keyData[`${j}-${i}`]){
                keyData[`${j}-${i}`].forEach(item=>{
                    htmlItem+=`
                    <a target="_blank" href="/store/chi-tiet-lich-hen/${item._id}.html?index=1">
                        <div class="schedule_item ${item.status== 3 ? 'done-work' : 'wait-work'}">
                            <div class="name">
                                ${item.userName}
                            </div>
                        </div>
                    </a>
                    `
                })
            }
            html+=`
                <td>
                    ${htmlItem}
                </td>
            `
        }
        html+=`</tr>`
    }
    $('.table_calendar thead tr').html(htmlThead)
    $('.table_calendar tbody').html(html)
    changeClassTable('week')
}

function renderTimePerMonth(){
    let keyData = {}
    let countDay = new Date(timeDay[0],timeDay[1]+1,0).getDate()
    let timeDate = new Date(getDateTime()).getDate()
    let timeFirst, timeEnd
    timeFirst = getDateTime()-(timeDate-1)*24*60*60*1000
    timeEnd = timeFirst + countDay*24*60*60*1000

    bookExams.forEach(item=>{
        if (item.timeCheckIn>=timeFirst && item.timeCheckIn < timeEnd){
            let day = Math.ceil((item.timeCheckIn+1-timeFirst)/(24*60*60*1000))-1
            if (!keyData[day]){
                keyData[day]=[]
            }
            keyData[day].push(item)
        }
    })
    let max = countDay + (6-new Date(timeEnd-1).getDay())
    let min = 0-(new Date(timeFirst).getDay())
    let htmlThead = ''
    for (let i=0;i<=6;i++){
        htmlThead+=`
            <th>
                <div class="day_week">
                    ${formatDay[i]}
                </div>
            </th>
        `
    }
    let html=''
    let i=min
    while (i<max){
        html+=`<tr>`
        for (let j=0;j<7;j++){
            let time = timeFirst + i*24*60*60*1000
            html+=`<td>
                    <div class="numb_day ${i<0? 'not_month':i>=countDay? 'not_month':'' }">
                        ${new Date(time).getDate()<10? `0${new Date(time).getDate()}` : new Date(time).getDate()}
                    </div>
                `
            if (keyData[i]){
                keyData[i].forEach(item=>{
                    html+=`
                    <a target="_blank" href="/store/chi-tiet-lich-hen/${item._id}.html?index=1">
                        <div class="schedule_item ${item.status== 3 ? 'done-work' : 'wait-work'}">
                            <div class="name">
                                ${item.userName}
                            </div>
                        </div>
                    </a>
                    `
                })
            }
            html+=`</td>`
            i++
        }
        html+=`</tr>`
    }
    $('.table_calendar thead tr').html(htmlThead)
    $('.table_calendar tbody').html(html)
    changeClassTable('month')
}

function renderTimePerYear(){
    let keyData = {}
    let countDay = new Date(timeDay[0],timeDay[1]+1,0).getDate()
    let timeDate = new Date(getDateTime()).getDate()
    let timeFirst, timeEnd
    timeFirst = new Date(timeDay[0],0,1).getTime()
    timeEnd = new Date(timeDay[0],11,31).getTime() +24*60*60*1000

    bookExams.forEach(item=>{
        if (item.timeCheckIn>=timeFirst && item.timeCheckIn < timeEnd){
            let month = new Date(item.timeCheckIn).getMonth()+1
            if (!keyData[month]){
                keyData[month]=[]
            }
            keyData[month].push(item)
        }
    })
    let html=''
    let i=1
    while (i<=12){
        html+=`<tr>`
        for (let j=0;j<=2;j++){
            let time = timeFirst + i*24*60*60*1000
            html+=`<td>
                        <div class="numb_day">
                            Tháng ${i}
                        </div>
                `
            if (keyData[i]){
                keyData[i].forEach(item=>{
                    html+=`
                    <a target="_blank" href="/store/chi-tiet-lich-hen/${item._id}.html?index=1">
                        <div class="schedule_item ${item.status== 3 ? 'done-work' : 'wait-work'}">
                            <div class="name">
                                ${item.userName}
                            </div>
                        </div>
                    </a>
                    `
                })
            }
            html+=`</td>`
            i++
        }
        html+=`</tr>`
    }
    $('.table_calendar thead tr').html('')
    $('.table_calendar tbody').html(html)
    changeClassTable('month year')
}

renderTimePerDay()
$(document).on('click', '.prev', function (e) {
    if (statusTable=='day'){
        changeDay(getDateTime()-24*60*60*1000)
        renderTimePerDay()
    } else if (statusTable=='week'){
        changeDay(getDateTime()-7*24*60*60*1000)
        renderTimePerWeek()
    } else if (statusTable=='month'){
        if (timeDay[1]==0){
            timeDay[1]=11
            timeDay[0]--
        } else {
            timeDay[1]--
        }
        changeDay(new Date(timeDay[0],timeDay[1],1).getTime())
        renderTimePerMonth()
    } else {
        changeDay(new Date(timeDay[0]-1,0,1).getTime())
        renderTimePerYear()
    }
    renderTimeSelect()
});
$(document).on('click', '.next', function (e) {
    if (statusTable=='day'){
        changeDay(getDateTime()+24*60*60*1000)
        renderTimePerDay()
    } else if (statusTable=='week'){
        changeDay(getDateTime()+7*24*60*60*1000)
        renderTimePerWeek()
    } else if (statusTable=='month'){
        if (timeDay[1]==11){
            timeDay[1]=0
            timeDay[0]++
        } else {
            timeDay[1]++
        }
        changeDay(new Date(timeDay[0],timeDay[1],1).getTime())
        renderTimePerMonth()
    } else {
        changeDay(new Date(timeDay[0]+1,0,1).getTime())
        renderTimePerYear()
    }
    renderTimeSelect()

});
