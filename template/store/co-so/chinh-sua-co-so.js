$(() => {


    function renderListCity() {
        let html = '<option value="">Chọn Tỉnh / Thành phố</option>'
        for (let key in countrys) {
            html += `
    <option value="${key}">${key}</option>
    `
        }
        $('#city-state').html(html);

        let valDefault = $('#city-state').attr('value');
        if (valDefault) $('#city-state').val(valDefault).change();
    }

    $(document).on('change', '#city-state', function (e) {
        let city = $(this).val()
        let html = '<option value="">Chọn Quận / Huyện</option>'
        for (let key in countrys[city]) {
            html += `
            <option value="${key}">${key}</option>
            `
        }
        $('#district-state').html(html);

        let valDefault = $('#district-state').attr('value');
        if (valDefault) $('#district-state').val(valDefault).change();
    });

    $(document).on('change', '#district-state', function (e) {
        let huyen = $(this).val()
        let html = '<option value="">Chọn Phường / Xã</option>'
        let city = $('#city-state').val()
        if (city && huyen && countrys[city]) {
            countrys[city][huyen].forEach(item => {
                html += `
            <option value="${item}">${item}</option>
            `
            })
        }
        $('#ward-state').html(html);

        let valDefault = $('#ward-state').attr('value');
        if (valDefault) $('#ward-state').val(valDefault).change();
    });

    renderListCity()


    $("select[name='province'] option").filter(function() {
        //may want to use $.trim in here
        return $(this).text().indexOf(province) != -1;
    }).prop('selected', true);
    $('#city-state').trigger('change');

    $("select[name='district'] option").filter(function() {
        //may want to use $.trim in here
        return $(this).text().indexOf(district) != -1;
    }).prop('selected', true);
    $('#district-state').trigger('change');

    $("select[name='ward'] option").filter(function() {
        //may want to use $.trim in here
        return $(this).text().indexOf(ward) != -1;
    }).prop('selected', true);
    $('#ward-state').trigger('change');

    function placeToAddress(place){
        var address = {};
        place.address_components.forEach(function(c) {
            switch(c.types[0]){
                case 'street_number':
                    address.StreetNumber = c;
                    break;
                case 'route':
                    address.StreetName = c;
                    break;
                case 'neighborhood': case 'locality':    // North Hollywood or Los Angeles?
                    address.City = c;
                    break;
                case 'administrative_area_level_1':     //  Note some countries don't have states
                    address.State = c;
                    break;
                case 'administrative_area_level_2':     //  Note some countries don't have states
                    address.District = c;
                    break;
                case 'postal_code':
                    address.Zip = c;
                    break;
                case 'country':
                    address.Country = c;
                    break;
            }
        });

        return address;
    }

    $("#address").geocomplete({ country: ["vn"], types: ["geocode", "establishment"],  }).bind("geocode:result", function(event, result){
        console.log(result);
        $("input[name='lat']").val(result.geometry.location.lat())
        $("input[name='lng']").val(result.geometry.location.lng())

        let province = placeToAddress(result).State.short_name
        let district = placeToAddress(result).District.short_name

        $("select[name='province'] option").filter(function() {
            //may want to use $.trim in here
            return $(this).text().indexOf(province) != -1;
        }).prop('selected', true);
        $('#city-state').trigger('change');

        $("select[name='district'] option").filter(function() {
            //may want to use $.trim in here
            return $(this).text().indexOf(district) != -1;
        }).prop('selected', true);
        $('#district-state').trigger('change');

    });
    var editor = CKEDITOR.replace('content', {
        enterMode: CKEDITOR.ENTER_BR,
        height: 300
    });
    $('.upload-new-picture').on('change', function () {
        var that = this
        if ($(that).prop('files')[0]) {
            var reader = new FileReader();
            reader.onload = function (e) {
                $(that).parent().find('img').attr('src', e.target.result)
            }
            reader.readAsDataURL($(this).prop('files')[0]);
        }
    });

    $('#form-add-service').on('submit', (e) => {
        e.preventDefault();
        let formData = new FormData();

        let data = $('#form-add-service').serializeArray();
        data.forEach(item => {
            formData.append(item.name, item.value);
        });

        formData.append('content', editor.getData());
        ajaxFile(`/store/chinh-sua-co-so/${branchId}.html`, formData, (res) => {
            if (!res.error) {
                displaySuccess(res.message);
                setTimeout(() => {
                    window.location.href = '/store/co-so.html?storeId=' + res.data.storeId;
                }, 500)
            } else {
                displayError(res.message);
            }
        })
    })
})
