var serviceShows = services;
let currentPage = 1;
let itemsPerPage = 15;
let statusTable = 'all'
let typePet = 'all'
let totalPages = Math.ceil(serviceShows.length / itemsPerPage)

function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage - 1
    if (end > serviceShows.length - 1) {
        end = serviceShows.length
    }
    for (let i = start; i <= end; i++) {
        if (serviceShows[i]) {
            let service = serviceShows[i]
            service.codeMP =  createCuaHang(service.code)
            let button=''
            if (service.status == 1) {
                button = `<a href="javascript:;" class="delete-service" title="Xoá">
                <img src="/template/ui/img/delete.svg" alt=""></a>`

                button += `<a href="javascript:;" class="hide-service" title="Ẩn">
            <img src="/template/ui/img/hide.svg" alt="">`
            } else {
                button = `<a href="javascript:;" class="show-service" title="Hiển thị">
                    <img src="/template/ui/img/check.svg" alt="">
                </a>`
            }
            html += `
            <tr id="${service._id}">
                <td>${i + 1}</td>
                <td>${service.codeMP}</td>
                <td>${service.name}</td>
                <td>${service.address}</td>
                <td>${service.status == 1 ?'Hiển thị' : 'Ẩn'}</td>
                <td>
                <a class="btn-co-so" href="/store/co-so.html?storeId=${service._id}" title="Xem cơ sở">Xem cơ sở</a>
                <a href="/store/chinh-sua-thuong-hieu/${service._id}.html" class="edit-service" title="Chỉnh sửa">
                    <img src="/template/ui/img/edit.svg" alt="">
                </a>
                    ${button}
                </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${serviceShows.length} kết quả`)
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function successDoneScheduleViewBox() {
    $('#successDoneScheduleViewBox').addClass('open');
    $('body').prepend('<div class="modal-backdrop fade show"></div>');
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    currentPage = 1;
    serviceShows = [];
    let data1s = [];
    typePet = $('#type-pet').val()
    if (typePet == 'all') {
        data1s = services
    } else {
        services.forEach(service => {
            if (service.typePet == typePet) {
                data1s.push(service)
            }
        })
    }
    let key = removeUtf8($('#search-goods').val().trim());
    let data3s = []
    data1s.forEach(item => {
        if (removeUtf8(item.name).includes(key) || removeUtf8(item.address.toString()).includes(key) || removeUtf8(item.codeMP.toString()).includes(key)) {
            data3s.push(item)
        }
    });
    serviceShows = data3s;
    totalPages = Math.ceil(serviceShows.length / itemsPerPage);
    renderTable()
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            renderTable();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        renderTable();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault();
        currentPage = totalPages;
        renderTable();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            renderTable();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        currentPage = e.currentTarget.value;
        renderTable();
    });


    $(document).on('click', '.delete-service', function (e) {
        let conf = confirm('Bạn có chắc muốn xoá?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/delete-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });


    $(document).on('click', '.hide-service', function (e) {
        let conf = confirm('Bạn có chắc muốn ẩn?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/hide-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });


    $(document).on('click', '.show-service', function (e) {
        let conf = confirm('Bạn có chắc muốn hiển thị?');
        let id = $(this).parents('tr').attr('id');
        if (conf) {
            get(`/store/show-service/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    renderTable();

    if (checkBank == 0) {
        successDoneScheduleViewBox()
    }

    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-shop-trung-tam.html`, {
            services,
            indexTable:typeServices+1,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });
});

