var bills, billShows
let currentPage = 1;
let itemsPerPage = 10;
let categoryId = 'all'
let statusTable = 'all'
let statusPayments = 'all'
let totalPages = 1
let countBill = 0
$(function () {
    $('.dateTimeHandle').datetimepicker({
        i18n: {
            de: {
                months: [
                    'Tha.1', 'Tha.2', 'Tha.3', 'Tha.4',
                    'Tha.5', 'Tha.6', 'Tha.7', 'Tha.8',
                    'Tha.9', 'Tha.10', 'Tha.11', 'Tha.12',
                ],
                dayOfWeek: [
                    "CN", "Thu.2", "Thu.3", "Thu.4",
                    "Thu.5", "Thu.6", "Thu.7",
                ]
            }
        },
        format: 'H:i, d/m/Y',
        scrollMonth : false,
        scrollInput : false,
        scrollTime:false

    });
})

function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage
    if (end > countBill) {
        end = end
    }
    for (let i = 0; i <= billShows.length-1; i++) {
        if (billShows[i]) {
            let bill = billShows[i]
            html += `
            <tr id="${bill._id}">
                <td>${(currentPage - 1) * 10 + i + 1}</td>
                <td><a href="#">${bill.orderId}</a></td>
                <td>${bill.listProduct.split('\n').join('<br>')}</td>
                <td>${bill.userName}</td>
                <td>${moment(Number(bill.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                <td>${numberFormat(bill.totalMoney)} VNĐ</td>
                <td>${bill.paymentMethod == 0 ? 'COD' : bill.paymentMethod == 1 ? 'Online' : bill.payments == 2 ? 'Điểm' : '-'}${bill.isPayOnline == 1 ? '-Đã thanh toán' : '-Chưa thanh toán'}</td>
                <td class="status"><span class="${bill.class}">${bill.statusText}</span></td>
                <td>
                    <a href="/store/chinh-sua-don-hang/${bill._id}.html" class="edit-bill" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${countBill} kết quả`);
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    let search = $(`#search-goods`).val().trim()
    let statusTable = $(`#status-table`).val()
    let statusPayments = $(`#status-payments`).val()
    function formatTime(time){
        time = time.split(',')
        let timeDate = time[1].trim().split('/')
        time[1] = timeDate[1] + '/' + timeDate[0] +'/'+timeDate[2]
        return new Date(time.join(' ')).getTime()
    }
    let timeStart = formatTime($(`#time-start-1`).val())
    let timeEnd = formatTime($(`#time-end-1`).val())
    post(`/store/lay-danh-sach-don-hang.html`, {
        page:currentPage,
        search,
        status:statusTable,
        payments:statusPayments,
        timeStart,
        timeEnd
    }, (res) => {
        if (!res.error) {
            bills = res.data.bills
            billShows = bills
            countBill = res.data.countBill
            totalPages = Math.ceil(res.data.countBill / itemsPerPage)
            renderTable()
        } else {
            displayError(res.message);
        }
    })
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            filterList();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        filterList();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        currentPage = totalPages;
        filterList();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            filterList();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let page = e.currentTarget.value;
        currentPage = page;
        filterList();
    });
    $(document).on('click', '.export_excel', function (e) {
        e.preventDefault();
        post(`/parse-file-don-hang.html`, {
            bills: billShows,
            indexService:1,
        }, res => {
            var element = document.createElement('a');
            element.setAttribute('href', res.data.path);
            element.setAttribute('target', '_blank');
            element.style.display = 'none';
            document.body.appendChild(element);
            element.click();
            document.body.removeChild(element);
        })
    });

    filterList()
})

