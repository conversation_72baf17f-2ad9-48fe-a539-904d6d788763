$(document).ready(function () {
    get_image_dropbox();
})

function getDropboxImage(path) {
    return new Promise(function (resolve,reject) {
        const ACCESS_TOKEN = '7_BaFnIPSNQAAAAAAAAAAStcgYyr1ZZlau43sMaRYiwb2XWA8VDQPYXBNLVznyii';

        const arr_str = path.split("files/");
        path = '/files/' + arr_str[1];

        const dbx = new Dropbox.Dropbox({accessToken: ACCESS_TOKEN});

        dbx.filesDownload({path: path}).then(function (data) {
            if (data.status == '200') {
                const fileBlob = data.result.fileBlob;
                var reader = new FileReader();
                reader.readAsDataURL(fileBlob);
                reader.onloadend = function () {
                    var base64data = reader.result;
                    // console.log('Data', base64data);
                    resolve(base64data);
                }
            } else {
                resolve(data);
            }
        }).catch(err => resolve(err));
    });

}

function get_image_dropbox() {

    $('img.img-dbx').each(function () {
        const ACCESS_TOKEN = '7_BaFnIPSNQAAAAAAAAAAStcgYyr1ZZlau43sMaRYiwb2XWA8VDQPYXBNLVznyii';
        let img = $(this);
        let data_url = img.data('url');

        let default_image = '/template/ui/img/upload.png'; // Default image

        if (data_url === '') {
            img.attr('src',default_image);
        } else {
            const arr_str = data_url.split("files/");
            data_url = '/files/' + arr_str[1];
            const dbx = new Dropbox.Dropbox({accessToken: ACCESS_TOKEN});

            dbx.filesDownload({path: data_url}).then(function (data) {

                if (data.status == '200') {
                    const fileBlob = data.result.fileBlob;
                    var reader = new FileReader();
                    reader.readAsDataURL(fileBlob);
                    reader.onloadend = function () {
                        var base64data = reader.result;
                        img.attr('src',base64data);
                    }
                } else {
                    img.attr('src',default_image);
                }
            });
        }
    });
}
