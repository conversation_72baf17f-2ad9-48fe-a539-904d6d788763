# Express Backend Environment Variables

# Application
NODE_ENV=production
PORT=4000

# Database - MongoDB
MONGODB_URI=mongodb://mongo:27017/lbvd_db
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=admin_password

# Cache - Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
SESSION_SECRET=your-super-secret-session-key-here

# Email Service - SendGrid
SENDGRID_API_KEY=your-sendgrid-api-key-here

# Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY=your-firebase-private-key
FIREBASE_CLIENT_EMAIL=your-firebase-client-email

# Google Cloud
GOOGLE_CLOUD_PROJECT_ID=your-google-cloud-project-id

# MongoDB Express (Development only)
MONGO_EXPRESS_USER=admin
MONGO_EXPRESS_PASSWORD=admin

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads

# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS
CORS_ORIGIN=http://localhost:4200,http://localhost:3000

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/app/logs/app.log
