{"info": {"_postman_id": "e4b48bd7-7491-4c5e-a36f-e49bae1db2f3", "name": "LBVD Products & Shopping Cart APIs", "description": "Collection of API endpoints for product and shopping cart related functionality", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Product APIs", "item": [{"name": "Get Products (Shopping)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api/shopping.html", "host": ["{{baseUrl}}"], "path": ["user", "api", "shopping.html"]}, "description": "Gets a list of products for the shopping page, including best-selling products, newest products, and product categories"}}, {"name": "Get Product Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api/chi-tiet-san-pham/:productId.html?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "chi-tiet-san-pham", ":productId.html"], "query": [{"key": "output", "value": "json", "description": "Response format"}], "variable": [{"key": "productId", "value": "", "description": "ID of the product to fetch"}]}, "description": "Gets detailed information about a specific product"}}, {"name": "Get Product Comments/Ratings", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api/product-rate/:productId?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "product-rate", ":productId"], "query": [{"key": "output", "value": "json", "description": "Response format"}], "variable": [{"key": "productId", "value": "", "description": "ID of the product"}]}, "description": "Gets ratings and comments for a specific product"}}, {"name": "Post Product Comment/Rating", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"productId\": \"{{productId}}\",\n    \"rate\": 5,\n    \"content\": \"Sản phẩm rất tốt\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/product-rate?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "product-rate"], "query": [{"key": "output", "value": "json", "description": "Response format"}]}, "description": "Adds a new comment/rating for a product"}}, {"name": "Get Products By Store", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api/get-products-by-storeid.html/:id", "host": ["{{baseUrl}}"], "path": ["user", "api", "get-products-by-storeid.html", ":id"], "variable": [{"key": "id", "value": "", "description": "ID of the store"}]}, "description": "Gets all products from a specific store"}}, {"name": "Search Products By Category", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/api/search-v2?search=&type=0&page=1&categoryId={{categoryId}}&order=&shipping=&price=&rate=&limit=10", "host": ["{{baseUrl}}"], "path": ["user", "api", "search-v2"], "query": [{"key": "search", "value": "", "description": "Search keyword"}, {"key": "type", "value": "0", "description": "Type of search (0 for products)"}, {"key": "page", "value": "1", "description": "Page number"}, {"key": "categoryId", "value": "{{categoryId}}", "description": "Category ID"}, {"key": "order", "value": "", "description": "Sort order"}, {"key": "shipping", "value": "", "description": "Shipping filter"}, {"key": "price", "value": "", "description": "Price filter"}, {"key": "rate", "value": "", "description": "Rating filter"}, {"key": "limit", "value": "10", "description": "Number of items per page"}]}, "description": "Search products with filters"}}]}, {"name": "Shopping Cart APIs", "item": [{"name": "Get Cart Information", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"shopProducts\": [\n        {\n            \"{{shopId}}\": {\n                \"products\": [\n                    {\n                        \"productId\": \"{{productId}}\",\n                        \"count\": 1,\n                        \"classifyActive\": null\n                    }\n                ]\n            }\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/tai-thong-tin-gio-hang.html?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "tai-thong-tin-gio-hang.html"], "query": [{"key": "output", "value": "json", "description": "Response format"}]}, "description": "Get information about products in the cart, including price updates and availability"}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"userId\": \"{{userId}}\",\n    \"name\": \"{{fullName}}\",\n    \"phone\": \"{{phone}}\",\n    \"province\": \"{{province}}\",\n    \"district\": \"{{district}}\",\n    \"ward\": \"{{ward}}\",\n    \"street\": \"{{street}}\",\n    \"paymentMethod\": 0,\n    \"totalAmount\": \"{{totalAmount}}\",\n    \"transportFee\": \"{{transportFee}}\",\n    \"items\": [\n        {\n            \"storeId\": \"{{shopId}}\",\n            \"totalPriceShop\": {{shopTotal}},\n            \"transportFee\": {{transportFee}},\n            \"totalWeight\": {{totalWeight}},\n            \"shippingService\": \"{{shippingService}}\",\n            \"products\": [\n                {\n                    \"productId\": \"{{productId}}\",\n                    \"name\": \"{{productName}}\",\n                    \"price\": {{productPrice}},\n                    \"count\": {{quantity}},\n                    \"classifyActive\": null\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/create-order-product?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "create-order-product"], "query": [{"key": "output", "value": "json", "description": "Response format"}]}, "description": "Create a new order with products"}}, {"name": "Calculate Shipping Fee", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"shopId\": \"{{shopId}}\",\n    \"toProvince\": \"{{province}}\",\n    \"toDistrict\": \"{{district}}\",\n    \"toWard\": \"{{ward}}\",\n    \"weight\": {{weight}}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/calculator-shipping?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "calculator-shipping"], "query": [{"key": "output", "value": "json", "description": "Response format"}]}, "description": "Calculate shipping fee based on shipping address and product details"}}, {"name": "Verify Coupon", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"code\": \"{{couponCode}}\",\n    \"subTotal\": {{subTotal}},\n    \"feeShip\": {{shippingFee}},\n    \"storeId\": \"{{shopId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/calculate-order?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "calculate-order"], "query": [{"key": "output", "value": "json", "description": "Response format"}]}, "description": "Verify coupon code and calculate discount"}}, {"name": "Order History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "url": {"raw": "{{baseUrl}}/user/api/lay-lich-su-mua-hang/?page=1&limit=10&output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "lay-lich-su-mua-hang", ""], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Number of items per page"}, {"key": "output", "value": "json", "description": "Response format"}]}, "description": "Get user's product order history"}}, {"name": "Order Details", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "url": {"raw": "{{baseUrl}}/user/api/order-product-detail/:orderId?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "order-product-detail", ":orderId"], "query": [{"key": "output", "value": "json", "description": "Response format"}], "variable": [{"key": "orderId", "value": "", "description": "Order ID"}]}, "description": "Get detailed information about a specific order"}}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "body": {"mode": "raw", "raw": "{\n    \"reasonCancel\": \"Reason for cancellation\",\n    \"noteCancel\": \"Additional notes\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/user/api/huy-don-hang.html/:id?output=json", "host": ["{{baseUrl}}"], "path": ["user", "api", "huy-don-hang.html", ":id"], "query": [{"key": "output", "value": "json", "description": "Response format"}], "variable": [{"key": "id", "value": "", "description": "Order ID"}]}, "description": "Cancel an order"}}, {"name": "Get Shipping Info", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{accessToken}}", "description": "JWT token for authorization"}], "url": {"raw": "{{baseUrl}}/user/api/get-shipping-info/:orderId", "host": ["{{baseUrl}}"], "path": ["user", "api", "get-shipping-info", ":orderId"], "variable": [{"key": "orderId", "value": "", "description": "Order ID"}]}, "description": "Get shipping information for a specific order"}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://your-api-base-url", "description": "Base URL for API endpoints", "type": "string"}, {"key": "accessToken", "value": "your-access-token", "description": "JWT token for authentication", "type": "string"}, {"key": "userId", "value": "", "description": "Current user's ID", "type": "string"}]}